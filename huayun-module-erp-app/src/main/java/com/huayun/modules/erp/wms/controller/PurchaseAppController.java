package com.huayun.modules.erp.wms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.erp.wms.constant.enums.receipt.ReceiptTypeEnum;
import com.huayun.modules.erp.wms.dao.IReceiptItemDao;
import com.huayun.modules.erp.wms.entity.Receipt;
import com.huayun.modules.erp.wms.entity.ReceiptItem;
import com.huayun.modules.erp.wms.entity.model.LotModel;
import com.huayun.modules.erp.wms.entity.model.ReceiptModel;
import com.huayun.modules.erp.wms.factory.ReceiptFactory;
import com.huayun.modules.erp.wms.query.PurchaseAppQuery;
import com.huayun.modules.erp.wms.query.ReceiptQuery;
import com.huayun.modules.erp.wms.service.receipt.ILotService;
import com.huayun.modules.erp.wms.service.receipt.IReceiptService;
import com.huayun.modules.erp.wms.vo.ListPurchaseAppVO;
import com.huayun.modules.erp.wms.vo.PurchaseAppVO;
import com.huayun.modules.erp.wms.vo.ReceiptLotVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;

/**
 * 采购
 *
 * <AUTHOR>
 * @date 2022/11/15 16:10
 **/
@Slf4j
@Api(tags="APP-WMS-采购管理")
@RestController
@RequestMapping("/app/wms/purchase")
public class PurchaseAppController {

    private static final ReceiptTypeEnum TYPE = ReceiptTypeEnum.INCOMING_MATERIAL;

    @Resource
    private ReceiptFactory receiptFactory;
    @Resource
    private ILotService lotService;
    @Resource
    private IReceiptItemDao receiptItemService;

    @Resource
    private IReceiptService receiptService;

    /**
     * 分页列表查询
     *
     * @param query
     */
    @ApiOperation(value="分页查询", notes="分页查询")
    @GetMapping
    public Result<IPage<ListPurchaseAppVO>> queryPageList(PurchaseAppQuery query) {
        //分页条件
        Page<Receipt> page = new Page<>(query.getPage(), query.getPageSize());
        //查询条件
        ReceiptQuery receiptQuery = new ReceiptQuery();
        receiptQuery.setCode(query.getCode());
        if (ObjectUtils.isNotEmpty(query.getState())) {
            receiptQuery.setStates(Collections.singletonList(query.getState()));
        }
        receiptQuery.setCategoryName(query.getCategoryName());
        receiptQuery.setRelationCode(query.getRelationCode());
        receiptQuery.setSupplier(query.getSupplier());
        receiptQuery.setMaterialCode(query.getMaterials());
        receiptQuery.setSpecification(query.getSpecification());
        receiptQuery.setTypeList(Arrays.asList(ReceiptTypeEnum.PURCHASE, ReceiptTypeEnum.OUTSOURCE, ReceiptTypeEnum.OUTSOURCE_RETURN));
        receiptQuery.setOrderBy(query.getOrderBy());
        //分页查询
        IPage<ListPurchaseAppVO> pageList = receiptFactory.queryPage(TYPE, page, receiptQuery).convert(ListPurchaseAppVO::new);
        return Result.OK("查询成功！", pageList);
    }

    /**
     * 通过id查询
     *
     * @param id
     */
    @ApiOperation(value="单据详情", notes="单据详情")
    @GetMapping("/{id}")
    public Result<PurchaseAppVO> getReceipt(@PathVariable String id,
                                            @RequestParam(value = "allItem", required = false, defaultValue = "false") Boolean allItem) {
        ReceiptModel receipt = receiptFactory.getReceipt(id, allItem);

        return Result.OK("查询成功！", new PurchaseAppVO(receipt));
    }

    /**
     * 重置入库
     *
     * @param id
     */
    @ApiOperation(value="重置入库", notes="重置入库")
    @PutMapping("/{id}/reset")
    public Result<String> reset(@PathVariable String id){
        receiptFactory.reset(id);
        return Result.OK("重置入库成功！");
    }


    /**
     * 重置入库
     *
     * @param id
     */
    @ApiOperation(value="重置入库", notes="重置入库")
    @PutMapping("/{id}/reset/item")
    public Result<String> resetItem(@PathVariable String id){
        Receipt receipt = receiptService.getByItemId(id);
        receiptFactory.resetItem(id,receipt.getType());
        return Result.OK("重置入库成功！");
    }
    
    @ApiOperation("根据标签查询入库单详情")
    @GetMapping("/lot")
    public Result<ReceiptLotVo> getReceiptByLot(String lotNo) {
        LotModel lotModel = lotService.getLotDetailByLotNo(lotNo);
        BizAssert.isNotNull(lotModel, "标签不存在");
        ReceiptItem receiptItem = receiptItemService.getById(lotModel.getReceiptItemId());
        BizAssert.isNotNull(receiptItem, "入库单不存在");
        return Result.OK("查询成功！", new ReceiptLotVo(lotModel, receiptItem));
    }

}
