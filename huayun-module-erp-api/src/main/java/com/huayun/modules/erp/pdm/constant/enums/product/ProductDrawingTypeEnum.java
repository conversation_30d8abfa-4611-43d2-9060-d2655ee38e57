package com.huayun.modules.erp.pdm.constant.enums.product;


import com.huayun.modules.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @description 产品图纸类型枚举
 * @date 2025/4/7 16:42
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ProductDrawingTypeEnum implements BaseEnum {


    /**
     * 产品文件夹
     */
    PRODUCT_FOLDER("产品图纸", "PRODUCT_FOLDER"),


    /**
     * 图纸库文件夹
     */
    DRAWING_FOLDER("图纸库", "DRAWING_FOLDER");



    private String label;

    private String value;


}
