package com.huayun.modules.erp.pms.fallback.factory;

import com.huayun.modules.erp.pms.api.SupplierApi;
import com.huayun.modules.erp.pms.fallback.SupplierFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/1/5 16:11
 **/
@Component
public class SupplierFallbackFactory implements FallbackFactory<SupplierApi> {
    @Override
    public SupplierApi create(Throwable cause) {
        return new SupplierFallback(cause);
    }
}
