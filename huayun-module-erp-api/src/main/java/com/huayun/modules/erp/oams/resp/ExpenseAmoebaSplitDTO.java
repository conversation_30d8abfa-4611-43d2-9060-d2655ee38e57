/**  
 * Project Name:huayun-module-erp-api  
 * File Name:ExpenseAmoebaSplit.java  
 * Package Name:com.huayun.modules.erp.oams.resp  
 * Date:2023年6月13日上午9:31:39  
 * Copyright (c) 2023, <EMAIL> All Rights Reserved.  
 *  
*/  
/**  
 * Project Name:huayun-module-erp-api  
 * File Name:ExpenseAmoebaSplit.java  
 * Package Name:com.huayun.modules.erp.oams.resp  
 * Date:2023年6月13日上午9:31:39  
 * Copyright (c) 2023, <EMAIL> All Rights Reserved.  
 *  
 */  
  
package com.huayun.modules.erp.oams.resp;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**  
 * ClassName:ExpenseAmoebaSplit   
 * Function: TODO ADD FUNCTION.   
 * Reason:   TODO ADD REASON.   
 * Date:     2023年6月13日 上午9:31:39   
 * <AUTHOR>  
 * @version    
 * @since    version 1.0  
 * @see        
 */
@Getter
@Setter
@NoArgsConstructor
public class ExpenseAmoebaSplitDTO {
	/**部门id*/
	@Excel(name = "部门id", width = 15)
    @ApiModelProperty(value = "部门id")
	private java.lang.String organizationId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
	private java.lang.String remarks;
	/**分摊开始日期*/
	@Excel(name = "分摊开始日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "分摊开始日期")
	private java.util.Date startDate;
	/**分摊结束日期*/
	@Excel(name = "分摊结束日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "分摊结束日期")
	private java.util.Date endDate;
	/**自动计算的分摊费用*/
	@Excel(name = "自动计算的分摊费用", width = 15)
    @ApiModelProperty(value = "自动计算的分摊费用")
	private java.math.BigDecimal expenseAuto;
	/**手动输入的分摊费用 有手动取手动,没手动取自动*/
	@Excel(name = "手动输入的分摊费用 有手动取手动,没手动取自动", width = 15)
    @ApiModelProperty(value = "手动输入的分摊费用 有手动取手动,没手动取自动")
	private java.math.BigDecimal expenseHand;
	/**分摊的总工时*/
	@Excel(name = "分摊的总工时", width = 15)
    @ApiModelProperty(value = "分摊的总工时")
	private java.math.BigDecimal workHours;
	/**本部门的本期总的分摊费用*/
	@Excel(name = "本部门的本期总的分摊费用", width = 15)
    @ApiModelProperty(value = "本部门的本期总的分摊费用")
	private java.math.BigDecimal expenseAll;
}
  
