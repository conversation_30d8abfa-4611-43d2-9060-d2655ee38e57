package com.huayun.modules.erp.oams.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.oams.fallback.factory.IExpenseTypeApiFallbackFactory;
import com.huayun.modules.erp.oams.fallback.factory.ISelfCostingApiFallbackFactory;
import com.huayun.modules.erp.oams.query.ExpenseTypeQueryDTO;
import com.huayun.modules.erp.oams.query.SelfCostQuery;
import com.huayun.modules.erp.oams.resp.ExpenseTypeDTO;
import com.huayun.modules.erp.oams.vo.CostCollection;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.api.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@Api(tags = "费用类型-接口")
@FeignClient(value = "huayun-erp", contextId = "ISelfCostingApi", fallbackFactory = ISelfCostingApiFallbackFactory.class)
public interface ISelfCostingApi {
    @ApiOperation(value = "获取费用归集表")
    @GetMapping("/oams/costing/self/report/list")
    public Result<Page<CostCollection>> getReportList(@SpringQueryMap SelfCostQuery query);
}
