package com.huayun.modules.erp.wms.form;

import com.huayun.modules.common.rule.validate.ValidationGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
* 货位表单
*
* <AUTHOR>
* @date 2022/7/27
*/
@Data
public class LineLocationForm {

    /**id*/
    @ApiModelProperty(value = "id")
    @Null(message = "仓库货位id不可填",groups = ValidationGroup.Create.class)
    @NotBlank(message = "仓库货位id不可为空",groups = ValidationGroup.Update.class)
    private String id;

    /**货位编号*/
    @ApiModelProperty(value = "货位编号")
    @NotBlank(message = "货位编号不可为空")
    private String goodsAllocationCode;

    /**位置描述*/
    @ApiModelProperty(value = "位置描述")
    private String description;

    /**是否启用*/
    @ApiModelProperty(value = "是否启用")
    @NotNull(message = "请选择是否启用")
    private Boolean enable;
}
