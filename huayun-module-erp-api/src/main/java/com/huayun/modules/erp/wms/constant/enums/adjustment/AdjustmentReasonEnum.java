package com.huayun.modules.erp.wms.constant.enums.adjustment;

import com.huayun.modules.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存调整原因
 *
 * <AUTHOR>
 * @date 2022/11/2 16:15
 **/
@Getter
@AllArgsConstructor
public enum AdjustmentReasonEnum implements BaseEnum {

    /**
     * 盘点盈亏
     */
    PROFIT_LOSS("盘点盈亏","PROFIT_LOSS"),
    /**
     * 物料过期
     */
    EXPIRATION("物料过期","EXPIRATION"),
    /**
     * 库存损耗
     */
    DEPLETION("库存损耗","DEPLETION"),
    /**
     * 合并备料
     */
    MERGE_PICK("合并备料","MERGE_PICK");

    String label;
    String value;
}
