package com.huayun.modules.erp.oms.fallback;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.oms.api.IOrderApi;
import com.huayun.modules.erp.oms.dto.ListOrderQueryDTO;
import com.huayun.modules.erp.oms.dto.OrderDTO;
import com.huayun.modules.erp.oms.dto.OrderQueryDTO;
import com.huayun.modules.erp.oms.dto.OrderStorageDTO;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/1 18:33
 **/
@Slf4j
public class OrderFallback implements IOrderApi {

    private final Throwable cause;

    public OrderFallback(Throwable cause) {
        this.cause = cause;
    }

    @Override
    public Result<Page<OrderDTO>> queryPageList(ListOrderQueryDTO query) {
        log.error("获取订单分页列表接口调用失败： {}", cause);
        return Result.error("获取订单分页列表失败");
    }

    @Override
    public Result<List<OrderDTO>> findList(OrderQueryDTO orderQueryDTO) {
        log.error("获取订单列表接口调用失败： {}", cause);
        return Result.error("获取订单列表失败");
    }

    @Override
    public Result<List<OrderDTO>> findList2(OrderQueryDTO orderQueryDTO) {
        log.error("获取订单列表接口调用失败： {}", cause);
        return Result.error("获取订单列表失败");
    }

    @Override
    public Result<String> occurOrderStorage(List<OrderStorageDTO> orderStorages) {
        log.error("订单入库接口调用失败： {}", cause);
        return Result.error(cause.getMessage());
    }

    @Override
    public Result<OrderDTO> mergeOrdersIntoForecasts(List<String> orderIds) {
        log.error("合并客户订单接口调用失败： {}", cause);
        return Result.error(cause.getMessage());
    }
}
