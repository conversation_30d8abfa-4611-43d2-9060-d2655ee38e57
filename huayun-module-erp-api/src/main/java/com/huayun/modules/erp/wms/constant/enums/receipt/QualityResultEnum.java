package com.huayun.modules.erp.wms.constant.enums.receipt;

import com.huayun.modules.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum QualityResultEnum implements BaseEnum {

    /**
     * 通过
     */
    PASS("通过","PASS"),
    /**
     * 部分通过
     */
    PARTLY_PASS("部分通过","PARTLY_PASS"),
    /**
     * 不通过
     */
    FAIL("不通过","FAIL");

    String label;
    String value;
}
