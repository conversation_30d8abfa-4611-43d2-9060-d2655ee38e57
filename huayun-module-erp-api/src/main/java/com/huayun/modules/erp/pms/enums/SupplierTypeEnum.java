package com.huayun.modules.erp.pms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.huayun.modules.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SupplierTypeEnum  implements BaseEnum {
    /**
     * 供应商
     */
    PURCHASE("采购商", "PURCHASE"),
    /**
     * 服务商
     */
    SERVICE("服务商", "SERVICE"),
    /**
     * 委外商
     */
    OUTSOURCING("委外商", "OUTSOURCING");


    String label;
    @EnumValue
    String value;
}
