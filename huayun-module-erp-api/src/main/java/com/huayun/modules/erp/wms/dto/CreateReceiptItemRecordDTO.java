package com.huayun.modules.erp.wms.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 入库物料入库记录
 *
 * <AUTHOR>
 * @date 2022/9/26 15:39
 **/
@Data
public class CreateReceiptItemRecordDTO {

    /**批次号*/
    private String batchNo;

    /**物料标签号*/
    @NotBlank(message = "物料标签号不可为空")
    private String lotNo;
    
    /**生产日期*/
    private Date productionTime;

    /**送货数量*/
    @NotNull(message = "入库数量不可为空")
    @Positive(message = "入库数量必须为正数")
    private BigDecimal receiptQuantity;
}
