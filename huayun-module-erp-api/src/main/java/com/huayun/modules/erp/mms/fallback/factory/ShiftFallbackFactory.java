package com.huayun.modules.erp.mms.fallback.factory;

import com.huayun.modules.erp.mms.api.ShiftApi;
import com.huayun.modules.erp.mms.fallback.ShiftFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/21
 */
@Component
public class ShiftFallbackFactory implements FallbackFactory<ShiftApi> {
    @Override
    public ShiftApi create(Throwable cause) {
        return new ShiftFallback(cause);
    }
}
