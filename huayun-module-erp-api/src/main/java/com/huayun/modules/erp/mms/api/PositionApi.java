package com.huayun.modules.erp.mms.api;

import com.huayun.modules.erp.constant.ErpConstant;
import com.huayun.modules.erp.mms.api.query.PositionQuery;
import com.huayun.modules.erp.mms.dto.PositionDTO;
import com.huayun.modules.erp.mms.fallback.factory.TenantCalendarFallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.api.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/11/17:13
 */
@FeignClient(contextId = "PositionApi", value = ErpConstant.APP_NAME, fallbackFactory = TenantCalendarFallbackFactory.class)
public interface PositionApi {

    @ApiOperation(value = "岗位管理-列表查询", notes = "岗位管理-列表查询")
    @GetMapping("api/mms/positions")
    Result<List<PositionDTO>> getPositionList(@SpringQueryMap PositionQuery positionQuery);
}
