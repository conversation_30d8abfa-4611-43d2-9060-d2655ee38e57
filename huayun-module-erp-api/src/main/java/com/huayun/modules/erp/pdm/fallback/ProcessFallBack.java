package com.huayun.modules.erp.pdm.fallback;

import com.huayun.modules.erp.pdm.api.ProcessFlowApi;
import com.huayun.modules.erp.pdm.dto.process.ProcessDTO;
import com.huayun.modules.erp.pdm.dto.process.ProcessFlowDTO;
import com.huayun.modules.erp.pdm.dto.process.ProcessFlowSyncDTO;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;

/**
 * 工艺流程异常处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/26 17:37
 **/
@Slf4j
public class ProcessFallBack implements ProcessFlowApi {

    private final Throwable cause;

    public ProcessFallBack(Throwable cause) {
        this.cause = cause;
    }

    @Override
    public Result<ProcessFlowDTO> getProcessFlow(String productId) {
        log.error("Unable to create user. [Reason]: {} ", cause.getMessage());
        return Result.error(cause.getMessage());
    }

    @Override
    public Result<ProcessDTO> getProcessByNodeId(String processId) {
        log.error("Unable to create user. [Reason]: {} ", cause.getMessage());
        return Result.error(cause.getMessage());
    }

    @Override
    public Result<Boolean> syncProcessFlow(ProcessFlowSyncDTO dto) {
        log.error("sync error. [Reason]: {} ", cause.getMessage());
        return Result.error(cause.getMessage());
    }
}
