package com.huayun.modules.erp.qc.fallback.factory;

import com.huayun.modules.erp.qc.controller.IQcReportController;
import com.huayun.modules.erp.qc.fallback.QcReportFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class QcReportFallbackFactory implements FallbackFactory<IQcReportController> {
    @Override
    public IQcReportController create(Throwable cause) {
        return new QcReportFallback(cause);
    }
}
