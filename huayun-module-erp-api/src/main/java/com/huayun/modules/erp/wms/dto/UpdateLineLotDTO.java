package com.huayun.modules.erp.wms.dto;


import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;
import java.util.List;

/**
 * 线边仓物料标签
 *
 * <AUTHOR>
 * @date 2023/4/24 16:50
 **/
@Data
public class UpdateLineLotDTO {

    /**物料列表，用于扣减计划占用*/
    @Valid
    private List<LineMaterialItem> materialItems;

    /**标签列表*/
    @NotEmpty(message = "标签列表不可为空")
    @Valid
    private List<Item> items;
    
    
    private String code;

    @Data
    public static class LineMaterialItem{

        /**物料id*/
        @NotBlank(message = "物料id不可为空")
        private String materialId;

        /**物料编码*/
        @NotBlank(message = "物料编码不可为空")
        private String materialCode;

        /**占用库存*/
        @NotNull(message = "占用库存不可为空")
        private BigDecimal occupyInventory;

        @NotNull(message = "工序号")
        private String relationNumber;
    }

    @Data
    public static class Item{
        /**标签号*/
        @NotBlank(message = "标签号不可为空")
        private String lotNo;

        /**标签数量*/
        @PositiveOrZero(message = "标签数量必须为正数或者0")
        private BigDecimal quantity;
    }
}
