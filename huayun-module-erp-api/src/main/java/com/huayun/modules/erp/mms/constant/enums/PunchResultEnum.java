package com.huayun.modules.erp.mms.constant.enums;

import cn.hutool.core.util.ObjectUtil;
import com.huayun.modules.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum PunchResultEnum implements BaseEnum {

    /**
     * 正常
     */
    NORMAL("正常", "NORMAL"),
    /**
     * 缺卡
     */
    LACK("缺卡", "LACK"),
    /**
     * 迟到
     */
    LATE_ARRIVAL("迟到", "LATE_ARRIVAL"),
    /**
     * 早退
     */
    EARLY_LEAVE("早退", "EARLY_LEAVE");

    private final String label;
    private final String value;



    public static String getValueToLabel(String label){
        PunchResultEnum[] values = PunchResultEnum.values();
        if (ObjectUtil.isEmpty(label)){
            return null;
        }
        for (PunchResultEnum value : values) {
            if (value.getLabel().equals(label)){
                return value.getValue();
            }
        }
        return null;
    }


    /**
     * 根据value获取枚举
     * @param value
     * @return
     */
    public static PunchResultEnum getEnumToValue(String value){
        PunchResultEnum[] values = PunchResultEnum.values();
        if (ObjectUtil.isEmpty(value)){
            return null;
        }
        for (PunchResultEnum value1 : values) {
            if (value1.getValue().equals(value)){
                return value1;
            }
        }
        return null;
    }
}
