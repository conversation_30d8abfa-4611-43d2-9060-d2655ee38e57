package com.huayun.modules.erp.oams.fallback;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huayun.modules.erp.oams.controller.IEmployeeCostController;
import org.jeecg.common.api.vo.Result;

public class EmployeeCostFallback implements IEmployeeCostController {
    private final Throwable cause;

    public EmployeeCostFallback(Throwable cause) {
        this.cause = cause;
    }

    @Override
    public Result<IPage<EmployeeCostDTO>> requestGet(EmployeeCostQuery query) {
        return Result.error(cause.getMessage());
    }
}
