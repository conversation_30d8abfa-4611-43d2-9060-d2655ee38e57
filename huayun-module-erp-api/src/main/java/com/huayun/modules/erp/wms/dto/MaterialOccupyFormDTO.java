package com.huayun.modules.erp.wms.dto;

import com.huayun.modules.erp.wms.constant.enums.material.OccupyBizTypeEnum;
import com.huayun.modules.erp.wms.constant.enums.material.OccupyTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.groups.Default;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/04/03/14:11
 */
@Data
@Accessors(chain = true)
public class MaterialOccupyFormDTO {

    /**
     * 主业务ID
     */
    @ApiModelProperty(value = "主业务ID")
    @NotBlank(message = "主业务ID不可为空", groups = AddOccupyQtyRule.class)
    private String mainId;


    @ApiModelProperty("主业务单据号（如订单主表编号）")
    @NotBlank(message = "主业务单据号不可为空", groups = AddOccupyQtyRule.class)
    private String mainBizOrderNo;

    /**
     * 业务单据明细号
     */
    @ApiModelProperty(value = "业务单据明细号")
    @NotBlank(message = "业务单据明细号不可为空")
    private String detailBizOrderNo;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    @NotBlank(message = "物料ID不可为空")
    private String materialId;


    /**
     * 占用类型
     */
    @ApiModelProperty(value = "占用类型")
    @NotNull(message = "占用类型不可为空")
    private OccupyTypeEnum occupyType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @NotNull(message = "业务类型不可为空")
    private OccupyBizTypeEnum bizType;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    private String remark;

    /**
     * 已扣除数量
     */
    @ApiModelProperty(value = "已扣除数量")
    @NotNull(message = "已扣除数量不可为空", groups = DeductQtyRule.class)
    private BigDecimal deductQty;

    /**
     * 占用数量
     */
    @ApiModelProperty(value = "占用数量")
    @NotNull(message = "占用数量不可为空", groups = AddOccupyQtyRule.class)
    @Positive(message = "占用数量必须是正数")
    private BigDecimal occupyQty;

    public interface DeductQtyRule extends Default {
    }


    public interface AddOccupyQtyRule extends Default {
    }
}
