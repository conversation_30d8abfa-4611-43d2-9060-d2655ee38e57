package com.huayun.modules.erp.mms.entity;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.constant.Pattern;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 时间对象
 *
 * <AUTHOR>
 * @date 2022/09/05/16:34
 */
@Data
@NoArgsConstructor
@ApiModel(value = "加班时间")
public class WorkTime implements Serializable, Cloneable {

    /**
     * 班组ID
     */
    @ApiModelProperty(value = "班组ID")
    private String shiftId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.HOUR_MINUTE)
    @DateTimeFormat(pattern = Pattern.HOUR_MINUTE)
    @JSONField(format = Pattern.HOUR_MINUTE)
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.HOUR_MINUTE)
    @DateTimeFormat(pattern = Pattern.HOUR_MINUTE)
    @JSONField(format = Pattern.HOUR_MINUTE)
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    /**
     * 加班时间标记
     */
    @ApiModelProperty(value = "加班时间标记")
    @NotNull(message = "是否加班标记不能为空")
    private Boolean overTimeSign;


    /**
     * 获取工作时间
     *
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2022/11/12 17:49
     */
    public static BigDecimal getWorkHour(List<WorkTime> workTimes) {
        List<WorkTime> filterWorkTimes = workTimes.stream().filter(workTimeComponent -> workTimeComponent.getOverTimeSign() == null || !workTimeComponent.getOverTimeSign()).collect(Collectors.toList());
        return getBetweenHour(filterWorkTimes);

    }


    /**
     * 获取那一天的加班时间
     *
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2022/9/27 9:48
     */
    public static BigDecimal getOverHour(List<WorkTime> workTimes) {
        List<WorkTime> filterWorkTimes = workTimes.stream().filter(item -> item.getOverTimeSign() != null && item.getOverTimeSign()).collect(Collectors.toList());
        return getBetweenHour(filterWorkTimes);

    }

    public static BigDecimal getBetweenHour(List<WorkTime> filterWorkTimes) {
        BigDecimal workHour = new BigDecimal(0);
        for (WorkTime WorkTime : filterWorkTimes) {
            workHour = NumberUtil.add(workHour, getBetweenHour(WorkTime));
        }
        return workHour;
    }

    public static BigDecimal getBetweenHour(WorkTime WorkTime) {
        //把时间重置为1970年做比较
        DateTime startTime = DateUtil.parse(DateUtil.format(WorkTime.getStartTime(), Pattern.TIME), Pattern.TIME);
        DateTime endTime = DateUtil.parse(DateUtil.format(WorkTime.getEndTime(), Pattern.TIME), Pattern.TIME);
        long between = DateUtil.between(startTime, endTime, DateUnit.MINUTE);
        BigDecimal workHour = NumberUtil.div(between, new BigDecimal(60), 2);
        //因为DateUtil本身会做开始时间和结束时间的交付,夜班的时间差
        if (startTime.after(endTime)) {
            workHour = NumberUtil.sub(new BigDecimal(24), workHour);
        }
        return workHour;
    }


    public WorkTime clone() {
        try {
            return (WorkTime) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }
    }


    public WorkTime(Date startTime, Date endTime, String shiftId) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.shiftId = shiftId;
    }


}
