package com.huayun.modules.erp.wms.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.wms.dto.LotDTO;
import com.huayun.modules.erp.wms.dto.LotQueryDTO;
import com.huayun.modules.erp.wms.fallback.factory.LotFallbackFactory;
import org.jeecg.common.api.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 物料批号
 *
 * <AUTHOR>
 */
@FeignClient(value = "huayun-erp", contextId = "lot-api", fallbackFactory = LotFallbackFactory.class)
public interface ILotApi {

    /**
     * 分页查询
     *
     * <AUTHOR>
     * @date 2023/4/25 10:59
     * @param query
     * @return org.jeecg.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.dto.LotDTO>>
     */
    @GetMapping(value = "/api/wms/lots")
    Result<Page<LotDTO>> queryPage(@SpringQueryMap LotQueryDTO query);

    /**
     * 生成标签号
     *
     * <AUTHOR>
     * @date 2023/1/13 17:51
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     */
    @GetMapping(value = "/api/wms/lots/generator")
    Result<String> generatorLotNo();
    
    @PostMapping("/api/wms/lots/query")
    Result<List<LotDTO>> listLotByNos(@RequestBody LotQueryDTO queryDTO);
}
