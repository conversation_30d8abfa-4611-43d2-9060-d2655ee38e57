package com.huayun.modules.erp.oams.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.huayun.modules.erp.oams.constant.OamsConstant;
import com.huayun.modules.erp.oams.constant.enums.TemplateBizTypeEnum;
import com.huayun.modules.erp.oams.constant.enums.TemplateFinanceTypeEnum;
import com.huayun.modules.erp.oams.constant.enums.VoucherTemplateTypeEnum;
import com.huayun.modules.erp.oams.controller.query.InvoiceQuery;
import com.huayun.modules.erp.oams.entity.InvoiceTemplate;
import com.huayun.modules.erp.oams.entity.VoucherTemplate;
import com.huayun.modules.erp.oams.service.IInvoiceTemplateService;
import com.huayun.modules.erp.oams.service.IVoucherTemplateService;
import com.huayun.modules.erp.oams.vo.InvoiceTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/25
 */
@Slf4j
@Api(tags = "OAMS-发票模板")
@RestController
@RequestMapping("/oams/invoice/template")
public class InvoiceTemplateController {

    @Resource
    private IInvoiceTemplateService invoiceTemplateService;

    @Resource
    private IVoucherTemplateService voucherTemplateService;
 
    
    /**
     * 分页查询
     * @return
     */
    @ApiOperation(value = "发票模板查询", notes = "发票模板查询")
    @GetMapping("/get")
    public Result<List<InvoiceTemplateVO>> requestGetPage(InvoiceQuery query) {
        LambdaQueryWrapper<VoucherTemplate> wr = new LambdaQueryWrapper<>();
        wr.eq(VoucherTemplate::getTemplateType, VoucherTemplateTypeEnum.INVOICE);
        if (OamsConstant.SPECIAL_TICKE.equals(query.getInvoiceType()) || OamsConstant.E_SPECIAL_TICKE.equals(query.getInvoiceType())){
            wr.eq(VoucherTemplate::getFinanceType, TemplateFinanceTypeEnum.SPECIAL_INVOICE);
        }
        if (OamsConstant.UNIVERSAL_TICKET.equals(query.getInvoiceType()) || OamsConstant.E_UNIVERSAL_TICKET.equals(query.getInvoiceType())){
            wr.eq(VoucherTemplate::getFinanceType, TemplateFinanceTypeEnum.NOMAL_INVOICE);
        }
        if (OamsConstant.PURCHASE.equals(query.getInvoiceType())){
            wr.eq(VoucherTemplate::getTemplateBizType, TemplateBizTypeEnum.PURCHASE_INVOICE);
        }
        if (OamsConstant.SALE.equals(query.getInvoiceType())){
            wr.eq(VoucherTemplate::getTemplateBizType, TemplateBizTypeEnum.SALES_INVOICE);
        }

        List<VoucherTemplate> list = voucherTemplateService.list(wr);
        //专票使用专票模板SPECIAL_TICKE ， E_SPECIAL_TICKE ，普票使用普票模板 UNIVERSAL_TICKET，E_UNIVERSAL_TICKET
        List<InvoiceTemplateVO> res  = list.stream().map(InvoiceTemplateVO::new).collect(Collectors.toList());

        return Result.ok(res);
    }
    
    /**
     * 创建
     * @param form 
     */
    @ApiOperation(value = "发票模板-创建", notes = "发票模板-创建")
    @PostMapping("/create")
    public Result<InvoiceTemplate> requestCreate(@RequestBody InvoiceTemplateVO form ) {
        return Result.ok(invoiceTemplateService.save(form));
    }
    
    /**
     * 修改
     * @param form 
     */
    @ApiOperation(value = "发票模板-修改", notes = "发票模板-修改")
    @PostMapping("/update")
    public Result<InvoiceTemplate> requestUpdate(@RequestBody InvoiceTemplateVO form) {
        return Result.ok(invoiceTemplateService.update(form));
    }
    
    /**
     * 删除
     */
    @ApiOperation(value = "发票模板-删除", notes = "发票模板-删除")
    @DeleteMapping("/del/{id}")
    public Result<String> requestDelete(@PathVariable String id) {
    	invoiceTemplateService.del(id);
        return Result.ok("删除成功");
    }
    
    /**
     * 删除
     */
    @ApiOperation(value = "发票模板-按ID获取详情", notes = "发票模板-按ID获取详情")
    @DeleteMapping("/get/{id}")
    public Result<InvoiceTemplateVO> getDetail(@PathVariable String id) {
    	InvoiceTemplateVO res = invoiceTemplateService.getDetail(id);
        return Result.ok(res);
    }
}
