package com.huayun.modules.erp.mro.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.common.approval.annotation.ApprovalRequired;
import com.huayun.modules.common.log.aspect.annotation.Log;
import com.huayun.modules.common.log.constant.OperateTypeConstant;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.erp.mro.constant.MroConstant;
import com.huayun.modules.erp.mro.constant.MroLogConstant;
import com.huayun.modules.erp.mro.constant.enums.DeviceTorsionStatusEnums;
import com.huayun.modules.erp.mro.entity.DeviceInfo;
import com.huayun.modules.erp.mro.entity.DeviceToolInfo;
import com.huayun.modules.erp.mro.form.CreateDeviceToolInfoForm;
import com.huayun.modules.erp.mro.form.DeviceToolImportForm;
import com.huayun.modules.erp.mro.form.GeneralInformationForm;
import com.huayun.modules.erp.mro.form.UpdateDeviceToolInfoForm;
import com.huayun.modules.erp.mro.query.*;
import com.huayun.modules.erp.mro.service.IDeviceInfoService;
import com.huayun.modules.erp.mro.service.IDeviceToolInfoService;
import com.huayun.modules.erp.mro.vo.*;
import com.huayun.modules.erp.pdm.entity.Product;
import com.huayun.modules.erp.pdm.service.IProductService;
import com.huayun.modules.erp.utils.FileUtils;
import com.huayun.modules.erp.utils.ImportError;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工具信息维护
 *
 * <AUTHOR> am I?
 * @date 2022/08/04
 */
@Slf4j
@Api(tags = "MRO工具信息维护")
@RestController
@RequestMapping("/mro")
public class DeviceToolInfoController {

    @Resource
    private IDeviceToolInfoService deviceToolInfoService;
    @Resource
    private IProductService productService;
    @Resource
    private IDeviceInfoService deviceInfoService;

    /**
     * 分页列表查询
     *
     * @param deviceToolInfoQuery
     */
    @ApiOperation(value = "工具信息维护-分页列表查询", notes = "工具信息维护-分页列表查询")
    @GetMapping("/device-tool-infos")
    public Result<IPage<DeviceToolInfoVO>> queryPage(DeviceToolInfoQuery deviceToolInfoQuery) {
        IPage<DeviceToolInfoVO> pageList = deviceToolInfoService.getDeviceToolInfoPage(deviceToolInfoQuery).convert(DeviceToolInfoVO::new);
        fillDeviceToolInfoVO(pageList.getRecords());
        return Result.OK("查询成功！", pageList);
    }

    /**
     * 填充管理产品、关联设备信息
     * @param tools
     */
    private void fillDeviceToolInfoVO(List<DeviceToolInfoVO> tools) {
        Set<String> productIds = tools.stream().map(DeviceToolInfoVO::getToolProductId).filter(ObjectUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet());
        Set<String> devIds = tools.stream().map(DeviceToolInfoVO::getDevId).filter(ObjectUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.toSet());
        devIds.add("none");
        productIds.add("none");
        List<Product> productList = productService.getProductByIds(productIds);
        Map<String, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        List<DeviceInfo> deviceInfoList = deviceInfoService.listByIds(devIds);
        Map<String, DeviceInfo> deviceInfoMap = deviceInfoList.stream().collect(Collectors.toMap(DeviceInfo::getId, Function.identity()));
        for (DeviceToolInfoVO tool : tools) {
            List<Product> products = new ArrayList<>();
            List<DeviceInfo> deviceToolInfos = new ArrayList<>();
            List<String> toolProductId = tool.getToolProductId();
            if (ObjectUtil.isNotEmpty(toolProductId)){
                toolProductId.forEach(id -> {
                    Product product = productMap.get(id);
                    if (ObjectUtil.isNotEmpty(product)) {
                        products.add(product);
                    }
                });
            }
            List<String> devId = tool.getDevId();
            if (ObjectUtil.isNotEmpty(devId)){
                devId.forEach(id -> {
                    DeviceInfo deviceInfo = deviceInfoMap.get(id);
                    if (ObjectUtil.isNotEmpty(deviceInfo)) {
                        deviceToolInfos.add(deviceInfo);
                    }
                });
            }
            tool.setProducts(products);
            tool.setDeviceInfos(deviceToolInfos);

        }

    }

    /**
     * 添加
     *
     * @param createDeviceToolInfoForm
     */
    @Log(resourceType = MroConstant.TOOL,
            operateType = OperateTypeConstant.INSERT,
            template = MroLogConstant.TOOL_INSERT)
    @ApiOperation(value = "工具信息维护-添加", notes = "工具信息维护-添加")
    @PostMapping("/device-tool-infos")
    @ApprovalRequired(resourceType = MroConstant.TOOL, approvalType = MroConstant.TOOL_CREATE_REVIEW)
    public Result<DeviceToolInfoVO> createDeviceToolInfo(@Validated @RequestBody CreateDeviceToolInfoForm createDeviceToolInfoForm) {
        DeviceToolInfo deviceToolInfo = new DeviceToolInfo();
        BeanUtils.copyProperties(createDeviceToolInfoForm, deviceToolInfo);
        deviceToolInfo.setDevId(createDeviceToolInfoForm.getDevId());
        deviceToolInfo.setToolUseTypeName(createDeviceToolInfoForm.getToolUseTypeName());
        deviceToolInfo.setToolProductId(createDeviceToolInfoForm.getToolProductId());
        deviceToolInfo.setToolTorsionStatus(DeviceTorsionStatusEnums.NORMAL_STATUS);
        DeviceToolInfo toolInfo = deviceToolInfoService.createOrUpdateDeviceToolInfo(deviceToolInfo);
        return Result.OK("添加成功！", new DeviceToolInfoVO(toolInfo));
    }

    /**
     * 编辑
     *
     * @param deviceToolInfoId
     * @param updateDeviceToolInfoForm
     */
    @Log(resourceType = MroConstant.TOOL,
            operateType = MroConstant.TOOL_UPDATE_REVIEW,
            template = MroLogConstant.TOOL_UPDATE)
    @ApiOperation(value = "工具信息维护-编辑", notes = "工具信息维护-编辑")
    @PutMapping("/device-tool-infos/{deviceToolInfoId}")
    @ApprovalRequired(resourceType = MroConstant.TOOL, approvalType = MroConstant.TOOL_UPDATE_REVIEW)
    public Result<DeviceToolInfoVO> updateDeviceToolInfo(@Validated @RequestBody UpdateDeviceToolInfoForm updateDeviceToolInfoForm, @PathVariable String deviceToolInfoId) {
        DeviceToolInfo deviceToolInfo = new DeviceToolInfo();
        DeviceToolInfo selectDeviceToolInfo = deviceToolInfoService.getById(deviceToolInfoId);
        BizAssert.isNotNull(selectDeviceToolInfo, "工具ID不存在");
        List<DeviceTorsionStatusEnums> changeStateList = Arrays.asList(DeviceTorsionStatusEnums.DEBUGGING, DeviceTorsionStatusEnums.NORMAL_STATUS);
        BizAssert.isTrue(changeStateList.contains(selectDeviceToolInfo.getToolTorsionStatus()), "调试中和正常状态才可修改");
        BeanUtils.copyProperties(updateDeviceToolInfoForm, deviceToolInfo);
        deviceToolInfo.setToolTorsionStatus(DeviceTorsionStatusEnums.NORMAL_STATUS);
        return Result.OK("编辑成功!", new DeviceToolInfoVO(deviceToolInfoService.updateDeviceToolInfo(deviceToolInfoId, deviceToolInfo)));
    }

    /**
     * 通过id删除
     *
     * @param deviceToolInfoId
     */
    @ApiOperation(value = "工具信息维护-通过id删除", notes = "工具信息维护-通过id删除")
    @DeleteMapping("/device-tool-infos/{deviceToolInfoId}")
    public Result deleteDeviceToolInfo(@PathVariable String deviceToolInfoId) {
        deviceToolInfoService.deleteDeviceToolInfo(deviceToolInfoId);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param
     */
    @ApiOperation(value = "工具信息维护-通用类工具查看", notes = "工具信息维护-通用类工具查看")
    @GetMapping("/device-general-information")
    public Result<GeneralInformationVO> findGeneralInformation(GeneralInformationForm informationForm) {
        GeneralInformationVO deviceToolInfo = deviceToolInfoService.findGeneralInformation(informationForm);
        return Result.OK("查询成功！", deviceToolInfo);
    }


    @ApiOperation(value = "工具信息维护-配套类工具查看", notes = "工具信息维护-配套类工具查看")
    @GetMapping("/device-supporting-information")
    public Result<SupportingInformationVO> findSupportingInformation(GeneralInformationForm informationForm) {
        SupportingInformationVO deviceToolInfo = deviceToolInfoService.findSupportingInformation(informationForm);
        return Result.OK("查询成功！", deviceToolInfo);
    }


    /**
     * 根据工具类型查询工具信息
     *
     * @param deviceToolInfoTypeQuery
     */
    @ApiOperation(value = "工具信息维护-根据工具类型查询工具信息", notes = "工具信息维护-根据工具类型查询工具信息")
    @PostMapping("/tool-find-type-infos")
    public Result<IPage<DeviceToolInfoTypeVO>> findTooInfoBytoolModel(DeviceToolInfoTypeQuery deviceToolInfoTypeQuery) {
        IPage<DeviceToolInfoTypeVO> deviceToolInfo = deviceToolInfoService.findTooInfoBytoolModel(deviceToolInfoTypeQuery).convert(DeviceToolInfoTypeVO::new);
        return Result.OK("查询成功！", deviceToolInfo);
    }

    /**
     * 根据编码查询设备
     *
     * @param deviceToolInfoCodeQuery
     */
    @ApiOperation(value = "工具信息维护-根据编码查询工具信息", notes = "工具信息维护-根据编码查询工具信息")
    @PostMapping("/tool-find-code-infos")
    public Result<IPage<DeviceToolInfoCodeVO>> findTooInfoByCode(DeviceToolInfoCodeQuery deviceToolInfoCodeQuery) {
        IPage<DeviceToolInfoCodeVO> deviceToolInfo = deviceToolInfoService.findTooInfoByToolCode(deviceToolInfoCodeQuery).convert(DeviceToolInfoCodeVO::new);
        return Result.OK("查询成功！", deviceToolInfo);
    }

    /**
     * 根据设备类型和型号查询工具信息(配套类)
     *
     * @param modelTypeDeviceQuery
     */
    @ApiOperation(value = "工具信息维护-根据设备类型和型号查询工具信息(配套类)", notes = "工具信息维护-根据设备类型和型号查询工具信息(配套类)")
    @PostMapping("/find-type-mode-tools")
    public Result<IPage<DeviceToolModeAndTypeVO>> findTypeAndModeTools(@RequestBody ModelTypeDeviceQuery modelTypeDeviceQuery) {
        IPage<DeviceToolModeAndTypeVO> deviceToolInfo = deviceToolInfoService.findTypeAndModeTools(modelTypeDeviceQuery).convert(DeviceToolModeAndTypeVO::new);
        return Result.OK("查询成功！", deviceToolInfo);
    }

    /**
     * 根据产品ID配套工具(配套类)  工序所需资源查询
     *
     * @param modelTypeDeviceQuery
     */
    @ApiOperation(value = "工具信息维护-根据产品ID配套工具(配套类)", notes = "工具信息维护-根据产品ID配套工具(配套类)")
    @PostMapping("/product-tools")
    public Result<IPage<DeviceToolModeAndTypeVO>> findToolInfoByProductId(@RequestBody ModelTypeDeviceQuery modelTypeDeviceQuery) {
        IPage<DeviceToolModeAndTypeVO> deviceToolInfo = deviceToolInfoService.findToolInfoByProductId(modelTypeDeviceQuery).convert(DeviceToolModeAndTypeVO::new);
        return Result.OK("查询成功！", deviceToolInfo);
    }


    /**
     * 工具信息维护-查询根据型号分组的工具信息
     */
    @ApiOperation(value = "工具信息维护-查询根据型号分组的配套工具信息", notes = "工具信息维护-查询根据型号分组的配套工具信息")
    @GetMapping("/tools-model")
    public Result<IPage<DeviceToolModeAndTypeVO>> getToolGroupByToolModel(@RequestParam(name = "page", required = false, defaultValue = "1") Integer pageNo, @RequestParam(required = false, defaultValue = "20") int pageSize) {
        Page<DeviceToolInfo> page = new Page<>(pageNo, pageSize);
        IPage<DeviceToolInfo> deviceToolInfoIPage = deviceToolInfoService.selectToolGroupByModel(page);
        return Result.ok(deviceToolInfoIPage.convert(DeviceToolModeAndTypeVO::new));
    }




    /**
     * 根据设备类型和型号查询工具信息(通用类工具信息)
     *
     * @param deviceToolModeQuery
     */
    @ApiOperation(value = "工具信息维护-查询通用类工具信息", notes = "工具信息维护-查询通用类工具信息")
    @PostMapping("/find-general-device-toolInfos")
    public Result<IPage<DeviceToolModeVO>> findGeneralDeviceTools(DeviceToolModeQuery deviceToolModeQuery) {
        IPage<DeviceToolModeVO> deviceToolInfo = deviceToolInfoService.findGeneralDeviceTools(deviceToolModeQuery).convert(DeviceToolModeVO::new);
        return Result.OK("查询成功！", deviceToolInfo);
    }

    /**
     * 配套类设备按照编码查询工具信息
     *
     * @param deviceToolModeQuery
     */
    @ApiOperation(value = "工具信息维护- 配套类设备按照编码查询工具信息", notes = "工具信息维护- 配套类设备按照编码查询工具信息")
    @PostMapping("/find-code-device-toolInfos")
    public Result<IPage<DeviceToolModeVO>> findToolCodeDeviceTools(@RequestBody DeviceToolCodeQuery deviceToolModeQuery) {
        IPage<DeviceToolModeVO> deviceToolInfo = deviceToolInfoService.findToolCodeDeviceTools(deviceToolModeQuery).convert(DeviceToolModeVO::new);
        return Result.OK("查询成功！", deviceToolInfo);
    }

    /**
     * 保养计划添加需要工具接口
     *
     * @param deviceToolQuery
     */
    @ApiOperation(value = "工具信息维护- 保养计划添加需要工具条件查询", notes = "工具信息维护-保养计划添加需要工具条件查询")
    @GetMapping("/device-tools")
    public Result<IPage<DeviceToolVO>> findTools(DeviceToolQuery deviceToolQuery) {
        IPage<DeviceToolVO> deviceTool = deviceToolInfoService.findTools(deviceToolQuery).convert(DeviceToolVO::new);
        return Result.OK("查询成功！", deviceTool);
    }

    @ApiOperation(value = "工具信息维护- 查询型号信息", notes = "工具信息维护-查询型号信息")
    @GetMapping("/device-tool-models")
    public Result<List<DeviceToolInfo>> findModels() {
        List<DeviceToolInfo> toolModels = deviceToolInfoService.findModels();
        return Result.OK("查询成功！", toolModels);
    }




    /**
     * 工具修改提交审核
     */
    @ApiOperation(value = "工具信息维护- 暂存", notes = "工具信息维护-暂存")
    @PutMapping("/device-tool-infos/temporary-storage")
    public Result<DeviceToolInfoVO> temporaryStorage(@Validated @RequestBody CreateDeviceToolInfoForm createDeviceToolInfoForm) {
        DeviceToolInfo deviceToolInfo = new DeviceToolInfo();
        BeanUtils.copyProperties(createDeviceToolInfoForm, deviceToolInfo);
        deviceToolInfo.setToolTorsionStatus(DeviceTorsionStatusEnums.DEBUGGING);
        return Result.OK("编辑成功!", new DeviceToolInfoVO(deviceToolInfoService.createOrUpdateDeviceToolInfo(deviceToolInfo)));
    }


    /**
     * 工具详情查询
     *
     * @param
     */
    @ApiOperation(value = "工具信息维护- 工具详情查询", notes = "工具信息维护-工具详情查询")
    @GetMapping("/device-tool-details/{toolInfoId}")
    public Result<DeviceToolDetailsVO> findDeviceToolDetails(@PathVariable String toolInfoId) {
        return Result.OK("查询成功！", new DeviceToolDetailsVO(deviceToolInfoService.findDeviceToolDetails(toolInfoId)));
    }
    /**
     * 设备导入
     *
     * <AUTHOR>
     * @date 2023/3/7 17:12
     * @param excelFile
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     */
    @Log(resourceType = MroConstant.TOOL, operateType = OperateTypeConstant.IMPORT, template = "工具导入")
    @ApiOperation(value = "工具管理-导入", notes = "工具管理-导入")
    @PostMapping("/device-tool-infos/import")
    public Result<List<ImportError>> importDevice(@RequestParam("file") MultipartFile excelFile,
                                                  @RequestParam(value = "coverFlag",required = false,defaultValue = "false") Boolean coverFlag) {
        //读取为Bean列表
        List<DeviceToolImportForm> deviceTools = FileUtils.importExcel(DeviceToolImportForm.class, excelFile);
        //工具导入
        List<ImportError> failureReason = deviceToolInfoService.importDeviceTools(deviceTools,coverFlag);
        if (failureReason.isEmpty()) {
            return Result.ok("导入成功！");
        } else {
            Result<List<ImportError>> result = Result.error("导入失败", failureReason);
            result.setCode(400);
            return result;
        }
    }

    /**
     * 固定资产查询工具
     * @param
     * @return
     */
    @ApiOperation(value = "工具基础信息-固定资产查询工具分页列表查询", notes = "工具基础信息-固定资产查询工具分页列表查询")
    @GetMapping("/device-tool-infos/asset")
    public Result<IPage<DeviceToolInfoVO>> queryPage4Asset(DeviceToolInfoQuery deviceToolInfoQuery) {
        IPage<DeviceToolInfoVO> pageList = deviceToolInfoService.getToolInfo4AssetPage(deviceToolInfoQuery).convert(DeviceToolInfoVO::new);
        return Result.OK("查询成功！", pageList);
    }


}