package com.huayun.modules.erp.wms.vo;

import com.huayun.modules.common.vo.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: lihao
 * @Date: 2025/04/01/下午 03:05
 * @Description:
 */
@Data
public class MaterialInventoryAgeInfoVO extends BaseVO {
    private BigDecimal zeroInventory;
    private BigDecimal zeroTotal;

    private BigDecimal oneInventory;
    private BigDecimal oneTotal;

    private BigDecimal twoInventory;
    private BigDecimal twoTotal;

    private BigDecimal threeInventory;
    private BigDecimal threeTotal;

    private BigDecimal fourInventory;
    private BigDecimal fourTotal;


    private BigDecimal fiveInventory;
    private BigDecimal fiveTotal;

    /**库存*/
    private BigDecimal inventory;

    /**总价*/
    private BigDecimal total;
}
