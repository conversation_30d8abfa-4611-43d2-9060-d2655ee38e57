package com.huayun.modules.erp.mms.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.constant.Pattern;
import com.huayun.modules.erp.mms.constant.enums.MaterialRequisitionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


/**
 * Created with IntelliJ IDEA.
 *
 * @Author: lihao
 * @Date: 2024/11/21/下午 05:34
 * @Description:
 */
@Data
public class CreateMmsMaterialRequisitionForm {
    
    @ApiModelProperty(value = "申请人部门")
    private String applyDepartmentId;

    @ApiModelProperty(value = "申请人部门")
    private String applyDepartmentName;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "申请人名称")
    private String applyName;

    @ApiModelProperty(value = "申请人")
    private String applyBy;

    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "期望日期")
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.DATE)
    @DateTimeFormat(pattern = Pattern.DATE)
    private Date applyTime;

    @ApiModelProperty(value = "物料明细")
    private List<CreateMmsMaterialRequisitionItemForm> itemFormList;
    
}
