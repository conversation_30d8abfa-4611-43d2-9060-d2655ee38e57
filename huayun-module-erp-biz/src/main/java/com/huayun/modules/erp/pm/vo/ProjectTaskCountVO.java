package com.huayun.modules.erp.pm.vo;

import com.huayun.modules.common.vo.BaseVO;
import com.huayun.modules.erp.pm.constant.ProjectTaskCountEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: ProjectTaskCountVO
 * Function: 对项目得任务统计
 * date: 2024年3月1日 下午6:50:22
 *
 * <AUTHOR>
 * @version
 * @since version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProjectTaskCountVO   extends BaseVO {

	private ProjectTaskCountEnum taskCountState;

	private int count;


}
