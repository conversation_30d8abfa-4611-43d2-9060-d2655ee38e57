package com.huayun.modules.erp.wms.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.huayun.modules.common.dict.JsonDict;
import com.huayun.modules.common.util.OssUtils;
import com.huayun.modules.erp.pms.enums.EnumTypeConverter;
import com.huayun.modules.erp.utils.ProcessUtils;
import com.huayun.modules.erp.wms.constant.enums.material.MaterialPropertyEnum;
import com.huayun.modules.erp.wms.entity.model.InventoryTurnoverModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * 存货周转率
 *
 * <AUTHOR>
 * @date 2023/6/26 15:16
 **/
@Data
public class InventoryTurnoverVO {

    /**物料id*/
    @ApiModelProperty(value = "物料id")
    private String materialId;

    /**物料编码*/
    @ApiModelProperty(value = "物料编码")
    @Excel(name = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料主图url")
    private String masterDrawingUrl;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @Excel(name = "物料名称")
    private String materialName;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @JsonDict(type = "WMS_MATERIAL_PROPERTY")
    private String materialProperty;


    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @JsonDict(type = "WMS_MATERIAL_PROPERTY")
    @Excel(name = "属性")
    private String propertyName;

    /**
     * 物料规格
     */
    @ApiModelProperty(value = "物料规格")
    @Excel(name = "规格")
    private String materialSpecification;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    @Excel(name = "单位")
    private String materialUnit;

    /**单价*/
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**期初库存*/
    @ApiModelProperty(value = "期初库存")
    @Excel(name = "期初库存")
    private BigDecimal beginningInventory;

    /**
     * 期末库存
     */
    @ApiModelProperty(value = "期末库存")
    @Excel(name = "期末库存")
    private BigDecimal endingInventory;

    /**
     * 存货周转率
     */
    @ApiModelProperty(value = "存货周转率")
    private BigDecimal inventoryTurnover;

    /**
     * 导出时候用到
     */
    @ApiModelProperty(value = "百分比存货周转率")
    @Excel(name = "库存周转率")
    private String percentageInventoryTurnover;

    /**
     * 存货周转天数
     */
    @ApiModelProperty(value = "存货周转天数")
    @Excel(name = "库存周转天数")
    private BigDecimal inventoryTurnoverDays;

    public InventoryTurnoverVO(InventoryTurnoverModel model) {
        init(model);
    }

    private void init(InventoryTurnoverModel model) {
        BeanUtil.copyProperties(model, this);
        this.unitPrice = ProcessUtils.stripTrailingZeros(this.unitPrice);
        this.beginningInventory = ProcessUtils.stripTrailingZeros(this.beginningInventory);
        this.endingInventory = ProcessUtils.stripTrailingZeros(this.endingInventory);
        this.inventoryTurnover = ProcessUtils.stripTrailingZeros(this.inventoryTurnover);
        this.inventoryTurnoverDays = ProcessUtils.stripTrailingZeros(this.inventoryTurnoverDays);
        this.percentageInventoryTurnover = StrUtil.format("{}%",this.inventoryTurnover);

        MaterialPropertyEnum materialPropertyEnum = EnumTypeConverter.convert(MaterialPropertyEnum.class, model.getMaterialProperty());
        if (materialPropertyEnum != null) {
            this.propertyName = materialPropertyEnum.getLabel();
        }

        if (StringUtils.isNotEmpty(model.getMasterDrawingUrl())) {
            //物料主图拼接域名
            this.setMasterDrawingUrl(OssUtils.getUrl(model.getMasterDrawingUrl()));
        }

    }
}
