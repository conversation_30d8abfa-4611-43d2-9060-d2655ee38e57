package com.huayun.modules.erp.oms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huayun.modules.erp.oms.entity.OrderDeliveryItem;
import com.huayun.modules.erp.oms.entity.model.OrderDeliveryItemModel;
import com.huayun.modules.erp.oms.entity.model.OrderDeliveryItemProfitModel;
import com.huayun.modules.erp.oms.entity.model.ProductDeliveryQuantityModel;
import com.huayun.modules.erp.oms.query.OrderDeliveryQuery;
import com.huayun.modules.erp.wms.form.QualityControlForm;

import java.util.*;

/**
 * <AUTHOR>
 */
public interface IOrderDeliveryItemService {

    /**
     * 列表查询
     *
     * @param orderNo
     * @return java.util.List<com.huayun.modules.erp.oms.entity.model.OrderDeliveryModel>
     * <AUTHOR>
     * @date 2023/2/28 15:50
     */
    List<OrderDeliveryItemModel> list(String orderNo);

	List<OrderDeliveryItem> list(QueryWrapper<OrderDeliveryItem> wrapper);

	/**
     * 根据产品编码查询交付数量
     *
     * @param productCodes
     * @param startTime
     * @param endTime
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2023/7/12 17:39
     */
    Map<String, ProductDeliveryQuantityModel> findProductDeliveryQuantity(Collection<String> productCodes, Date startTime, Date endTime);

    void qualityControlSubmit(QualityControlForm from);

    void qualityControlEdit(QualityControlForm from);

    List<OrderDeliveryItemModel> findByOrderNo(String orderNo);

	/**
	 * getSaleProfitReport:销售利润分析表.
	 * Date:2024年12月24日10:33:38
	 * <AUTHOR>
	 * @param query
	 * @return
	 * @since version 1.0
	 */
	IPage<OrderDeliveryItemProfitModel> getSaleProfitReport(OrderDeliveryQuery query);

	/**
	 * sumSaleProfitReport:销售利润分析表 合计.
	 * Date:2024年12月24日10:33:38
	 * <AUTHOR>
	 * @param query
	 * @return
	 * @since version 1.0
	 */
	OrderDeliveryItemProfitModel sumSaleProfitReport(OrderDeliveryQuery query);

	Map<String, Object> exportSaleProfitReport(OrderDeliveryQuery query);

	ProductDeliveryQuantityModel sumDeliveryQuantity(OrderDeliveryQuery query);

	/**
	 *
	 * @param deliveryIds
	 * @return
	 */
	List<OrderDeliveryItem> listByDeliveryIds(Set<String> deliveryIds);
}
