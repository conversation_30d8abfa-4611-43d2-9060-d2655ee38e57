/**
 * Project Name:huayun-module-erp-biz
 * File Name:VoucherTypeEnum.java
 * Package Name:com.huayun.modules.erp.oams.constant.enums
 * Date:2024年7月1日09:44:21
 * Copyright (c) 2024, <EMAIL> All Rights Reserved.
 *
*/

package com.huayun.modules.erp.oams.constant.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.huayun.modules.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 关联设置科目类型
 */
@Getter
@AllArgsConstructor
public enum AssociateSetLedgerEnum implements BaseEnum{
    MATERIAL("存货科目","MATERIAL","CH101",LedgerAssoSetTypeEnum.MATERIAL.getValue(),"ledgerCode1"),
    INCOME("收入科目","INCOME","CH102",LedgerAssoSetTypeEnum.MATERIAL.getValue(),"ledgerCode2"),
    COST("成本科目","COST","CH103",LedgerAssoSetTypeEnum.MATERIAL.getValue(),"ledgerCode3"),

    ACCOUNTS_RECEIVABLE("应收账款科目","ACCOUNTS_RECEIVABLE","KH101",LedgerAssoSetTypeEnum.CUSTOMER.getValue(),"ledgerCode1"),
    ADVANCE_RECEIVABLES("预收账款科目","ADVANCE_RECEIVABLES","KH102",LedgerAssoSetTypeEnum.CUSTOMER.getValue(),"ledgerCode2"),
    OTHER_RECEIVABLES("其他应收款科目","OTHER_RECEIVABLES","KH103",LedgerAssoSetTypeEnum.CUSTOMER.getValue(),"ledgerCode3"),

    ACCOUNTS_PAYABLE("应付账款科目","ACCOUNTS_PAYABLE","GYS101",LedgerAssoSetTypeEnum.SUPPLIER.getValue(),"ledgerCode1"),
    ADVANCE_PAYABLES("预付账款科目","ADVANCE_PAYABLES","GYS102",LedgerAssoSetTypeEnum.SUPPLIER.getValue(),"ledgerCode2"),
    OTHER_PAYABLES("其他应付款科目","OTHER_PAYABLES","GYS103",LedgerAssoSetTypeEnum.SUPPLIER.getValue(),"ledgerCode3"),

    //费用类别科目
    COST_TYPE("费用类别科目","COST_TYPE","FY101",LedgerAssoSetTypeEnum.COST.getValue(),"ledgerCode1"),

    //收入科目
    INCOME_TYPE("收入类别科目","INCOME_TYPE","SR101",LedgerAssoSetTypeEnum.INCOME.getValue(),"ledgerCode1"),

    //现金银行科目
    CASH("现金银行科目","CASH","XJYH101",LedgerAssoSetTypeEnum.CASH.getValue(),"ledgerCode1"),
    ;

    public static final String DICT_TYPE = "";

    //存货前缀
    public static final String PRE_MATERIAL = "CH";

    public static final String MATERIAL_CODE = "CH101";



    private final String label;
    @EnumValue
    private final String value;
    /**
     * 编码
     */
    private final String code;
    /**
     * 类型：LedgerAssoSetTypeEnum
     */
    private final String type;

    /**
     * 关联表里对应的字段
     */
    private final String column;

    /**
     * 遍历获取所有编码,返回结果为List集合
     */
    public static List<String> getCodes() {
        List<String> codes = new ArrayList<>();
        for (AssociateSetLedgerEnum associateSetLedger : values()) {
            codes.add(associateSetLedger.code);
        }
        return codes;
    }

    public static List<String> getOtherExpenseCodes() {
        List<String> codes = new ArrayList<>();
        codes.add("FY101");
        codes.add("SR101");
        return codes;
    }

    /**
     * 根据编码获取枚举
     */
    public static AssociateSetLedgerEnum getByCode(String code) {
        for (AssociateSetLedgerEnum associateSetLedger : values()) {
            if (associateSetLedger.code.equals(code)) {
                return associateSetLedger;
            }
        }
        return null;
    }

    /**
     * 根据类型和字段获取列枚举
     */
    public static AssociateSetLedgerEnum getByTypeAndColumn(String type, String column) {
        for (AssociateSetLedgerEnum associateSetLedger : values()) {
            if (associateSetLedger.type.equals(type) && associateSetLedger.column.equals(column)) {
                return associateSetLedger;
            }
        }
        return null;
    }





}

