package com.huayun.modules.erp.oams.controller.form;

import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
* TODO description
*
* <AUTHOR>
* @date 2022/7/27
*/
@Data
public class AssetDeprecitionItemForm {

	/**折旧id*/
    @ApiModelProperty(value = "折旧id")
	private String deprecitionId;
	/**资产编码*/
    @ApiModelProperty(value = "资产编码")
	private String assetNo;
	/**资产id*/
    @ApiModelProperty(value = "资产id")
	private String assetId;
	/**资产分类*/
    @ApiModelProperty(value = "资产分类")
	private String assetCategoryName;
	/**资产名称*/
    @ApiModelProperty(value = "资产名称")
	private String assetName;
	/**规格型号*/
    @ApiModelProperty(value = "规格型号")
	private String assetModel;
	/**资产部门id 分摊部门*/
    @ApiModelProperty(value = "资产部门id 分摊部门")
	private String useDepartment;
	/**使用部门名称*/
    @ApiModelProperty(value = "使用部门名称")
	private String useDepartmentName;
	/**原值*/
    @ApiModelProperty(value = "原值")
	private java.math.BigDecimal originAmount;
	/**累计折旧金额*/
    @ApiModelProperty(value = "累计折旧金额")
	private java.math.BigDecimal accumulatedDepreciation;
	/**上期净值*/
    @ApiModelProperty(value = "上期净值")
	private java.math.BigDecimal lastMonthNetWorth;
	/**本期折旧*/
    @ApiModelProperty(value = "本期折旧")
	private java.math.BigDecimal currentDepreciation;
	/**残值率*/
    @ApiModelProperty(value = "残值率")
	private java.math.BigDecimal residualValueRate;
	/**本期净值*/
    @ApiModelProperty(value = "本期净值")
	private java.math.BigDecimal netWorth;

	@ApiModelProperty(value = "上期累计折旧")
	private java.math.BigDecimal lastAccumulatedDepreciation;
}
