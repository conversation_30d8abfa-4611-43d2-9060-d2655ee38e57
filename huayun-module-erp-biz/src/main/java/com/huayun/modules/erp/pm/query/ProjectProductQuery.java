package com.huayun.modules.erp.pm.query;

import com.huayun.modules.common.mybatis.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: ProjectProductQuery
 * Function: 项目产品查询.
 * date: 2024年3月13日 下午5:25:14
 *
 * <AUTHOR>
 * @version
 * @since version 1.0
 */
@Data
@NoArgsConstructor
@ApiModel(value = "项目产品查询参数")
public class ProjectProductQuery extends BaseQuery {
	@ApiModelProperty(value = "项目ID")
	private String pId;

	@ApiModelProperty(value = "任务执行人名称")
	private String sunpId;
}
