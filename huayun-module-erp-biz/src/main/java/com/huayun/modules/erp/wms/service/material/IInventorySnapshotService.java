package com.huayun.modules.erp.wms.service.material;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.wms.entity.InventorySnapshot;
import com.huayun.modules.erp.wms.entity.model.InventoryTurnoverModel;
import com.huayun.modules.erp.wms.query.InventorySnapshotQuery;
import com.huayun.modules.erp.wms.query.InventoryTurnoverQuery;

/**
 * <AUTHOR>
 */
public interface IInventorySnapshotService {

    /**
     * 创建快照
     *
     * <AUTHOR>
     * @date 2023/6/25 18:04
     * @return void
     */
    void saveSnapshot();

    /**
     * 分页查询
     *
     * <AUTHOR>
     * @date 2023/6/27 16:32
     * @param page
     * @param query
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.huayun.modules.erp.wms.entity.InventorySnapshot>
     */
    IPage<InventorySnapshot> queryPage(Page<InventorySnapshot> page, InventorySnapshotQuery query);

    /**
     * 分页查询存货周转率
     *
     * <AUTHOR>
     * @date 2023/6/26 17:57
     * @param page
     * @param query
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.huayun.modules.erp.wms.entity.model.InventoryTurnoverModel>
     */
    IPage<InventoryTurnoverModel> queryInventoryTurnoverPage(Page<InventorySnapshot> page, InventoryTurnoverQuery query);
}
