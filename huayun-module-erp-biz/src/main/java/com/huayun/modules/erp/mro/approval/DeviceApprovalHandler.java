package com.huayun.modules.erp.mro.approval;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.huayun.modules.common.approval.ApprovalHandler;
import com.huayun.modules.common.approval.entity.Approval;
import com.huayun.modules.common.constant.Pattern;
import com.huayun.modules.common.exception.ExceptionFactory;
import com.huayun.modules.erp.mro.constant.MroConstant;
import com.huayun.modules.erp.mro.constant.enums.DeviceTorsionStatusEnums;
import com.huayun.modules.erp.mro.entity.DeviceInfo;
import com.huayun.modules.erp.mro.form.UpdateDeviceInfoForm;
import com.huayun.modules.erp.mro.service.IDeviceInfoService;
import com.huayun.modules.erp.mro.vo.DeviceInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 *设备审核
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeviceApprovalHandler implements ApprovalHandler {
    private static final Map<String, String> LABEL_MAP = new LinkedHashMap<String, String>();

    static {
        LABEL_MAP.put("devCode", "设备编码");
        LABEL_MAP.put("devName", "设备名称");
        LABEL_MAP.put("devModel", "设备型号");
        LABEL_MAP.put("devSequenceNum", "设备序号");
        LABEL_MAP.put("devDateProduction", "设备出厂日期");
        LABEL_MAP.put("devType", "设备类型");
        LABEL_MAP.put("devWeightCategoryId", "重要性分类");
        LABEL_MAP.put("devAssetCategoryId", "资产分类");
        LABEL_MAP.put("devDepartment", "设备归属部门");
        LABEL_MAP.put("devUnitPrice", "单价（元/小时）");
        LABEL_MAP.put("devOverallEffect", "OEE设定（%）");
        LABEL_MAP.put("ratedPower", "额定功率（KW）");
        LABEL_MAP.put("devOperationUrl", "操作信息");
        LABEL_MAP.put("devMaintainUrl", "维护信息");
        LABEL_MAP.put("devCapacityDescription", "设备产能描述");
        LABEL_MAP.put("devManufacturer", "生产厂家");
        LABEL_MAP.put("devAddress", "设备摆放地址");

    }
    @Resource
    private IDeviceInfoService deviceInfoService;


    @Override
    public String resourceType() {
        return MroConstant.DEVICE;
    }

    @Override
    public boolean isAutoCommit(String methodName, Object[] args) {
        return MroConstant.DEVICE_CREATE_METHOD.equals(methodName);
    }

    @Override
    public List<Approval.Property> getProperties(Object resource) {
        LinkedList<Approval.Property> properties = new LinkedList<>();
        DeviceInfoVO deviceInfoVO = (DeviceInfoVO) resource;
        DeviceInfo deviceInfo = deviceInfoService.getById(deviceInfoVO.getId());
        if (deviceInfo != null) {
            if (DeviceTorsionStatusEnums.DEBUGGING.equals(deviceInfo.getDevTorsionStatus())) {
                properties.add(new Approval.Property("devTorsionStatus", "设备状态", "调试中", "正常"));
            }
            Field[] beforeFields = deviceInfo.getClass().getDeclaredFields();
            for (Field afterField : deviceInfoVO.getClass().getDeclaredFields()) {
                String fieldName = afterField.getName();
                List<Field> fieldList = Arrays.stream(beforeFields)
                        .filter((field -> Objects.equals(field.getName(), fieldName)))
                        .collect(Collectors.toList());
                if (fieldList.size() > 1) {
                    throw ExceptionFactory.internalServerError();
                } else if (fieldList.size() < 1) {
                    continue;
                }

                try {
                    Field beforeField = fieldList.get(0);
                    beforeField.setAccessible(true);
                    Object beforeValue = beforeField.get(deviceInfo);
                    afterField.setAccessible(true);
                    Object afterValue = afterField.get(deviceInfoVO);
                    if (!Objects.equals(beforeValue, afterValue) && LABEL_MAP.containsKey(fieldName)) {
                        properties.add(new Approval.Property(fieldName, getLabel(fieldName), toString(fieldName, beforeValue), toString(fieldName, afterValue)));
                    }
                } catch (IllegalAccessException e) {
                    throw ExceptionFactory.internalServerError(e.getMessage());
                }
            }
        }

        return properties;
    }
    /**
     * 审核中回调
     * @param approval 审核记录
     */
    @Override
    public void pending(Approval approval) {
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setId(approval.getResourceId());
        deviceInfo.setDevTorsionStatus(DeviceTorsionStatusEnums.SUBMITTED);
        deviceInfoService.updateDeviceInfo(approval.getResourceId(),deviceInfo);
    }
    /**
     * 审核通过的操作
     * @param approval
     */
    @Override
    public void approved(Approval approval) {
        DeviceInfo deviceInfo = new DeviceInfo();
        if (MroConstant.DEVICE_UPDATE_REVIEW.equals(approval.getApprovalType())) {
            Object[] args = approval.getSnapshot().getArgs();
            try {
                UpdateDeviceInfoForm updateDeviceInfoForm = JSON.parseObject(args[0].toString(), UpdateDeviceInfoForm.class);
                BeanUtils.copyProperties(updateDeviceInfoForm, deviceInfo);
            } catch (Exception e) {
                log.error("审核转换对象失败[Reason]: {}", e.getMessage());
            }
        }
        deviceInfo.setId(approval.getResourceId());
        deviceInfo.setDevTorsionStatus(DeviceTorsionStatusEnums.NORMAL_STATUS);
        deviceInfoService.updateDeviceInfo(approval.getResourceId(), deviceInfo);
    }

    /**
     * 审核失败的回调
     * @param approval
     */

    @Override
    public void rejected(Approval approval) {
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setId(approval.getResourceId());
        DeviceTorsionStatusEnums deviceTorsionStatus = null;
        if (MroConstant.DEVICE_UPDATE_REVIEW.equals(approval.getApprovalType())) {
            Object[] args = approval.getSnapshot().getArgs();
            try {
                UpdateDeviceInfoForm updateDeviceInfoForm = JSON.parseObject(args[0].toString(), UpdateDeviceInfoForm.class);
                deviceTorsionStatus = updateDeviceInfoForm.getDevTorsionStatus();
            } catch (Exception e) {
                log.error("审核转换对象失败[Reason]: {}", e.getMessage());
            }
        }
        deviceTorsionStatus = deviceTorsionStatus == null ? DeviceTorsionStatusEnums.DEBUGGING : deviceTorsionStatus;
        deviceInfo.setDevTorsionStatus(deviceTorsionStatus);
        deviceInfoService.updateDeviceInfo(approval.getResourceId(), deviceInfo);
    }

    @Override
    public Map<String, String> extraColumns(Object resource) {
        DeviceInfoVO deviceInfoVO = (DeviceInfoVO) resource;
        DeviceInfo deviceInfo = deviceInfoService.selectDeviceInfo(deviceInfoVO.getId());
        Map<String, String> map = new LinkedHashMap<>();
        //设备编码
        map.put("devCode", deviceInfo.getDevCode());
        //设备名称
        map.put("devName", deviceInfo.getDevName());
        //设备名称
        map.put("devModel", deviceInfo.getDevModel());
        //设备类型
        map.put("devType", deviceInfo.getDevType());
        return map;
    }

    private String toString(String fieldName, Object object) {
        String stringValue;
        switch (fieldName) {
            case "devDateProduction":
                Date date = (Date) object;
                stringValue = DateUtil.format(date, Pattern.DATE);
                break;
            default:
                stringValue = ObjectUtil.isNotEmpty(object) ? object.toString() : null;
                break;
        }
        return stringValue;
    }

    private String getLabel(String fieldName) {
        return LABEL_MAP.get(fieldName);
    }
}
