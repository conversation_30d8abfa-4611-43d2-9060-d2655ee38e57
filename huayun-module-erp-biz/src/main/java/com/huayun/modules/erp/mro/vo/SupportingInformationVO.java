package com.huayun.modules.erp.mro.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.erp.mro.constant.enums.ToolUseTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SupportingInformationVO {
    /**
     * 工具编码
     */
    @ApiModelProperty(value = "工具编码")
    private String toolCode;
    /**
     * 工具名称
     */
    @ApiModelProperty(value = "工具名称")
    private String toolName;
    /**
     * 工具型号
     */
    @ApiModelProperty(value = "工具型号")
    private String toolType;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String toolSequenceNum;
    /**
     * 工具生产厂家
     */
    @ApiModelProperty(value = "工具生产厂家")
    private String toolManufacturer;
    /**
     * 工具出厂日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "工具出厂日期")
    private java.util.Date toolDateProduction;
    /**
     * 工具功能描述
     */
    @ApiModelProperty(value = "工具功能描述")
    private String toolFunDescription;
    /**
     * 工具功能·类型名称
     */
    @ApiModelProperty(value = "工具功能·类型名称")
    private String toolFunTypeName;
    /**
     * 工具使用类型名称
     */
    @ApiModelProperty(value = "工具使用类型名称")
    private ToolUseTypeEnums toolUseTypeName;
    /**
     * 工具归属部门
     */
    @ApiModelProperty(value = "工具归属部门")
    private String toolDepartment;
    /**
     * 配套设备
     */
    @ApiModelProperty(value = "配套设备")
    private List<DeviceInfoModelVO> toolClassification;
    /**
     * 工具使用单价
     */
    @ApiModelProperty(value = "工具使用单价")
    private String toolUsePrice;
}
