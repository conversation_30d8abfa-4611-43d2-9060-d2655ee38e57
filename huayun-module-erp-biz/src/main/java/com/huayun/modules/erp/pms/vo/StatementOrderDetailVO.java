package com.huayun.modules.erp.pms.vo;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.constant.Pattern;
import com.huayun.modules.erp.mms.entity.Employee;
import com.huayun.modules.erp.oams.constant.enums.StatementDetailTypeEnum;
import com.huayun.modules.erp.oams.util.CurrencySymbolUtil;
import com.huayun.modules.erp.oams.util.CurrencyUtils;
import com.huayun.modules.erp.oms.constant.enums.CollectionStateEnum;
import com.huayun.modules.erp.oms.entity.CustomizedProduct;
import com.huayun.modules.erp.oms.entity.OrderDeliveryItem;
import com.huayun.modules.erp.pms.entity.OtherFee;
import com.huayun.modules.erp.pms.entity.model.StatementOrderDeliveryModel;
import com.huayun.modules.erp.pms.entity.model.StatementOrderModel;
import com.huayun.modules.erp.pms.entity.model.StatementOrderProductModel;
import com.huayun.modules.erp.pms.entity.model.StatementOrderReceiptModel;
import com.huayun.modules.erp.pms.enums.OrderPayStateEnum;
import com.huayun.modules.erp.wms.utils.BusinessUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName StatementOrderDetailVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/18 18:07
 */
@Data
@NoArgsConstructor
public class StatementOrderDetailVO {
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @Excel(name = "序号", width = 15)
    private String no;

    /**
     * 采购或委外开始日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.DATE)
    @DateTimeFormat(pattern = Pattern.DATE)
    @ApiModelProperty(value = "采购或委外开始日期")
    private Date orderDate;

    /**
     * 采购单id
     */
    @ApiModelProperty(value = "采购单id")
    private String orderId;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商或客户id")
    private String supplier;

    @ApiModelProperty(value = "供应商或客户名称")
    @Excel(name = "客户名称", width = 15)
    private String supplierName;

    @ApiModelProperty(value = "客户负责人")
    @Excel(name = "客户负责人", width = 15)
    private String supervisorNames;

    /**
     * 结算方式id
     */
    @ApiModelProperty(value = "结算方式id")
    private String settlementMethodId;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @Excel(name = "结算方式", width = 15)
    private String settlementMethod;
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    @Excel(name = "订单单号", width = 15)
    private String orderNo;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @Excel(name = "产品编码", width = 15)
    private String materialCode;

    @ApiModelProperty(value = "客户料号")
    @Excel(name = "客户料号", width = 15)
    private String aliasCode;



    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @Excel(name = "产品名称", width = 15)
    private String materialName;

    @ApiModelProperty(value = "客户品名")
    @Excel(name = "客户品名", width = 15)
    private String aliasName;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    @Excel(name = "规格", width = 15)
    private String specification;


    /**
     * 型号
     */
    @ApiModelProperty(value = "型号")
    @Excel(name = "型号", width = 15)
    private String model;


    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    @Excel(name = "主单位", width = 15)
    private String materialUnit;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @Excel(name = "币种", width = 15)
    private String currency;

    /**
     * 请购量
     */
    @ApiModelProperty(value = "采购量")
    @Excel(name = "订单数量", numFormat = "0.00",width = 15)
    private BigDecimal requestCount;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @Excel(name = "税率", numFormat = "0.00", width = 15)
    private BigDecimal taxRate;

    /**
     * 不含税单价
     */
    @ApiModelProperty(value = "单价")
    @Excel(name = "单价", numFormat = "0.0000",width = 15)
    private BigDecimal price;

    /**税额*/
    @ApiModelProperty("税额")
    @Excel(name = "税额", numFormat = "0.0000",width = 15)
    private java.math.BigDecimal taxAmount;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @Excel(name = "金额", numFormat = "0.0000",width = 15)
    private BigDecimal totalAmount;

    /**
     * 含税单价
     */
    @ApiModelProperty(value = "含税单价")
    @Excel(name = "含税单价", numFormat = "0.0000",width = 15)
    private BigDecimal taxPrice;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    private BigDecimal totalTaxAmount;

    /**
     * 入库日期
     */
    @ApiModelProperty(value = "入库日期")
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.DATE)
    @DateTimeFormat(pattern = Pattern.DATE)
    private Date receiptTime;

    /**
     * 入库单号
     */
    @ApiModelProperty(value = "入库单号")
    private String receiptCode;

    /**
     * 入库量
     */
    @ApiModelProperty(value = "入库量")
    private BigDecimal receiptQuantity;

    /**
     * 入库金额
     */
    @ApiModelProperty(value = "入库金额")
    private BigDecimal receiptAmount;
    /**
     * 出库日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.DATE)
    @DateTimeFormat(pattern = Pattern.DATE)
    @ApiModelProperty(value = "出库日期")
    @Excel(name = "出库日期", format = "yyyy-MM-dd", width = 15)
    private Date deliveryTime;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    @Excel(name = "出库/退货单号", width = 15)
    private String deliveryCode;

    /**
     * 出库量
     */
    @ApiModelProperty(value = "出库量")
    @Excel(name = "交付/退货量", numFormat = "0.00",width = 15)
    private BigDecimal deliveryQuantity;

    /**
     * 出库金额
     */
    @ApiModelProperty(value = "出库金额")
    @Excel(name = "含税金额", numFormat = "0.0000",width = 15)
    private BigDecimal deliveryAmount;

    /**
     * 来料退货金额
     */
    @ApiModelProperty(value = "来料退货金额")
    private BigDecimal returnIncomingAmount;

    /**
     * 库存明细id
     */
    @ApiModelProperty(value = "库存明细id")
    private String inventoryLogId;

    /**付款状态*/
    @ApiModelProperty(value = "付款状态")
    private OrderPayStateEnum payState;

    /**收款状态*/
    @ApiModelProperty(value = "收款状态")
    private CollectionStateEnum collectionState;


    /**可请款*/
    @ApiModelProperty(value = "付款状态")
    private boolean canPay;

    /**
     * 关联财务单号  用于对账请款/收款申请
     */
    @ApiModelProperty(value = "关联财务单号")
    private String financeCode;

    /**本位币单价*/
    @ApiModelProperty(value = "本位币单价")
    private BigDecimal localPrice;

    /**本位币总金额*/
    @ApiModelProperty(value = "本位币总金额")
    private BigDecimal localAmount;

    @ApiModelProperty(value = "本位币交付金额")
    private BigDecimal localDeliveryAmount;

    @ApiModelProperty(value = "本位币入库金额")
    private BigDecimal localReceiptAmount;

    @ApiModelProperty("货币符号")
    private String currencySymbol;


    /**
     * 含税单价
     */
    @ApiModelProperty(value = "格式化含税单价(导出用)")
    private String formatTaxPrice;


    /**
     * 含税金额
     */
    @ApiModelProperty(value = "格式化含税金额(导出用)")
    private String formatTotalTaxAmount;

    /**
     * 入库金额
     */
    @ApiModelProperty(value = "格式化入库金额")
    private String formatReceiptAmount;

    @ApiModelProperty("其他费用")
    List<OtherFee> otherFees;

    /**物料副单位*/
    @ApiModelProperty("物料副单位")
    @Excel(name = "辅助单位", width = 15)
    private String secondUnit;
    /**辅单位采购数量*/
    @ApiModelProperty("辅单位交付数量")
    private BigDecimal secondCount;
    @ApiModelProperty("辅单位订单数量")
    @Excel(name = "订单数量(辅)", width = 15)
    private BigDecimal secondRequestCount;

    @ApiModelProperty("辅单位含税单价")
    private BigDecimal secondTaxPrice;




    @ApiModelProperty(value = "已对账数量")
    private BigDecimal yetStatementQuantity;

    @ApiModelProperty(value = "本次对账数量")
    private BigDecimal statementQuantity;

    @ApiModelProperty("是否寄售")
    private Boolean isConsignment;
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("是否赠送")
    private Boolean isGift;


    /**单位换算率*/
    @ApiModelProperty(value = "单位换算率")
    private BigDecimal unitConvertRate;

    @ApiModelProperty(value = "(辅助单位)入库/退货数量")
    @Excel(name = "交付/退货数量(辅)", width = 15)
    private BigDecimal secondReceiptQuantity;

    @ApiModelProperty(value = "(辅助单位)单价")
    private BigDecimal secondPrice;

    @ApiModelProperty(value = "对账明细类型")
    private StatementDetailTypeEnum statementDetailType;

    /**
     * 开票数量
     */
    @ApiModelProperty(value = "开票数量")
    @Excel(name = "已开票数量", width = 15, numFormat = "0.00")
    private  BigDecimal invoiceQuantity = BigDecimal.ZERO;

    /**
     * 预付金额
     */
    @ApiModelProperty(value = "已预付金额")
    private BigDecimal prepaidAmount;

    /**
     * 已申请的预付金额
     */
    private BigDecimal appliedPrepayAmount;

    @ApiModelProperty(value = "出库明细id")
    private String taskDetailId;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;






    public StatementOrderDetailVO(StatementOrderModel orderModel, StatementOrderProductModel statementOrderProductModel, StatementOrderReceiptModel statementOrderReceiptModel) {
        BeanUtils.copyProperties(statementOrderReceiptModel,this);
        BeanUtils.copyProperties(orderModel,this);
        BeanUtils.copyProperties(statementOrderProductModel,this);
        // 其他费用
        this.otherFees = statementOrderReceiptModel.getOtherFees();
        if (this.otherFees != null && !this.otherFees.isEmpty()) {
            // 计算总金额
            this.receiptAmount = this.otherFees.stream().map(OtherFee::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (statementOrderReceiptModel.getReceiptTaxPrice() != null){
            this.taxPrice = CurrencyUtils.localToCurrent(statementOrderReceiptModel.getReceiptTaxPrice(),this.exchangeRate,true) ;
            this.taxRate = statementOrderReceiptModel.getReceiptTaxRate();
            this.taskDetailId = statementOrderReceiptModel.getTaskDetailId();
        }

        // 不含税入库金额
        if (taxPrice != null && receiptQuantity != null) {
            calculateAmount(receiptQuantity);
        }
        
        //退货与付状态无关
        if (this.receiptQuantity.compareTo(BigDecimal.ZERO)<0){
            this.canPay = ObjectUtil.isEmpty(financeCode);
        }else {
            //未生成对账 且不是全款预付 这样可以找到发货后（已对账）再退货（已对账）再发货（未对账）的数据
            this.canPay =  ObjectUtil.isEmpty(financeCode)
                    && NumberUtil.null2Zero(this.prepaidAmount) .compareTo(NumberUtil.null2Zero(orderModel.getOrderTotalTaxAmount())) < 0;
        }
        this.currencySymbol = CurrencySymbolUtil.getCurrencySymbol(this.currency) ;
        //辅助单位  入库有的取入库的，入库没有的取采购单的
        if (!ObjectUtil.isAllEmpty(statementOrderProductModel.getSecondUnit(), statementOrderReceiptModel.getSecondUnit()) ) {

            if (ObjectUtil.isNotEmpty(statementOrderReceiptModel.getSecondUnit())&& this.secondReceiptQuantity.signum()>0){
                // statementOrderReceiptModel.getSecondReceiptQuantity()这里是入库辅助数量
                // 得算一下辅助单位的上架数量
                this.secondUnit = statementOrderReceiptModel.getSecondUnit();
                BigDecimal receiptUnitConvertRate = statementOrderReceiptModel.getUnitConvertRate();
                this.secondReceiptQuantity = BusinessUtils.calculateSecondValue(this.receiptQuantity,receiptUnitConvertRate);
                //this.unitConvertRate = this.receiptQuantity.divide(this.secondReceiptQuantity,8, RoundingMode.HALF_UP);
                //转化率采用采购单的转化率
                this.unitConvertRate = statementOrderProductModel.getUnitConvertRate();
                this.taxPrice = BusinessUtils.calculateSecondPrice(this.taxPrice,this.unitConvertRate);

            }else{
                this.secondUnit = statementOrderProductModel.getSecondUnit();
                this.unitConvertRate = statementOrderProductModel.getUnitConvertRate();
                this.secondReceiptQuantity = BusinessUtils.calculateSecondValue(this.receiptQuantity,this.unitConvertRate);
            }
            calculateAmount(this.secondReceiptQuantity);
        }

    }

    private void calculateAmount(BigDecimal quantity) {
        //单价 = 含税单价 /（1+ 税率）
        this.price = this.taxPrice.divide(BigDecimal.ONE.add(this.taxRate.divide(new BigDecimal("100"),8,RoundingMode.HALF_UP)),6, RoundingMode.HALF_UP);

        // 计算总金额 = 含税单价 * 数量 / (1 + 税率)
        this.totalAmount = this.taxPrice.multiply(quantity)
                .divide(BigDecimal.ONE.add(this.taxRate.divide(new BigDecimal("100"), 8, RoundingMode.HALF_UP)), 8, RoundingMode.HALF_UP)
                .setScale(4, RoundingMode.HALF_UP);

        // 计算税额 = 含税单价 * 数量 - 含税单价 * 数量 / (1 + 税率)
        this.taxAmount = this.taxPrice.multiply(quantity)
                .subtract(this.taxPrice.multiply(quantity)
                        .divide(BigDecimal.ONE.add(this.taxRate.divide(new BigDecimal("100"), 8, RoundingMode.HALF_UP)), 8, RoundingMode.HALF_UP))
                .setScale(4, RoundingMode.HALF_UP);

        // 计算总的含税金额  = 含税单价 * 数量
        this.totalTaxAmount = this.taxPrice.multiply(quantity)
                .setScale(4, RoundingMode.HALF_UP);
        //todo: 其他费用暂时不维护
        this.receiptAmount = this.totalTaxAmount;


    }


    public StatementOrderDetailVO(StatementOrderModel orderModel, StatementOrderProductModel statementOrderProductModel, StatementOrderDeliveryModel statementOrderDeliveryModel, CustomizedProduct customizedProduct, List<Employee> employeeList, OrderDeliveryItem deliveryItem) {

        BeanUtils.copyProperties(statementOrderDeliveryModel,this);
        //单价、税率信息在这
        BeanUtils.copyProperties(statementOrderProductModel,this);
        //数量、金额信息在这
        BeanUtils.copyProperties(orderModel,this);
        this.isGift = statementOrderDeliveryModel.getIsGift();
        this.exchangeRate = ObjectUtil.defaultIfNull(orderModel.getExchangeRate(),BigDecimal.ONE) ;

        if (customizedProduct != null) {
            this.aliasCode = customizedProduct.getAliasCode();
            this.aliasName = customizedProduct.getAliasName();
        }
        if (ObjectUtil.isNotEmpty(employeeList)){
            this.supervisorNames = orderModel.getSupervisorIdList().stream().map(item-> employeeList.stream().filter(row -> row.getId().equals(item)).findFirst().map(Employee::getRealName).orElse(null)).filter(StringUtils::isNotEmpty).collect(Collectors.joining("、"));
//            this.supervisorNames = employeeList.stream()
//                    .filter(item-> CollUtil.contains(orderModel.getSupervisorIdList(),item.getId()))
//                    .map(Employee::getRealName).collect(Collectors.joining("、"));
        }

        this.deliveryTime = statementOrderDeliveryModel.getDeliveryDate();
        this.deliveryCode = statementOrderDeliveryModel.getDeliveryOrderNo();
        //可以生成对账单 与订单收款状态无关 为了兼容退货补发的情况
        if (BooleanUtil.isTrue(isGift) ){
            //赠品和收款状态无关
            this.canPay = ObjectUtil.isEmpty(financeCode);
        }else {
            //非赠品未预付满就可用生对账单
            this.canPay = ObjectUtil.isEmpty(financeCode)
                    &&(NumberUtil.null2Zero( this.totalTaxAmount).compareTo(NumberUtil.null2Zero(this.prepaidAmount)) > 0);
        }

        if (ObjectUtil.isNotEmpty(deliveryItem)){
            if (ObjectUtil.isNotEmpty(deliveryItem.getOrderTaxPrice())){
                this.taxPrice = deliveryItem.getOrderTaxPrice();
                this.taxRate = ObjectUtil.defaultIfNull(deliveryItem.getTaxRate(),BigDecimal.ZERO) ;
            }
            this.remark = deliveryItem.getRemark();
            this.taskDetailId = deliveryItem.getId();
        }

        // 不含税入库金额
        if (price != null && deliveryQuantity != null) {
            this.calculateAmount(this.deliveryQuantity);
            this.deliveryAmount = this.totalTaxAmount;
            //this.totalAmount = this.price.multiply(this.deliveryQuantity).setScale(2, RoundingMode.HALF_UP);
           // this.taxAmount = this.deliveryAmount.subtract(this.totalAmount);
        }
//        if (Boolean.TRUE.equals(isGift)) {
//            // 赠品订单金额为0
//            this.totalAmount = BigDecimal.ZERO;
//            this.taxAmount = BigDecimal.ZERO;
//            this.deliveryAmount = BigDecimal.ZERO;
//        }



        this.secondUnit = null;
        if (StringUtils.isNotEmpty(statementOrderProductModel.getSecondUnit())
                && statementOrderProductModel.getUnitConvertRate() != null
                && statementOrderProductModel.getUnitConvertRate().compareTo(BigDecimal.ZERO) > 0 
                && deliveryQuantity != null) {
            // 有辅助单位，下发的时候保存辅助单位信息，这里的辅助单位不考虑小数的情况，默认保留4为小数，四舍五入
            this.setSecondUnit(statementOrderProductModel.getSecondUnit());
            this.setSecondCount(deliveryQuantity.divide(statementOrderProductModel.getUnitConvertRate(), 4, RoundingMode.HALF_UP));
            this.setSecondReceiptQuantity(deliveryQuantity.divide(statementOrderProductModel.getUnitConvertRate(), 4, RoundingMode.HALF_UP));
            if (requestCount != null) {
                this.setSecondRequestCount(requestCount.divide(statementOrderProductModel.getUnitConvertRate(), 4, RoundingMode.HALF_UP));
            }
            this.setSecondPrice(this.totalAmount.divide(this.secondReceiptQuantity, 6, RoundingMode.HALF_UP));
            this.setSecondTaxPrice(this.totalTaxAmount.divide(this.secondReceiptQuantity, 6, RoundingMode.HALF_UP));
        }



        if (BooleanUtil.isTrue(isGift) ){
            this.price = BigDecimal.ZERO;
            this.taxPrice = BigDecimal.ZERO;
            this.localPrice = BigDecimal.ZERO;
            this.secondPrice = BigDecimal.ZERO;
            this.secondTaxPrice = BigDecimal.ZERO;
            this.totalAmount = BigDecimal.ZERO;
            this.totalTaxAmount = BigDecimal.ZERO;
            this.taxAmount = BigDecimal.ZERO;
            this.deliveryAmount = BigDecimal.ZERO;
            this.receiptAmount = BigDecimal.ZERO;
        }
        this.statementQuantity = this.deliveryQuantity;
        this.currencySymbol = CurrencySymbolUtil.getCurrencySymbol(this.currency) ;
    }
}
