package com.huayun.modules.erp.oams.controller.form;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.entity.UploadFile;
import com.huayun.modules.common.mybatis.UploadFileHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * 内部转账表
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@Data
public class InternalTransferForm {


    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "日期")
    @NotNull(message = "日期不能为空")
    private java.util.Date bookDate;
    /**
     * 转入账户id
     */
    @ApiModelProperty(value = "转入账户id")
    @NotEmpty(message = "转入账户id不能为空")
    private String inAccountId;
    /**
     * 转入账户编码
     */
    @ApiModelProperty(value = "转入账户编码")
    private String inAccountCode;
    /**
     * 转入账户名称
     */
    @ApiModelProperty(value = "转入账户名称")
    @NotEmpty(message = "转入账户名称不能为空")
    private String inAccountName;

    /**
     * 转出账户id
     */
    @ApiModelProperty(value = "转出账户id")
    @NotEmpty(message = "转出账户id不能为空")
    private String outAccountId;
    /**
     * 转出账户编码
     */
    @ApiModelProperty(value = "转出账户编码")
    private String outAccountCode;
    /**
     * 转出账户名称
     */
    @ApiModelProperty(value = "转出账户名称")
    @NotEmpty(message = "转出账户名称不能为空")
    private String outAccountName;


    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String abstracts;
    /**
     * 金额 收入-支出
     */
    @ApiModelProperty(value = "金额 收入-支出")
    @Positive(message = "金额必须大于等于0")
    @NotNull(message = "金额不能为空")
    private java.math.BigDecimal amount;
    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String departmentId;
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 附件
     */
    @TableField(typeHandler = UploadFileHandler.class)
    @ApiModelProperty(value = "附件 回单")
    private List<UploadFile> attachments;

    /**
     * 凭证id
     */
    @ApiModelProperty(value = "凭证id")
    private String voucherId;

    public void setAttachments(List<UploadFile> attachments) {
        this.attachments = CollUtil.emptyIfNull(attachments);
    }
}
