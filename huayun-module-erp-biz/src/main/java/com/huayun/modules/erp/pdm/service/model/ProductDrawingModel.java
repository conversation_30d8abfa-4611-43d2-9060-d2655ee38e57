package com.huayun.modules.erp.pdm.service.model;

import com.huayun.modules.common.vo.BaseVO;
import com.huayun.modules.erp.pdm.constant.enums.product.ProductDrawingTypeEnum;
import com.huayun.modules.erp.pdm.constant.enums.product.ProductStateEnum;
import com.huayun.modules.erp.pdm.entity.ProductDrawingFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/7/18
 */
@Data
public class ProductDrawingModel extends BaseVO {
    /**
     * 图纸目录id
     */
    @ApiModelProperty(value = "图纸目录id")
    private String id;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;


    /**
     * 图纸库编码
     */
    @ApiModelProperty(value = "图纸库编码")
    private String libraryCode;


    /**
     * 图纸库名称
     */
    @ApiModelProperty(value = "图纸库名称")
    private String libraryName;


    /**
     * 图纸库类型
     */
    @ApiModelProperty(value = "图纸库类型")
    private ProductDrawingTypeEnum drawingType;

    /**
     * 图纸文件列表
     */
    @ApiModelProperty(value = "图纸文件列表")
    List<ProductDrawingFile> fileList;

    /**
     * 产品状态
     */
    @ApiModelProperty(value = "产品状态")
    private ProductStateEnum state;
}
