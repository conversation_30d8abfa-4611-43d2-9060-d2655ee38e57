package com.huayun.modules.erp.mms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huayun.modules.erp.mms.entity.handler.WorkTimeTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 班组
 *
 * <AUTHOR>
 * @date 2022/07/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class WorkGroup extends Organization {
    /**
     * 工作时间
     */
    @ApiModelProperty(value = "工作时间")
    @TableField(typeHandler = WorkTimeTypeHandler.class)
    private TimePeriod workTime;
    /**
     * 休息时间
     */
    @ApiModelProperty(value = "休息时间")
    @TableField(typeHandler = WorkTimeTypeHandler.class)
    private List<TimePeriod> restTime;
}
