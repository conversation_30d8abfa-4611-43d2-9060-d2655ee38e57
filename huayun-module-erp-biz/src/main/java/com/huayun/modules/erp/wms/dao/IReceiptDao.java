package com.huayun.modules.erp.wms.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huayun.modules.erp.wms.constant.enums.receipt.ReceiptTypeEnum;
import com.huayun.modules.erp.wms.dto.WarehouseVisualItemDTO;
import com.huayun.modules.erp.wms.entity.Receipt;
import com.huayun.modules.erp.wms.entity.ReceiptItem;
import com.huayun.modules.erp.wms.entity.model.DashBoardModel;
import com.huayun.modules.erp.wms.entity.model.ReceiptModel;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 入库单据管理
 *
 * <AUTHOR>
 * @date 2022/9/9 10:07
 **/
public interface IReceiptDao extends IService<Receipt> {

    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:20
     * @param page
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    Page<ReceiptModel> pageList(Page<Receipt> page, QueryWrapper<Receipt> queryWrapper, Boolean isSelectMaterial);



    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:20
     * @param
     * @param queryWrapper
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    List<ReceiptModel> selectList(QueryWrapper<Receipt> queryWrapper);

    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:20
     * @param page
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    Page<ReceiptModel> pageListQuery(Page<Receipt> page, QueryWrapper<Receipt> queryWrapper, Boolean isSelectMaterial);

    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:20
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    List<ReceiptModel> exportList(QueryWrapper<Receipt> queryWrapper, Boolean isSelectMaterial);

    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:20
     * @param page
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    Page<ReceiptModel> pageListOutsourcing(Page<Receipt> page, QueryWrapper<Receipt> queryWrapper, Boolean isSelectMaterial);

    /**
     * 根据id修改单据
     *
     * <AUTHOR>
     * @date 2023/9/12 17:22
     * @param receipt
     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    void updateReceiptById(Receipt receipt);

    /**
     * 获取仓储可视化信息
     *
     * @param wrapper
     * @param sumFiled
     * @return com.huayun.modules.erp.wms.dto.WarehouseVisualDTO
     * <AUTHOR>
     * @date 2023/2/20 11:07
     */
    WarehouseVisualItemDTO findWarehouseVisualReceipts(QueryWrapper<Receipt> wrapper, String sumFiled);


    /**
     * 查询库存面板入库情况
     *
     * @param receiptType 入库类型
     * @return com.huayun.modules.erp.wms.entity.model.DashBoardModel.ShortcutMenu
     * <AUTHOR>
     * @date 2023/6/14 10:21
     */
    DashBoardModel.ShortcutMenu getShotcutMenuInfo(String receiptType);

    /**
     * 查询库存面板入库情况
     *
     * @param receiptType 入库类型
     * @return com.huayun.modules.erp.wms.entity.model.DashBoardModel.ShortcutMenu
     * <AUTHOR>
     * @date 2023/6/14 10:21
     */
    DashBoardModel.ShortcutMenu selectShotcutMenuInfo(String receiptType);

    Page<ReceiptModel> pageList(Page<Receipt> page, QueryWrapper<Receipt> queryWrapper, Boolean isSelectMaterial, String categoryId, String categoryName);

    /**
     * 统计入库数量
     * @param returnTypeEnum
     * @param beginDate
     * @param endDate
     * @return
     */
    ReceiptItem sumStorageCount(ReceiptTypeEnum returnTypeEnum, Date beginDate, Date endDate);
}
