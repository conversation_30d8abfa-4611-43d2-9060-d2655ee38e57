package com.huayun.modules.erp.wms.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.constant.Pattern;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 新增盘点物料标签
 *
 * <AUTHOR>
 * @date 2023/6/27 13:37
 **/
@Data
public class CreateInventoryCountItemForm {

    /**物料id*/
    @ApiModelProperty(value = "物料id")
    @NotBlank(message = "物料id不可为空")
    private String materialId;

    /**物料编码*/
    @ApiModelProperty(value = "物料编码")
    @NotBlank(message = "物料编码不可为空")
    private String materialCode;

    /**货位id*/
    @ApiModelProperty(value = "货位id")
    @NotBlank(message = "货位id不可为空")
    private String locationId;

    /**供应商id*/
    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    /**存货价值*/
    @ApiModelProperty(value = "存货价值")
    @Positive(message = "物料单价必须大于0")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "入库日期")
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.DATE)
    @DateTimeFormat(pattern = Pattern.DATE)
    private Date receiptTime;
}
