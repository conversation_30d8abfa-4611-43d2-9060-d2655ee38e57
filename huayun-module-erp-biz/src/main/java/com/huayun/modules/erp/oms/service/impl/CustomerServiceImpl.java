package com.huayun.modules.erp.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.huayun.modules.common.approval.entity.Approval;
import com.huayun.modules.common.auth.context.SessionContext;
import com.huayun.modules.common.constant.CommonConstant;
import com.huayun.modules.common.entity.BaseEntity;
import com.huayun.modules.common.entity.DictData;
import com.huayun.modules.common.entity.UploadFile;
import com.huayun.modules.common.enums.YesOrNOEnum;
import com.huayun.modules.common.log.constant.OperateTypeConstant;
import com.huayun.modules.common.log.dao.IOperationLogDao;
import com.huayun.modules.common.log.entity.OperationLog;
import com.huayun.modules.common.log.service.IOperationLogService;
import com.huayun.modules.common.log.util.LogUtils;
import com.huayun.modules.common.service.IDictDataService;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.erp.auth.dao.TenantUserDAO;
import com.huayun.modules.erp.auth.entity.TenantUser;
import com.huayun.modules.erp.mms.dao.EmployeeDAO;
import com.huayun.modules.erp.mms.dao.OrganizationDAO;
import com.huayun.modules.erp.mms.entity.Employee;
import com.huayun.modules.erp.mms.entity.Organization;
import com.huayun.modules.erp.mms.service.IEmployeeService;
import com.huayun.modules.erp.mms.service.IOrganizationService;
import com.huayun.modules.erp.oams.dao.CollectionDAO;
import com.huayun.modules.erp.oms.constant.OMSConstant;
import com.huayun.modules.erp.oms.constant.enums.ContractStateEnum;
import com.huayun.modules.erp.oms.constant.enums.CustomerFileTypeEnum;
import com.huayun.modules.erp.oms.constant.enums.CustomerPropertyTypeEnum;
import com.huayun.modules.erp.oms.constant.enums.CustomerStateEnum;
import com.huayun.modules.erp.oms.dao.ICustomerDao;
import com.huayun.modules.erp.oms.dao.IOrderDeliveryDao;
import com.huayun.modules.erp.oms.entity.*;
import com.huayun.modules.erp.oms.entity.model.AreaModel;
import com.huayun.modules.erp.oms.entity.model.CustomerAndFileModel;
import com.huayun.modules.erp.oms.entity.model.CustomerContactModel;
import com.huayun.modules.erp.oms.form.*;
import com.huayun.modules.erp.oms.mapper.CustomerMapper;
import com.huayun.modules.erp.oms.query.CustomerQuery;
import com.huayun.modules.erp.oms.service.*;
import com.huayun.modules.erp.oms.vo.ContractVO;
import com.huayun.modules.erp.oms.vo.CustomerExportVO;
import com.huayun.modules.erp.pms.entity.OtherFee;
import com.huayun.modules.erp.utils.ImportError;
import com.huayun.modules.erp.utils.ProcessUtils;
import com.huayun.modules.erp.utils.ValidationUtils;
import com.huayun.modules.erp.utils.validation.FieldChange;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 客户
 * @Author: huayun
 * @Date: 2022-07-12
 * @Version: V1.0
 */
@Service
public class CustomerServiceImpl implements ICustomerService {

    @Resource
    private ICustomerDao iCustomerDao;

    @Resource
    private ICustomerFileInfoService iCustomerFileInfoService;

    @Resource
    private ICustomerDeliveryAddressInfoService iCustomerDeliveryAddressInfoService;

    @Resource
    private ICustomerBankInfoService iCustomerBankInfoService;

    @Resource
    private ICustomerPropertyService iCustomerPropertyService;

    @Resource
    private IDictDataService dictDataService;

    //@Resource
    //private IEmployeeService employeeService;

    @Resource
    private EmployeeDAO employeeDAO;

    @Resource
    private TenantUserDAO tenantUserDAO;
    @Resource
    private IEmployeeService employeeService;

    @Resource
    private ConverterRegistry converterRegistry;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    IContractService contractService;;
    @Resource
    IOrderService orderService;
    @Resource
    IOrderDeliveryDao orderDeliveryDao;

    private static final DefaultIdentifierGenerator GENERATOR = new DefaultIdentifierGenerator();
    @Autowired
    private CollectionDAO collectionDAO;
    @Resource
    private IOrganizationService organizationService;
    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private OrganizationDAO organizationDAO;

    @Resource
    private IOperationLogService operationLogService;
    @Resource
    private IOperationLogDao operationLogDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerAndFileModel saveCustomer(CustomerForm addCustomerForm) {
        /*客户信息新增*/
        // 新增提交后默认就是"待提交"状态，在生成审批记录后会改为"审批中"状态，审批通过后会改成"正常"状态
        CustomerStateEnum state = CustomerStateEnum.DRAFT;
        Customer customer = new Customer();
        BeanUtils.copyProperties(addCustomerForm, customer);
        customer.setState(state);
        //冗余logo字段
        List<CustomerFileInfoForm> infoFormList = addCustomerForm.getFileInfoList();
        for (CustomerFileInfoForm infoForm : infoFormList) {
            //文件类型为logo
            if (CustomerFileTypeEnum.LOGO.equals(infoForm.getFileType())) {
                UploadFile attachment = infoForm.getAttachment();
                String url = attachment.getPath();
                //将logo url设置到客户表中
                customer.setCustomerLogoUrl(url);
                break;
            }
        }
        //保存客户信息
        iCustomerDao.save(customer);

        /*附件批量保存*/
        //保存所有附件
        List<CustomerFileInfo> infoList = infoFormList.stream().map(e -> {
            CustomerFileInfo fileInfo = new CustomerFileInfo();
            BeanUtils.copyProperties(e, fileInfo);
            fileInfo.setCustomerId(customer.getId());
            return fileInfo;
        }).collect(Collectors.toList());
        //批量保存附件
        iCustomerFileInfoService.saveBatch(infoList);

        /*出货信息批量保存*/
        //保存出货信息
        List<CustomerDeliveryAddressInfoForm> addressInfoFormList = addCustomerForm.getDeliveryAddressInfoList();
        List<CustomerDeliveryAddressInfo> addressInfoList = addressInfoFormList.stream().map(e -> {
            CustomerDeliveryAddressInfo addressInfo = new CustomerDeliveryAddressInfo();
            BeanUtils.copyProperties(e, addressInfo);
            addressInfo.setCustomerId(customer.getId());
            return addressInfo;
        }).collect(Collectors.toList());
        iCustomerDeliveryAddressInfoService.saveBatch(addressInfoList);

        /*银行信息批量保存*/
        //保存银行信息
        List<CustomerBankInfoForm> bankInfoFormList = addCustomerForm.getCustomerBankInfoList();
        List<CustomerBankInfo> bankInfoList = bankInfoFormList.stream().map(e -> {
            CustomerBankInfo bankInfo = new CustomerBankInfo();
            BeanUtils.copyProperties(e, bankInfo);
            bankInfo.setCustomerId(customer.getId());
            return bankInfo;
        }).collect(Collectors.toList());
        iCustomerBankInfoService.saveBatch(bankInfoList);

        /*查询客户等级和客户行业类型*/
        //客户属性id列表
        return getCustomerAndFileModel(customer, infoList, addressInfoList, bankInfoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerAndFileModel editCustomer(CustomerForm editCustomerForm, CustomerStateEnum stateEnum) {
        /*客户信息修改*/
        //类拷贝
        Customer customer = new Customer();
        BeanUtils.copyProperties(editCustomerForm, customer);
        // 这个字段是单独更新的，这里设置为null，不更新这个字段，不然值会被清空，审核的时候form表单的null值会被转成空字符串，导致值被覆盖
        // 本来去掉表单的里面这个多余的字段就行了，但是因为可能有审批记录已经保存了，这里强制清掉这个值，@yangtao写的bug
        customer.setDeliveryRemark(null);
        customer.setState(stateEnum);
        //冗余logo字段
        List<CustomerFileInfoForm> infoFormList = editCustomerForm.getFileInfoList();
        String url = "";
        List<CustomerFileInfoForm> removeFiles = new ArrayList<>();
        CustomerFileInfoForm oldLogo = null;
        for (CustomerFileInfoForm infoForm : infoFormList) {
            //文件类型为logo
            if (CustomerFileTypeEnum.LOGO.equals(infoForm.getFileType())) {
                UploadFile attachment = infoForm.getAttachment();
                url = attachment.getPath();
                // 前端会传多个logo过来，以最后一个为准
                if (oldLogo != null) {
                    removeFiles.add(oldLogo);
                }
                oldLogo = infoForm;
            }
        }
        //将logo url设置到客户表中
        customer.setCustomerLogoUrl(url);
        //修改客户信息
        if (stateEnum.equals(CustomerStateEnum.APPROVED)) {
            Customer oldCustomer = iCustomerDao.getById(customer.getId());
            // 检测字段变更
            List<FieldChange> fieldChanges = ValidationUtils.detectChanges(oldCustomer, customer);

            // 如果有字段变更，记录到日志
            if (!fieldChanges.isEmpty()) {
                fieldChanges.forEach(e -> {
                    if ("supervisor".equals(e.getFieldName())) {
                        if (ObjectUtils.isNotEmpty(oldCustomer.getSupervisor())) {
                            e.setOldValue(employeeDAO.listByIds(oldCustomer.getSupervisor()).stream().map(Employee::getRealName).collect(Collectors.joining(",")));
                        }
                        if (ObjectUtils.isNotEmpty(customer.getSupervisor())) {
                            e.setNewValue(employeeDAO.listByIds(customer.getSupervisor()).stream().map(Employee::getRealName).collect(Collectors.joining(",")));
                        }
                    }
                });
                // 构建变更日志内容
                final List<String> fields = Arrays.asList("supervisor", "customerName", "customerLogoUrl", "area", "unifySocialCreditCodes", "legalRepresentative", "registeredCapital", "companyTel", "address", "websiteAddress", "currency");
                String changeLog = fieldChanges.stream()
                        // 没有设置别名的字段不显示
                        .filter(e -> !e.getFieldName().equals(e.getFieldAlias()))
                        .filter(e -> fields.contains(e.getFieldName()))
                        .map(FieldChange::toString)
                        .collect(Collectors.joining("\n"));

                OperationLog operationLog = operationLogService.getOperationLog(OMSConstant.RESOURCE_CUSTOMER, OperateTypeConstant.UPDATE,
                        "修改客户信息，客户编号：${customerNo}，变更内容：\n" + changeLog, customer);
                // 保存操作日志
                operationLogDao.save(operationLog);
            }
        }
        
        BizAssert.isTrue(iCustomerDao.updateById(customer), "更新失败，请稍后再试");
        

        /*附件只做新增和删除，不做更新*/
        //先将保存的所有附件删除
        LambdaQueryWrapper<CustomerFileInfo> fileInfoQueryWrapper = new LambdaQueryWrapper<CustomerFileInfo>()
                .eq(CustomerFileInfo::getCustomerId, customer.getId());
        iCustomerFileInfoService.remove(fileInfoQueryWrapper);
        //保存所有附件
        List<CustomerFileInfo> infoList = infoFormList.stream().filter(row -> !removeFiles.contains(row)).map(e -> {
            CustomerFileInfo fileInfo = new CustomerFileInfo();
            BeanUtils.copyProperties(e, fileInfo);
            fileInfo.setCustomerId(customer.getId());
            return fileInfo;
        }).collect(Collectors.toList());
        //批量保存附件
        iCustomerFileInfoService.saveBatch(infoList);

        /*客户出货信息变更*/
        //前端传输过来的出货信息列表
        List<CustomerDeliveryAddressInfoForm> addressInfoFormList = editCustomerForm.getDeliveryAddressInfoList();
        //id不为空的出货信息id列表
        List<String> idNotEmptyAddressInfoIds = new ArrayList<>();
        //出货信息列表
        List<CustomerDeliveryAddressInfo> addressInfoList = addressInfoFormList.stream().map(e -> {
            //将form类拷贝到entity类
            CustomerDeliveryAddressInfo addressInfo = new CustomerDeliveryAddressInfo();
            BeanUtils.copyProperties(e, addressInfo);
            //id为空的对象需要设置客户id做一对多关系对应
            addressInfo.setCustomerId(customer.getId());
            if (StringUtils.isNotEmpty(e.getId())) {
                //取出id不为空的对象，用于批量删除做比对
                idNotEmptyAddressInfoIds.add(addressInfo.getId());
            }
            return addressInfo;
        }).collect(Collectors.toList());
        //查询该可以下所有的出货信息列表（这个列表只查询了主键id）
        LambdaQueryWrapper<CustomerDeliveryAddressInfo> addressInfoQueryWrapper = new LambdaQueryWrapper<CustomerDeliveryAddressInfo>()
                .eq(CustomerDeliveryAddressInfo::getCustomerId, customer.getId())
                .select(CustomerDeliveryAddressInfo::getId);
        //取出所有已有id
        List<String> addressIds = iCustomerDeliveryAddressInfoService.list(addressInfoQueryWrapper)
                .stream()
                .map(CustomerDeliveryAddressInfo::getId)
                .collect(Collectors.toList());
        //将数据库中已有的id和前端传输过来的id对比，过滤出前端没传的id，说明这些id被移除了
        List<String> addressRemoveIds = addressIds.stream().filter(e -> !idNotEmptyAddressInfoIds.contains(e)).collect(Collectors.toList());
        //批量删除
        iCustomerDeliveryAddressInfoService.removeByIds(addressRemoveIds);
        //批量更新、新增
        iCustomerDeliveryAddressInfoService.saveOrUpdateBatch(addressInfoList);

        /*客户银行信息变更*/
        List<CustomerBankInfoForm> bankInfoFormList = editCustomerForm.getCustomerBankInfoList();
        //id不为空的银行信息id列表
        List<String> idNotEmptyBankInfoIds = new ArrayList<>();
        //银行信息列表
        List<CustomerBankInfo> bankInfoList = bankInfoFormList.stream().map(e -> {
            //将form类拷贝到entity类
            CustomerBankInfo bankInfo = new CustomerBankInfo();
            BeanUtils.copyProperties(e, bankInfo);
            //id为空的对象需要设置客户id做一对多关系对应
            bankInfo.setCustomerId(customer.getId());
            if (StringUtils.isNotEmpty(e.getId())) {
                //取出id不为空的对象，用于批量删除做比对
                idNotEmptyBankInfoIds.add(e.getId());
            }
            return bankInfo;
        }).collect(Collectors.toList());
        //查询该可以下所有的银行信息列表（这个列表只查询了主键id）
        LambdaQueryWrapper<CustomerBankInfo> bankInfoFormQueryWrapper = new LambdaQueryWrapper<CustomerBankInfo>()
                .eq(CustomerBankInfo::getCustomerId, customer.getId())
                .select(CustomerBankInfo::getId);
        //取出所有已有id
        List<String> bankIds = iCustomerBankInfoService.list(bankInfoFormQueryWrapper)
                .stream()
                .map(CustomerBankInfo::getId)
                .collect(Collectors.toList());
        List<String> bankRemoveIds = bankIds.stream().filter(e -> !idNotEmptyBankInfoIds.contains(e)).collect(Collectors.toList());
        //批量删除
        iCustomerBankInfoService.removeByIds(bankRemoveIds);
        //批量更新、新增
        iCustomerBankInfoService.saveOrUpdateBatch(bankInfoList);

        checkConsignmentStorage(customer);


        /*查询客户等级和客户行业类型*/
        return getCustomerAndFileModel(customer, infoList, addressInfoList, bankInfoList);
    }

    private void checkConsignmentStorage(Customer customer) {
//        if (CustomerStateEnum.APPROVED.equals(customer.getState())) {
//            ConsignmentStorageCreateMessage message = new ConsignmentStorageCreateMessage(customer.getId(), customer.getCustomerNo(), customer.getCustomerName());
//            message.setSession(SessionContext.getSession());
//            rabbitTemplate.convertAndSend(OMSConstant.CONSIGNMENT_STORAGE_CREATE, message);
//        }
    }

    private CustomerAndFileModel getCustomerAndFileModel(Customer customer,
                                                         List<CustomerFileInfo> infoList,
                                                         List<CustomerDeliveryAddressInfo> addressInfoList,
                                                         List<CustomerBankInfo> bankInfoList) {
        //返回前端
        CustomerAndFileModel model = new CustomerAndFileModel();
        BeanUtils.copyProperties(customer, model);

        //客户附件
        setCustomerFile(model, infoList);

        //客户出货信息
        model.setAddressInfoList(addressInfoList);

        //客户出货信息
        model.setBankInfoList(bankInfoList);

        return model;
    }

    private void setCustomerFile(CustomerAndFileModel model, List<CustomerFileInfo> fileInfoList) {
        fileInfoList.forEach(file -> {
            UploadFile attachment = file.getAttachment();
            switch (file.getFileType()) {
                case LOGO:
                    model.setLogoFile(attachment);
                    break;
                case CERTIFICATE:
                    model.setCertificateFile(attachment);
                    break;
                case COOPERATION:
                    model.setCooperationFile(attachment);
                    break;
                case CONFIDENTIALITY:
                    model.setConfidentialityFile(attachment);
                    break;
                case OTHER:
                    model.setOtherFile(attachment);
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public IPage<Customer> page(Page<Customer> page, CustomerQuery customerQuery) {
        QueryWrapper<Customer> queryWrapper = getCustomerQueryWrapper(customerQuery);

        queryWrapper.and(ObjectUtils.isNotEmpty(customerQuery.getDeptId()), q2 -> q2.in("customer.dept_id", customerQuery.getDeptId()).or().isNull("customer.dept_id"));

        Page<Customer> pageList = iCustomerDao.pageList(page, queryWrapper);
        fillCustomerPropertyName(pageList);
        return pageList;
    }


    @Override
    public IPage<Customer> customerContractStatistics(Page<Customer> page, CustomerQuery customerQuery) {
        customerQuery.setSortColumn("order_amount");
        QueryWrapper<Customer> queryWrapper = getCustomerQueryWrapper(customerQuery);
        ArrayList<String> groupArrays = new ArrayList<>(Arrays.asList("customer.id",
                "customer.customer_no",
                "customer.customer_name",
                "customer.customer_logo_url",
                "customer.customer_level",
                "customer.area",
                "customer.contact",
                "customer.contact_info",
                "customer.state",
                "customer.create_time",
                "customer.property_payment_id",
                "customer.property_source_id",
                "customer.property_vat_rates_id",
                "customer.industry",
                "property.name "));
        queryWrapper.groupBy(groupArrays );
        queryWrapper.in("contract.state", ContractStateEnum.getValid());
        if(ObjectUtil.isNotEmpty(customerQuery.getContractDate())){
            queryWrapper.between("contract.create_time", DateUtil.beginOfDay(customerQuery.getContractDate().get(0))
                    ,DateUtil.endOfDay(customerQuery.getContractDate().get(1)) );
        }
        Page<Customer> pageList = iCustomerDao.customerContractStatisticsPage(page, queryWrapper);
        //拿出客户id，再次查询，用于查询其他费用
        if (!CollectionUtils.isEmpty(pageList.getRecords())){
            List<String> customerIds = pageList.getRecords().stream().map(Customer::getId).distinct().collect(Collectors.toList());
            queryWrapper.in("customer.id",customerIds);
            queryWrapper.getExpression().getGroupBy().clear();
            queryWrapper.getExpression().getOrderBy().clear();

            List<ContractVO> otherFeeList = iCustomerDao.selectOtherFeeByCustomerIds(queryWrapper);
            Map<String, List<ContractVO>> otherFeeMap = otherFeeList.stream().collect(Collectors.groupingBy(ContractVO::getCustomerId));
            for (Customer record : pageList.getRecords()) {
                List<ContractVO> contractVOS = otherFeeMap.get(record.getId());
                BigDecimal otherAmount = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(contractVOS)){
                    for (ContractVO contractVO : contractVOS) {
                        List<OtherFee> otherFees = contractVO.getOtherFees();
                        if (!CollectionUtils.isEmpty(otherFees)){
                            for (OtherFee otherFee : otherFees) {
                                otherAmount = otherAmount.add(otherFee.getValue());
                            }
                        }
                    }
                }
                record.setOtherAmount(otherAmount);
                record.setTaxOtherAmount(otherAmount);
            }
        }
        fillCustomerPropertyName(pageList);
        return pageList;
    }


    @Override
    public Customer customerContractTotal(CustomerQuery customerQuery) {
        //合计接口不需要排序
        customerQuery.setSortColumn(null);
        QueryWrapper<Customer> queryWrapper = getCustomerQueryWrapper(customerQuery);
        queryWrapper.in("contract.state", ContractStateEnum.getValid());
        if(ObjectUtil.isNotEmpty(customerQuery.getContractDate())){
            queryWrapper.between("contract.create_time", DateUtil.beginOfDay(customerQuery.getContractDate().get(0))
                    ,DateUtil.endOfDay(customerQuery.getContractDate().get(1)) );
        }
        Customer customer = iCustomerDao.customerContractStatisticsTotal(queryWrapper);

        //拿出客户id，再次查询，用于查询其他费用
        List<ContractVO> otherFeeList = iCustomerDao.selectOtherFeeByCustomerIds(queryWrapper);
        BigDecimal otherAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(otherFeeList)){
            for (ContractVO contractVO : otherFeeList) {
                List<OtherFee> otherFees = contractVO.getOtherFees();
                if (!CollectionUtils.isEmpty(otherFees)){
                    for (OtherFee otherFee : otherFees) {
                        otherAmount = otherAmount.add(otherFee.getValue());
                    }
                }
            }
        }
        customer.setOtherAmount(otherAmount);
        customer.setTaxOtherAmount(otherAmount);
        return customer;
    }


    private QueryWrapper<Customer> getCustomerQueryWrapper(CustomerQuery customerQuery) {
        QueryWrapper<Customer> queryWrapper = new QueryWrapper<Customer>()
                .eq("customer.deleted", YesOrNOEnum.NO.getValue())
                .in(ObjectUtils.isNotEmpty(customerQuery.getCustomerIds()), "customer.id", customerQuery.getCustomerIds())
                .apply(StringUtils.isNotEmpty(customerQuery.getCustomerName()), "(customer.customer_name like {0} or customer.customer_no like {0})", "%" + customerQuery.getCustomerName() + "%")
                .eq(StringUtils.isNotEmpty(customerQuery.getCustomerLevel()), "customer.customer_level", customerQuery.getCustomerLevel())
                .eq(StringUtils.isNotEmpty(customerQuery.getPropertySourceId()), "customer.property_source_id", customerQuery.getPropertySourceId())
                .like(StringUtils.isNotEmpty(customerQuery.getArea()), "customer.area", customerQuery.getArea())
                .like(StringUtils.isNotEmpty(customerQuery.getContact()), "customer.contact", customerQuery.getContact())
                .eq(customerQuery.getState() != null, "customer.state", customerQuery.getState())
               ;

        queryWrapper.and(ObjectUtils.isNotEmpty(customerQuery.getDeptId()), q2 -> q2.in("customer.dept_id", customerQuery.getDeptId()).or().isNull("customer.dept_id"));
        
        if(ObjectUtil.isNotEmpty(customerQuery.getSortColumn())){
            queryWrapper.orderByDesc(customerQuery.getSortColumn());
        }else {
            queryWrapper.orderByDesc("customer.create_time");
            queryWrapper.orderByDesc("customer.id");
        }

        if (StringUtils.isNotEmpty(customerQuery.getSupervisor())) {
            Employee employee = employeeService.getEmployeeByUser(customerQuery.getSupervisor());
            if (employee != null) {
                queryWrapper.like("customer.supervisor", employee.getId());
            } else {
                queryWrapper.like("customer.supervisor", customerQuery.getSupervisor());
            }
        }
        return queryWrapper;
    }

    private void fillCustomerPropertyName(Page<Customer> pageList) {
        //获取行业类型id
        Set<String> propertyIds = new HashSet<>();
        propertyIds.addAll(pageList.getRecords().stream().map(Customer::getPropertyPaymentId).collect(Collectors.toList()));
        propertyIds.addAll(pageList.getRecords().stream().map(Customer::getIndustry).collect(Collectors.toList()));
        propertyIds.addAll(pageList.getRecords().stream().map(Customer::getPropertySourceId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet()));
        propertyIds.addAll(pageList.getRecords().stream().map(Customer::getPropertyVatRatesId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet()));
        //客户属性列表
        List<CustomerProperty> propertyList = Collections.emptyList();
        if (ObjectUtil.isNotEmpty(propertyIds) && !propertyIds.isEmpty()) {
            //客户属性查询参数
            QueryWrapper<CustomerProperty> propertyQueryWrapper = new QueryWrapper<CustomerProperty>()
                    .select("id", "name")
                    .in("id", propertyIds);
            propertyList = iCustomerPropertyService.list(propertyQueryWrapper);
        }

        //因导出调用，数据量过大时不停查库，导致非常耗时；因此修改为批量查询
        List<String> supervisorList = pageList.getRecords().stream().map(Customer::getSupervisor).filter(Objects::nonNull).flatMap(List::stream).distinct().collect(Collectors.toList());
        //分批调用，防止过多
        List<Employee> employeeList = new LinkedList<>();
        List<List<String>> partition = Lists.partition(supervisorList, 300);
        for (List<String> supervisors : partition) {
            employeeList.addAll(employeeDAO.listByIds(supervisors));
        }
        Map<String, Employee> employeeMap = employeeList.stream().collect(Collectors.toMap(Employee::getId, p -> p));
        for (Customer customer : pageList.getRecords()) {
            //查询客户负责人
        	if(customer.getSupervisor()!=null && !customer.getSupervisor().isEmpty()) {
                // employeeList 排序一下，employeeList.id顺序跟customer.getSupervisor() 保持一致
                List<String> supervisorIds = customer.getSupervisor();
                List<Employee> employees = new ArrayList<>();
                for (String supervisorId : supervisorIds) {
                    employees.add(employeeMap.get(supervisorId));
                }
                customer.setSupervisorList(employees);
        	}

            //客户行业类型
            for (CustomerProperty customerProperty : propertyList) {
                if (customer.getIndustry().equals(customerProperty.getId())) {
                    customer.setIndustryName(customerProperty.getName());
                } else if (customerProperty.getId().equals(customer.getPropertySourceId())) {
                    customer.setPropertySourceName(customerProperty.getName());
                } else if (customerProperty.getId().equals(customer.getPropertyPaymentId())) {
                    customer.setPropertyPayment(customerProperty.getName());
                } else if (customerProperty.getId().equals(customer.getPropertyVatRatesId())) {
                    customer.setPropertyVatRates(customerProperty.getName());
                }
            }
            customer.setCurrency(customer.getCurrency() == null ? "CNY" : customer.getCurrency());
        }
    }

    @Override
    public void removeById(String id) {
        iCustomerDao.removeById(id);
    }

    @Override
    public void removeByIds(List<String> asList) {
        iCustomerDao.removeByIds(asList);
    }

    @Override
    public Customer getBasicById(String id) {
        return iCustomerDao.getById(id);
    }


    @Override
    public Customer getById(String id) {
        //根据id获取客户详情
        Customer customer = iCustomerDao.getById(id);

        //客户属性id列表
        List<String> propertyList = new ArrayList<>();
        //客户等级id
        String level = customer.getCustomerLevel();
        propertyList.add(level);
        //客户行业类型
        String industry = customer.getIndustry();
        propertyList.add(industry);
        //客户收款方式
        String payment = customer.getPropertyPaymentId();
        propertyList.add(payment);
        //增值税率
        String vatRates = customer.getPropertyVatRatesId();
        propertyList.add(vatRates);
        if (StringUtils.isNotEmpty(customer.getPropertySourceId())) {
            propertyList.add(customer.getPropertySourceId());
        }
        if (StringUtils.isNotEmpty(customer.getPropertyTypeId())) {
            propertyList.add(customer.getPropertyTypeId());
        }
        if (StringUtils.isNotEmpty(customer.getPropertyScaleId())) {
            propertyList.add(customer.getPropertyScaleId());
        }
        propertyList.addAll(customer.getPropertyPaymentIds());
        //查询客户属性列表
        List<CustomerProperty> customerPropertyList = iCustomerPropertyService.listByIds(propertyList);
        for (CustomerProperty property : customerPropertyList) {
            if (CustomerPropertyTypeEnum.LEVEL.equals(property.getPropertyType())) {
                //客户等级名称
                customer.setCustomerLevelName(property.getName());
            } else if (CustomerPropertyTypeEnum.INDUSTRY.equals(property.getPropertyType())) {
                //客户行业类型名称
                customer.setIndustryName(property.getName());
            } else if (CustomerPropertyTypeEnum.PAYMENT.equals(property.getPropertyType())) {
                //客户收款方式
                customer.setPropertyPayment(property.getName());
            } else if (CustomerPropertyTypeEnum.VAT_RATES.equals(property.getPropertyType())) {
                //客户增值税率
                customer.setPropertyVatRates(property.getName());
            } else if (CustomerPropertyTypeEnum.SOURCE.equals(property.getPropertyType())) {
                //客户增值税率
                customer.setPropertySourceName(property.getName());
            } else if (CustomerPropertyTypeEnum.TYPE.equals(property.getPropertyType())) {
                //客户类型
                customer.setPropertyTypeName(property.getName());
            } else if (CustomerPropertyTypeEnum.SCALE.equals(property.getPropertyType())) {
                //客户规模
                customer.setPropertyScaleName(property.getName());
            }
        }
        customer.setPropertyPayments(new ArrayList<>(customer.getPropertyPaymentIds().size()));
        for (String propertyPaymentId : customer.getPropertyPaymentIds()) {
            customerPropertyList.stream().filter(row -> row.getId().equalsIgnoreCase(propertyPaymentId))
                    .findFirst()
                    .ifPresent(p -> {
                        customer.getPropertyPayments().add(p.getName());
                    });
        }

        //查询客户附件信息
        LambdaQueryWrapper<CustomerFileInfo> fileInfoQueryWrapper = new LambdaQueryWrapper<CustomerFileInfo>()
                .eq(CustomerFileInfo::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(CustomerFileInfo::getCustomerId, customer.getId());
        List<CustomerFileInfo> fileInfoList = iCustomerFileInfoService.list(fileInfoQueryWrapper);
        customer.setFileInfoList(fileInfoList);

        //查询客户出货信息
        LambdaQueryWrapper<CustomerDeliveryAddressInfo> addressInfoQueryWrapper = new LambdaQueryWrapper<CustomerDeliveryAddressInfo>()
                .eq(CustomerDeliveryAddressInfo::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(CustomerDeliveryAddressInfo::getCustomerId, customer.getId());
        List<CustomerDeliveryAddressInfo> addressInfoList = iCustomerDeliveryAddressInfoService.list(addressInfoQueryWrapper);
        customer.setAddressInfoList(addressInfoList);

        //查询客户银行信息
        LambdaQueryWrapper<CustomerBankInfo> bankInfoQueryWrapper = new LambdaQueryWrapper<CustomerBankInfo>()
                .eq(CustomerBankInfo::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(CustomerBankInfo::getCustomerId, customer.getId());
        List<CustomerBankInfo> bankInfoList = iCustomerBankInfoService.list(bankInfoQueryWrapper);
        customer.setBankInfoList(bankInfoList);

        if(customer.getSupervisor()!=null && !customer.getSupervisor().isEmpty()) {
        	//查询客户负责人
            List<Employee> employeeList = employeeDAO.listByIds(customer.getSupervisor());//employeeService.getEmployeeByUser(customer.getSupervisor());

            Set<String> organizationIds = employeeList.stream().map(Employee::getOrganizationId).collect(Collectors.toSet());

            //查询客户负责人所属组织名称
            if (organizationIds.size() > 0) {
                Map<String, String> organizationMap = organizationDAO.listByIds(organizationIds).stream().collect(Collectors.toMap(Organization::getId, Organization::getName));
                employeeList.forEach(e -> {
                    e.setOrganizationName(organizationMap.get(e.getOrganizationId()));
                });
            }


            // employeeList 排序一下，employeeList.id顺序跟customer.getSupervisor() 保持一致
            Map<String, Integer> orderMap = new HashMap<>();
            List<String> supervisorIds = customer.getSupervisor();
            for (int i = 0; i < supervisorIds.size(); i++) {
                orderMap.put(supervisorIds.get(i), i);
            }
            employeeList.sort((e1, e2) -> {
                Integer pos1 = orderMap.get(e1.getId());
                Integer pos2 = orderMap.get(e2.getId());
                return pos1.compareTo(pos2);
            });
            customer.setSupervisorList(employeeList);
        }

        return customer;
    }


    @Override
    public List<CustomerExportVO> exportCustomer(CustomerQuery customerQuery) {
        //根据条件查询信息
        QueryWrapper<Customer> queryWrapper = getCustomerQueryWrapper(customerQuery);
        Page<Customer> page = new Page<Customer>(1, Integer.MAX_VALUE);
        Page<Customer> pageList = iCustomerDao.pageList(page, queryWrapper);
        List<Customer> records = pageList.getRecords();
        BizAssert.isFalse(CollectionUtils.isEmpty(records),"无导出内容");
        //根据id提取详情信息
        List<String> customerIds = records.stream().map(Customer::getId).collect(Collectors.toList());
        List<Customer> customers = selectBatchByIds(customerIds);
        // 根据customerIds的顺序排序一下customers
        Map<String, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < customerIds.size(); i++) {
            orderMap.put(customerIds.get(i), i);
        }
        customers.sort((c1, c2) -> {
            Integer pos1 = orderMap.get(c1.getId());
            Integer pos2 = orderMap.get(c2.getId());
            return pos1.compareTo(pos2);
        });
        
        //设置导出结果
        List<CustomerExportVO> exportVoList = new LinkedList<>();
        for (Customer customer : customers) {
            CustomerExportVO result = new CustomerExportVO(customer);
            exportVoList.add(result);
        }

        if (!exportVoList.isEmpty()) {
            Set<String> deptIds = exportVoList.stream().map(CustomerExportVO::getDeptId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            if (!deptIds.isEmpty()) {
                Map<String, String> deptMap = organizationService.getListByOrganizationIds(deptIds).stream().collect(Collectors.toMap(BaseEntity::getId, Organization::getName));
                exportVoList.forEach(row -> {
                    row.setDeptName(deptMap.get(row.getDeptId()));
                });
            }
        }
        return exportVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id, boolean disabled) {
        //根据id获取客户详情
        Customer customer = iCustomerDao.getById(id);
        BizAssert.isNotNull(customer, "客户不存在");
        if (disabled) {
            BizAssert.isTrue(CustomerStateEnum.DRAFT.equals(customer.getState()) || CustomerStateEnum.APPROVED.equals(customer.getState()), "只能停用待提交或正常状态客户");
            customer.setState(CustomerStateEnum.DISABLED);
            LogUtils.saveLog(OMSConstant.RESOURCE_CUSTOMER, OperateTypeConstant.UPDATE, "停用客户", customer);
        } else {
            BizAssert.isTrue(CustomerStateEnum.DISABLED.equals(customer.getState()), "只能启用已停用状态客户");
            customer.setState(CustomerStateEnum.DRAFT);
            LogUtils.saveLog(OMSConstant.RESOURCE_CUSTOMER, OperateTypeConstant.UPDATE, "启用客户", customer);
        }
        boolean success = iCustomerDao.update(new LambdaUpdateWrapper<Customer>()
                .set(Customer::getState, customer.getState())
                .set(Customer::getUpdateTime, new Date())
                .set(BaseEntity::getUpdateBy, SessionContext.getPrincipal())
                .eq(BaseEntity::getId, customer.getId()));
        BizAssert.isTrue(success, "更新失败，请稍后再试");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        Customer customer = iCustomerDao.getById(id);
        BizAssert.isNotNull(customer, "客户不存在");
        BizAssert.isTrue(CustomerStateEnum.DISABLED.equals(customer.getState()), "只能删除已停用状态客户");

        long count = contractService.count(new LambdaQueryWrapper<Contract>().eq(Contract::getCustomerId, id));
        BizAssert.isTrue(count == 0, "客户已经有订单，不可删除。");
        count = orderService.count(new LambdaQueryWrapper<Order>().eq(Order::getCustomerId, id));
        BizAssert.isTrue(count == 0, "客户已经有订单，不可删除。");
        orderDeliveryDao.count(new LambdaQueryWrapper<OrderDelivery>().eq(OrderDelivery::getCustomerId, id));
        BizAssert.isTrue(count == 0, "客户已经有订单，不可删除。");

        LogUtils.saveLog(OMSConstant.RESOURCE_CUSTOMER, OperateTypeConstant.DELETE, "删除客户", customer);

        iCustomerDao.removeById(customer);
    }

    @Override
    public List<Customer> list(Wrapper<Customer> queryWrapper) {
        return iCustomerDao.list(queryWrapper);
    }

    @Override
    public void updateCustomerDeliveryRemark(CustomerDeliveryRemarkForm form) {
        LambdaUpdateWrapper<Customer> updateWrapper = new LambdaUpdateWrapper<Customer>()
                .set(Customer::getDeliveryRemark, form.getDeliveryRemark())
                .eq(Customer::getId, form.getId());
        iCustomerDao.update(updateWrapper);
    }

    @Override
    public CustomerAndFileModel getCustomerAndFile(String id) {

        //根据id获取客户详情
        Customer customer = getById(id);

        //查询客户附件信息
        LambdaQueryWrapper<CustomerFileInfo> fileInfoQueryWrapper = new LambdaQueryWrapper<CustomerFileInfo>()
                .eq(CustomerFileInfo::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(CustomerFileInfo::getCustomerId, customer.getId());
        List<CustomerFileInfo> fileInfoList = iCustomerFileInfoService.list(fileInfoQueryWrapper);

        //查询客户出货信息
        LambdaQueryWrapper<CustomerDeliveryAddressInfo> addressInfoQueryWrapper = new LambdaQueryWrapper<CustomerDeliveryAddressInfo>()
                .eq(CustomerDeliveryAddressInfo::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(CustomerDeliveryAddressInfo::getCustomerId, customer.getId());
        List<CustomerDeliveryAddressInfo> addressInfoList = iCustomerDeliveryAddressInfoService.list(addressInfoQueryWrapper);

        //查询客户银行信息
        LambdaQueryWrapper<CustomerBankInfo> bankInfoQueryWrapper = new LambdaQueryWrapper<CustomerBankInfo>()
                .eq(CustomerBankInfo::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(CustomerBankInfo::getCustomerId, customer.getId());
        List<CustomerBankInfo> bankInfoList = iCustomerBankInfoService.list(bankInfoQueryWrapper);

        return getCustomerAndFileModel(customer, fileInfoList, addressInfoList, bankInfoList);
    }

    @Override
    public List<Customer> list(CustomerQuery query) {
        //获取客户列表
        QueryWrapper<Customer> queryWrapper = new QueryWrapper<Customer>()
                .eq("customer.state", CustomerStateEnum.APPROVED)
                .eq("customer.deleted", YesOrNOEnum.NO.getValue())
                .like(StringUtils.isNotEmpty(query.getCustomerName()), "customer.customer_name",   query.getCustomerName() )
                .in(ObjectUtil.isNotEmpty(query.getCustomerIds()), "customer.id",   query.getCustomerIds() )
                .like(StringUtils.isNotEmpty(query.getSupervisor()),"customer.supervisor",  query.getSupervisor() );
        List<Customer> customerList = iCustomerDao.pageList(new Page<>(1, -1), queryWrapper).getRecords();

        Set<String> propertyList = new HashSet<>();
        for (Customer customer : customerList) {
            propertyList.add(customer.getPropertyPaymentId());
            propertyList.add(customer.getPropertyVatRatesId());
        }

        //获取客户属性收款方式名称、税率
        if (ObjectUtils.isNotEmpty(propertyList)) {
            List<CustomerProperty> paymentList = iCustomerPropertyService.listByIds(propertyList);

            //处理返回的列表
            customerList.forEach(e -> {
                //收款方式
                paymentList.forEach(p -> {
                    if (p.getId().equals(e.getPropertyPaymentId())) {
                        e.setPropertyPayment(p.getName());
                    } else if (CustomerPropertyTypeEnum.VAT_RATES.equals(p.getPropertyType())) {
                        //客户增值税率
                        e.setPropertyVatRates(p.getName());
                    }
                });

            });
        }
        // 不分页的列表，不需要查询负责人数据，这里数据太多，很慢
        if (customerList.size() == 1) {
            // 客户报价会调用这个接口，只传一个客户名称，当只有一条记录的时候，返回客户负责人
            customerList.forEach(customer -> {
                //查询客户负责人
                if(customer.getSupervisor()!=null && !customer.getSupervisor().isEmpty()) {
                    List<Employee> employeeList = employeeDAO.listByIds(customer.getSupervisor());//employeeService.getEmployeeByUser(customer.getSupervisor());
                    customer.setSupervisorList(employeeList);
                }
            });
        }

        return customerList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStateById(Customer customer) {
        iCustomerDao.updateStateById(customer);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                checkConsignmentStorage(customer);
            }
        });
    }

    @Override
    public Long count() {
        QueryWrapper<Customer> wrapper = new QueryWrapper<Customer>()
                .eq("deleted", YesOrNOEnum.NO.getValue())
                .in("state", CustomerStateEnum.SUBMITTED.getValue(), CustomerStateEnum.APPROVED.getValue());
        return iCustomerDao.count(wrapper);
    }

    @Override
    public boolean checkCustomerNoExists(String no, String id) {
        return iCustomerDao.count(new LambdaQueryWrapper<Customer>().eq(Customer::getCustomerNo, no)
                .ne(StringUtils.isNotEmpty(id), BaseEntity::getId, id)) > 0;
    }

    @Override
    public boolean checkCustomerNameExists(String name, String id) {
        return iCustomerDao.count(new LambdaQueryWrapper<Customer>()
                .eq(Customer::getCustomerName, name)
                .ne(StringUtils.isNotEmpty(id), BaseEntity::getId, id)) > 0;
    }

    @Override
    public List<Customer> getList(CustomerQuery query) {
        //获取客户列表
        QueryWrapper<Customer> queryWrapper = new QueryWrapper<Customer>()
                .eq("deleted", Boolean.FALSE)
                .eq(StringUtils.isNotEmpty(query.getCustomerName()), "customer_name", query.getCustomerName())
                .like(StringUtils.isNotEmpty(query.getSupervisor()),"supervisor", query.getSupervisor());
        if (ObjectUtil.isNotEmpty(query.getState())) {
            queryWrapper.eq("state", query.getState());
        }
        return iCustomerDao.getList(queryWrapper);
    }

    @Override
    public List<AreaModel> findAreaByName(String area) {
        List<AreaModel> dictDataList =  iCustomerDao.findAreaByName(area);
        //转为树形结构
        Map<String, List<AreaModel>> areaModelMap = dictDataList.stream().collect(Collectors.groupingBy(AreaModel::getDictType));
        List<AreaModel> areaModels = areaModelMap.get(OMSConstant.ERP_COUNTRY);
        areaModels.forEach(a-> a.setChildren(areaModelMap.getOrDefault(a.getDictValue(),Collections.emptyList())));
        return areaModels;
    }

    @Override
    public List<Customer> findListByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return iCustomerDao.listByIds(ids);
    }

    @Override
    public List<Customer> findListByCodes(Collection<String> codes) {
        if (CollectionUtils.isEmpty(codes)){
            return Collections.emptyList();
        }
        return iCustomerDao.list(new LambdaQueryWrapper<Customer>().in(Customer::getCustomerNo, codes));
    }

    /**
     * changeSupervisor:更新用户ID为员工ID.
     *
     * <AUTHOR>
     * @since version 1.0
     */
    @Override
    public void changeSupervisor() {
    	 List<Customer> list = this.iCustomerDao.list();
    	 for(Customer customer : list) {
    		 List<String> sups = customer.getSupervisor();
    		 List<String> supsEmp = new ArrayList<>();
    		 if(sups!=null && sups.size()>0) {
    			 for(String id : sups) {
    				 TenantUser tenantUser = tenantUserDAO.getById(id);
                     if(tenantUser!=null) {
                    	 supsEmp.add(tenantUser.getEmployeeId());
                     }
    			 }
    		 }
    		 if(!supsEmp.isEmpty()) {
    			 customer.setSupervisor(supsEmp);
    			 this.iCustomerDao.updateById(customer);
    		 }
    	 }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportError> importCustomer(List<CustomerImportForm> importForms, Boolean isOverwrite) {
        //错误信息
        List<ImportError> reason = new LinkedList<>();

        //防止传空文件
        if (CollectionUtils.isEmpty(importForms)){
            return reason;
        }

        //客户编码
        List<String> customerNos = new ArrayList<>();
        customerNos.add("none");
        //客户名称
        List<String> customerNames = new ArrayList<>();
        customerNames.add("none");

        //客户负责人
        List<String> supervisors = new ArrayList<>();
        //部门
        Set<String> deptList = new HashSet<>();
        for (CustomerImportForm form : importForms) {
            customerNos.add(form.getCustomerNo());
            customerNames.add(form.getCustomerName());
            String supervisor = form.getSupervisor();
            if (ObjectUtil.isNotEmpty(form.getSupervisor())){
                //supervisor根据逗号分割，去除空格 [,，]为匹配任一
                supervisors.addAll(Arrays.stream(StringUtils.split(form.getSupervisor(), "[,，]")).map(String::trim).collect(Collectors.toList()));
            }
            deptList.add(form.getDept());
        }

        List<Customer> customerDBList = iCustomerDao.list(new LambdaQueryWrapper<Customer>()
                .and(wrapper ->wrapper.in(Customer::getCustomerNo, customerNos)
                        .or(wr->wr.in(Customer::getCustomerName, customerNames))));
        Map<String, Customer> customerMap = customerDBList.stream().collect(Collectors.toMap(Customer::getCustomerNo, Customer -> Customer, (a, b) -> a));
        // 根据客户编码查询客户信息
        Map<String, String> customerDBMap = customerDBList.stream()
                .collect(Collectors.toMap(Customer::getCustomerNo, Customer::getCustomerName, (a, b) -> a));

        //统计每个客户编码出现的次数
        Map<String, Long> codeFrequency = ProcessUtils.frequencyOfListQ(customerNos);

        //统计每个客户名称出现的次数
        Map<String, Long> nameFrequency = ProcessUtils.frequencyOfListQ(customerNames);


        //查询客户属性 客户等级 行业类型
        Map<CustomerPropertyTypeEnum, List<CustomerProperty>> propertyMap = iCustomerPropertyService.list().stream().collect(Collectors.groupingBy(CustomerProperty::getPropertyType));
        //等级
        Map<String, String> levelMap = propertyMap.getOrDefault(CustomerPropertyTypeEnum.LEVEL, Collections.emptyList()).stream().collect(Collectors.toMap(CustomerProperty::getName, CustomerProperty::getId));
        //行业类型
        Map<String, String> industryMap = propertyMap.getOrDefault(CustomerPropertyTypeEnum.INDUSTRY, Collections.emptyList()).stream().collect(Collectors.toMap(CustomerProperty::getName, CustomerProperty::getId));
        //结算方式
        Map<String, String> paymentMap = propertyMap.getOrDefault(CustomerPropertyTypeEnum.PAYMENT, Collections.emptyList()).stream().collect(Collectors.toMap(CustomerProperty::getName, CustomerProperty::getId));
        //增值税率
        Map<String, String> vatRateMap = propertyMap.getOrDefault(CustomerPropertyTypeEnum.VAT_RATES, Collections.emptyList()).stream().collect(Collectors.toMap(CustomerProperty::getName, CustomerProperty::getId));
        //客户来源
        Map<String, String> sourceMap = propertyMap.getOrDefault(CustomerPropertyTypeEnum.SOURCE, Collections.emptyList()).stream().collect(Collectors.toMap(CustomerProperty::getName, CustomerProperty::getId));
        //所属国家
        Map<String, String> countryMap = propertyMap.getOrDefault(CustomerPropertyTypeEnum.COUNTRY, Collections.emptyList()).stream().collect(Collectors.toMap(CustomerProperty::getName, CustomerProperty::getId));

        //查询员工信息
        Map<String, String> employeeMap = new HashMap<>();
        if (!supervisors.isEmpty()) {
            List<Employee> employees = employeeService.getEmployees(new LambdaQueryWrapper<Employee>().in(Employee::getCode, supervisors).or().in(Employee::getRealName, supervisors));
            employees.forEach(row -> {
                employeeMap.put(row.getCode(), row.getId());
                employeeMap.put(row.getRealName(), row.getId());
            });
        }
        
        // 部门信息
        Map<String, String> deptMap = new HashMap<>();
        if (!deptList.isEmpty()) {
            List<Organization> organizationList = organizationService.getList(new QueryWrapper<Organization>().in("code", deptList).or().in("name", deptList));
            organizationList.forEach(row -> {
                deptMap.put(row.getCode(), row.getId());
                deptMap.put(row.getName(), row.getId());
            });
        }

        //查询字典 性别COMMON_GENDER  存的是label 传的也是label
        Map<String, String> genderMap = dictDataService.findByDictType("COMMON_GENDER")
                .stream()
                .collect(Collectors.toMap(DictData::getDictLabel, DictData::getDictLabel));
        //查询字典 COMMON_CURRENCY
        Map<String, String> currencyMap = dictDataService.findByDictType("COMMON_CURRENCY")
                .stream()
                .collect(Collectors.toMap(DictData::getDictLabel, DictData::getDictLabel));


        //客户信息
        List<Customer> customerList = new ArrayList<>();
        List<Customer> customerUpdList = new ArrayList<>();
        //出货信息
        List<CustomerDeliveryAddressInfo> addressInfoList = new ArrayList<>();
        //银行信息
        List<CustomerBankInfo> bankInfoList = new ArrayList<>();
        for (int i = 0; i < importForms.size(); i++) {
            //行数据
            CustomerImportForm customerForm = importForms.get(i);
            //excel行号
            int lineNumber = i + 2;

            /*校验表单*/
            Map<String, String> message = validationForm(customerDBMap,codeFrequency,nameFrequency,levelMap,industryMap,paymentMap,vatRateMap,sourceMap,countryMap,employeeMap,genderMap,customerForm,currencyMap, isOverwrite);

            if (message.isEmpty()) {
                /*校验后没问题的数据才会处理*/
                //生成客户信息
                Customer customer = processCustomer(customerForm,levelMap,industryMap,paymentMap,vatRateMap,sourceMap,countryMap,employeeMap,genderMap);
                customer.setDeptId(deptMap.get(customerForm.getDept()));
                if (customerMap.containsKey(customerForm.getCustomerNo())){ //重复用户
                	Customer customerOld = customerMap.get(customerForm.getCustomerNo());
                	BeanUtils.copyProperties(customer, customerOld, "id");
                	customerUpdList.add(customerOld);
                } else {
                	customerList.add(customer);
                }

                if(ObjectUtil.isNotEmpty(customerForm.getConsigneeAddress())){
                    CustomerDeliveryAddressInfo addressInfo = processAddressInfo(customerForm,customer.getId());
                    addressInfoList.add(addressInfo);
                }
                if(ObjectUtil.isNotEmpty(customerForm.getBankName())){
                    CustomerBankInfo bankInfo = processBankInfo(customerForm,customer.getId());
                    bankInfoList.add(bankInfo);
                }
            }else{
                /*有错误信息就记录行号、错误提示*/
                reason.add(new ImportError(lineNumber, message.values()));
            }
        }
        if(ObjectUtil.isEmpty(reason)){
            //保存客户信息
            if(ObjectUtil.isNotEmpty(customerList)){
                iCustomerDao.saveBatch(customerList);
            }
            if(ObjectUtil.isNotEmpty(customerUpdList)){
                iCustomerDao.updateBatchById(customerUpdList);
            }

            //保存出货信息
            if(ObjectUtil.isNotEmpty(addressInfoList)){
                iCustomerDeliveryAddressInfoService.saveBatch(addressInfoList);
            }
            //保存银行信息
            if(ObjectUtil.isNotEmpty(bankInfoList)){
                iCustomerBankInfoService.saveBatch(bankInfoList);
            }

        }


        return reason;
    }

    private CustomerBankInfo processBankInfo(CustomerImportForm customerForm, String id) {
        CustomerBankInfo bankInfo = new CustomerBankInfo();
        return bankInfo.setCustomerId(id)
                .setBankName(customerForm.getBankName())
                .setBankOfDeposit(customerForm.getBankOfDeposit())
                .setAccountName(customerForm.getAccountName())
                .setAccount(customerForm.getAccount())
        ;
    }

    private CustomerDeliveryAddressInfo processAddressInfo(CustomerImportForm customerForm, String id) {
        CustomerDeliveryAddressInfo addressInfo = new CustomerDeliveryAddressInfo();
        if(ObjectUtil.isNotEmpty(customerForm.getConsigneeAddress())){
            addressInfo.setCustomerId(id)
                    .setAddress(customerForm.getConsigneeAddress())
                    .setContact(customerForm.getConsigneeContact())
                    .setPhone(customerForm.getPhone())
                    .setStreet(customerForm.getStreet());
        }
        return addressInfo;
    }

    private Customer processCustomer(CustomerImportForm customerImportForm, Map<String, String> levelMap, Map<String, String> industryMap, Map<String, String> paymentMap, Map<String, String> vatRateMap, Map<String, String> sourceMap, Map<String, String> countryMap, Map<String, String> employeeMap, Map<String, String> genderMap) {
        Customer customer = new Customer();
        ProcessUtils.copyProperties(customerImportForm, customer, converterRegistry);
        String customerId = GENERATOR.nextId(customer).toString();
        customer.setId(customerId);
        //客户等级
        customer.setCustomerLevel(levelMap.get(customerImportForm.getCustomerLevel()));
        //行业类型
        customer.setIndustry(industryMap.get(customerImportForm.getIndustry()));
        //结算方式
        customer.setPropertyPaymentId(paymentMap.get(customerImportForm.getPropertyPaymentId()));
        if (ObjectUtil.isNotEmpty(paymentMap.get(customerImportForm.getPropertyPaymentId()))) {
            customer.setPropertyPaymentIds(Collections.singletonList(paymentMap.get(customerImportForm.getPropertyPaymentId())));
        } else {
            customer.setPropertyPaymentIds(Collections.emptyList());
        }
        //增值税id
        customer.setPropertyVatRatesId(vatRateMap.get(customerImportForm.getPropertyVatRatesId()));
        //状态
        customer.setState(CustomerStateEnum.APPROVED);
        //客户联系人
        CustomerContactModel customerContactModel = new CustomerContactModel();
        customer.setCustomerContactInfo(Collections.emptyList());
        if(!ObjectUtil.isAllEmpty(customerImportForm.getContact(),
                customerImportForm.getContactNumber(),
                customerImportForm.getJob(),
                customerImportForm.getGender(),
                customerImportForm.getOtherContactInformation())
        ){
            BeanUtil.copyProperties(customerImportForm,customerContactModel);
            customer.setCustomerContactInfo( Collections.singletonList(customerContactModel));
        }
        //来源
        if(ObjectUtil.isNotEmpty(customerImportForm.getPropertySourceId())){
            customer.setPropertySourceId(sourceMap.get(customerImportForm.getPropertySourceId()));
        }
        //客户负责人
        List<String> supervisorNos = Arrays.stream(customerImportForm.getSupervisor().split("[,，]")).map(String::trim).collect(Collectors.toList());
        List<String> supervisors = supervisorNos.stream().map(employeeMap::get).collect(Collectors.toList());
        customer.setSupervisor(supervisors);


        return customer;
    }

    private Map<String, String> validationForm(Map<String, String> customerDBMap,
                                               Map<String, Long> codeFrequency,
                                               Map<String, Long> nameFrequency,
                                               Map<String, String> levelMap,
                                               Map<String, String> industryMap,
                                               Map<String, String> paymentMap,
                                               Map<String, String> vatRateMap,
                                               Map<String, String> sourceMap,
                                               Map<String, String> countryMap,
                                               Map<String, String> employeeMap,
                                               Map<String, String> genderMap,
                                               CustomerImportForm customerForm, Map<String, String> currencyMap, Boolean isOverwrite) {
        Map<String, String> message = new HashMap<>();
        /*表单校验*/
        message = ValidationUtils.validateMessage(customerForm);

        String customerMessage = ",客户名称:"+ StrUtil.emptyIfNull(customerForm.getCustomerName()) ;
        /*校验不可重复导入*/
        if (customerDBMap.containsKey(customerForm.getCustomerNo()) && !isOverwrite){
            message.put("exitCustomerCode", "系统存在相同的客户编码，不可重复导入");
        }

        if (customerDBMap.containsValue(customerForm.getCustomerName()) && !isOverwrite){
            message.put("exitCustomerName", "系统存在相同的客户名称，不可重复导入");
        }



        /*特殊校验*/
        //校验客户编码是否重复
        String customerNoField = "customerNo";
        //如果出现的频率大于1，说明该编码重复
        if (!message.containsKey(customerNoField)
                && codeFrequency.containsKey(customerForm.getCustomerNo())
                && codeFrequency.get(customerForm.getCustomerNo()) > CommonConstant.ONE) {
            message.put(customerNoField, "表单客户编码重复，请检查上传的excel");
        }
        //校验客户编码是否重复
        String customerNameField = "customerName";
        //如果出现的频率大于1，说明该编码重复
        if (!message.containsKey(customerNameField)
                && nameFrequency.containsKey(customerForm.getCustomerName())
                && nameFrequency.get(customerForm.getCustomerName()) > CommonConstant.ONE) {
            message.put(customerNameField, "客户名称重复，请检查上传的excel");
        }
        //校验客户等级
        String customerLevelField = "customerLevel";
        if (!message.containsKey(customerLevelField)
                && !levelMap.containsKey(customerForm.getCustomerLevel())) {
            message.put(customerLevelField, "客户等级不存在，请在客户属性里配置");
        }
        //校验行业类型
        String industryField = "industry";
        if (!message.containsKey(industryField)
                && !industryMap.containsKey(customerForm.getIndustry())) {
            message.put(industryField, "行业类型不存在，请在客户属性里配置");
        }
        //校验货币
        String currencyField = "currency";
        if (!message.containsKey(currencyField)
                && !currencyMap.containsKey(customerForm.getCurrency())) {
            message.put(currencyField, "货币不存在");
        }
        //校验收款方式
        String paymentField = "propertyPaymentId";
        if (!message.containsKey(paymentField)
                && !paymentMap.containsKey(customerForm.getPropertyPaymentId())) {
            message.put(paymentField, "付款方式不存在，请在客户属性里配置");
        }
        //校验增值税率
        String vatRateField = "propertyVatRatesId";
        if (!message.containsKey(vatRateField)
                && !vatRateMap.containsKey(customerForm.getPropertyVatRatesId())) {
            message.put(vatRateField, "增值税率不存在，请在客户属性里配置");
        }
        //校验客户来源
        String sourceField = "propertySourceId";
        if (!message.containsKey(sourceField)
                && !sourceMap.containsKey(customerForm.getPropertySourceId())
        && !StringUtils.isEmpty(customerForm.getPropertySourceId())) {
            message.put(sourceField, "客户来源不存在，请在客户属性里配置");
        }

        //校验客户负责人
        String supervisorField = "supervisor";
        if (!message.containsKey(supervisorField)) {
            List<String> supervisorNos = Arrays.stream(customerForm.getSupervisor().split("[,，]")).map(String::trim).collect(Collectors.toList());
            for (String supervisorNo : supervisorNos) {
                if (!employeeMap.containsKey(supervisorNo)){
                    message.put(supervisorField, "客户负责人不存在，请在员工管理里配置");
                }
            }
        }
        Map<String, String> finalMessage = message;
        message.forEach((key, value) -> finalMessage.put(key, value + customerMessage));

        return finalMessage;
    }




    /**
     *
     * <AUTHOR>
     * @description 根据批量客户id查询客户详情
     * 注：目前仅导出客户信息再用，因此部分信息不需要查询就去掉了，可能会存在信息不全的可能，如若调用，建议调试，看看返参是否全部满足调用方要求！！！
     * @date 2024/11/4 14:44
     * @param customerIds
     * @return java.util.List<com.huayun.modules.erp.oms.entity.Customer>
     */
    public List<Customer> selectBatchByIds(List<String> customerIds) {
        //根据id获取客户详情
        List<Customer> customers = iCustomerDao.getBaseMapper().selectBatchIds(customerIds);
        //客户属性id列表
        List<String> propertyList = new ArrayList<>();
        for (Customer customer : customers) {
            //客户等级id
            String level = customer.getCustomerLevel();
            propertyList.add(level);
            //客户行业类型
            String industry = customer.getIndustry();
            propertyList.add(industry);
            //客户收款方式
            String payment = customer.getPropertyPaymentId();
            propertyList.add(payment);
            //增值税率
            String vatRates = customer.getPropertyVatRatesId();
            propertyList.add(vatRates);
            if (StringUtils.isNotEmpty(customer.getPropertySourceId())) {
                propertyList.add(customer.getPropertySourceId());
            }
        }
        //查询客户属性列表
        List<CustomerProperty> customerPropertyList = iCustomerPropertyService.list();

        //客户属性不需要管类型，直接取出id对应的name
        Map<String, String> propertyMap = customerPropertyList.stream().collect(Collectors.toMap(CustomerProperty::getId, CustomerProperty::getName));

        //分批处理客户id用作查询
        List<List<String>> customerIdPartition = Lists.partition(customerIds, 300);
        //客户出货信息
        List<CustomerDeliveryAddressInfo> addressInfoList = new LinkedList<>();
        //客户银行信息
        List<CustomerBankInfo> bankInfoList = new LinkedList<>();

        for (List<String> strings : customerIdPartition) {

            //查询客户出货信息
            LambdaQueryWrapper<CustomerDeliveryAddressInfo> addressInfoQueryWrapper = new LambdaQueryWrapper<CustomerDeliveryAddressInfo>()
                    .eq(CustomerDeliveryAddressInfo::getDeleted, YesOrNOEnum.NO.getValue())
                    .in(CustomerDeliveryAddressInfo::getCustomerId, strings);
            List<CustomerDeliveryAddressInfo> list1 = iCustomerDeliveryAddressInfoService.list(addressInfoQueryWrapper);
            if (!CollectionUtils.isEmpty(list1)){
                addressInfoList.addAll(list1);
            }

            //查询客户银行信息
            LambdaQueryWrapper<CustomerBankInfo> bankInfoQueryWrapper = new LambdaQueryWrapper<CustomerBankInfo>()
                    .eq(CustomerBankInfo::getDeleted, YesOrNOEnum.NO.getValue())
                    .in(CustomerBankInfo::getCustomerId, strings);
            List<CustomerBankInfo> list2 = iCustomerBankInfoService.list(bankInfoQueryWrapper);
            if (!CollectionUtils.isEmpty(list2)){
                bankInfoList.addAll(list2);
            }
        }
        //组装出货信息Map和客户银行信息
        Map<String, List<CustomerDeliveryAddressInfo>> addressInfoMap = addressInfoList.stream().collect(Collectors.groupingBy(CustomerDeliveryAddressInfo::getCustomerId));
        Map<String, List<CustomerBankInfo>> bankInfoMap = bankInfoList.stream().collect(Collectors.groupingBy(CustomerBankInfo::getCustomerId));


        //查询客户负责人信息
        List<String> supervisorList = customers.stream()
                .flatMap(customer -> customer.getSupervisor().stream())
                .distinct()
                .collect(Collectors.toList());
        //分批查询
        List<Employee> employeeList = new LinkedList<>();
        List<List<String>> supervisorPartition = Lists.partition(supervisorList, 300);
        for (List<String> strings : supervisorPartition) {
            List<Employee> listByIds = employeeDAO.listByIds(strings);
            if (!CollectionUtils.isEmpty(listByIds)){
                employeeList.addAll(listByIds);
            }
        }
        //客户负责人信息
        Map<String, Employee> employeeMap = employeeList.stream().collect(Collectors.toMap(Employee::getId, p -> p));



        for (Customer customer : customers) {
            //客户等级名称
            customer.setCustomerLevelName(propertyMap.get(customer.getCustomerLevel()));
            //客户行业类型名称
            customer.setIndustryName(propertyMap.get(customer.getIndustry()));
            //客户收款方式
            customer.setPropertyPayment(propertyMap.get(customer.getPropertyPaymentId()));
            //客户增值税率
            customer.setPropertyVatRates(propertyMap.get(customer.getPropertyVatRatesId()));
            //客户来源
            if (StringUtils.isNotEmpty(customer.getPropertySourceId())) {
                customer.setPropertySourceName(propertyMap.get(customer.getPropertySourceId()));
            }
            //客户出货信息
            customer.setAddressInfoList(addressInfoMap.get(customer.getId()));
            //客户银行信息
            customer.setBankInfoList(bankInfoMap.get(customer.getId()));
            //客户负责人信息
            List<String> supervisor = customer.getSupervisor();
            List<Employee> employees = new LinkedList<>();
            if (!CollectionUtils.isEmpty(supervisor)){
                for (String employeeId : supervisor) {
                    employees.add(employeeMap.get(employeeId));
                }
            }
            customer.setSupervisorList(employees);
        }
        return customers;
    }

    @Override
    public IPage<Approval> approvalsPage(Page<Contract> page, QueryWrapper<Customer> qw) {
        return customerMapper.approvalsPage(page, qw);
    }

}
