package com.huayun.modules.erp.oams.controller.form;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.huayun.modules.erp.oams.constant.enums.AssTypeEnum;
import com.huayun.modules.erp.oams.controller.vo.CollectionVO;
import com.huayun.modules.erp.oams.entity.ExpenseDetail;
import com.huayun.modules.erp.oams.entity.InvoiceDetail;
import com.huayun.modules.erp.oams.entity.Payment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * @Description: 发票详情
 * @Author: jeecg-boot
 * @Date:   2023-10-20
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description="发票详情")
public class InvoiceDetailForm  implements BaseForm{
	/**{
	    "materialId": "1769974828510560257",
	    "materialCode": "*********",
	    "materialName": "黄色塑胶",
	    "specification": "01090002",
	    "materialUnit": "片",
	    "countQuantity": 8000,
	    "orderNo": "PO240320001",
	    "property": null,
	    "price": 0.531,
	    "taxPrice": 0.6,
	    "taxRate": 13,
	    "code": "RK240320001",
	    "receiptItemId": "1770293440672776193",
	    "purchaseOrderItemId": "1770293368803377153",
	    "currentBuyCount": 8000,
	    "deliveryQuantity": null,
	    "invoiceQuantity": 22,
	    "exTax": "11.68",
	    "tax": 1.5186600000000001,
	    "inTax": "13.20"
	}**/
    @ApiModelProperty(value = "发票ID")
	private java.lang.String invoiceId;

    @ApiModelProperty(value = "发货单ID")
	private java.lang.String deliveryId;

    @ApiModelProperty(value = "税率 采购订单、委外订单、客户订单、对账单的税率是带%的  这里的汇率是带%的")
	private java.math.BigDecimal rate;

    @ApiModelProperty(value = "税率 采购订单、委外订单、客户订单、对账单的税率是带%的  这里的汇率是带%的  原")
	private BigDecimal taxRate;

    @ApiModelProperty(value = "计税方法")
	private java.lang.String rateType;

    @ApiModelProperty(value = "是否暂估")
	private Boolean es;

	@ApiModelProperty(value = "出库单号  原")
    private String deliveryNo;

    @ApiModelProperty(value = "出库单号")
    private String code;

	@ApiModelProperty(value = "订单编号")
    private String orderNo;

	@ApiModelProperty(value = "产品id 原")
    private String productId;

	@ApiModelProperty(value = "产品id")
    private String materialId;

	@ApiModelProperty(value = "产品编码 原")
    private String productCode;

	@ApiModelProperty(value = "产品编码")
    private String materialCode;

	@ApiModelProperty(value = "产品名称 原")
    private String productName;

	@ApiModelProperty(value = "产品名称")
    private String materialName;

	@ApiModelProperty(value = "产品规格 原")
    private String productSpecification;

	@ApiModelProperty(value = "产品规格")
    private String specification;

	@ApiModelProperty(value = "产品型号")
    private String productModel;

	@ApiModelProperty(value = "产品单位")
    private String productUnit;

	@ApiModelProperty(value = "产品单位 原")
    private String materialUnit;

	@ApiModelProperty(value = "发货数量 数量 原")
    private BigDecimal deliveryQuantity;

	@ApiModelProperty(value = "发货数量 数量")
    private BigDecimal countQuantity;

	@ApiModelProperty(value = "本位币总价 含税金额")
	private BigDecimal inTax;

	@ApiModelProperty(value = "本位币总价 不含税金额")
	private BigDecimal exTax;

	@ApiModelProperty(value = "本位币总价 税额")
	private BigDecimal tax;

	@ApiModelProperty(value = "本位币单价 税前金额 原")
    private BigDecimal localPrice;

	@ApiModelProperty(value = "本位币单价 税前金额")
    private BigDecimal price;


	@ApiModelProperty(value = "本位币 含税单价")
	private BigDecimal taxPrice;

	@ApiModelProperty(value = "对应的发货单明细的ID 原")
    private String deliveryItemId;

	@ApiModelProperty(value = "对应的发货单明细的ID")
    private String receiptItemId;

	@ApiModelProperty(value = "币种")
	private String curreny;

	@ApiModelProperty(value = "开票数量")
    private BigDecimal invoiceQuantity;

	@ApiModelProperty(value = "关联类型")
	private AssTypeEnum assType;

	@ApiModelProperty(value = "费用类别ID")
	private String expenseTypeId;

	@ApiModelProperty(value = "费用类别名称")
	private String expenseTypeName;

	@ApiModelProperty(value = "父费用类型id")
	private String parentExpenseTypeId;

	@ApiModelProperty(value = "父费用类型编码")
	private String parentExpenseTypeCode;


	@ApiModelProperty(value = "父费用类型名称")
	private String parentExpenseTypeName;


	@ApiModelProperty(value = "原币种")
	private String currency;

	@ApiModelProperty(value = "结算汇率")
	private BigDecimal exchangeRate;


	public InvoiceDetailForm() {
		super();
	}
	//有几个字段不一样,需要转一下
	public InvoiceDetailForm(InvoiceDetail invoiceDetail) {
		super();
		BeanUtils.copyProperties(invoiceDetail, this);
		this.taxRate =  invoiceDetail.getRate();
		this.price = invoiceDetail.getLocalPrice();
		this.receiptItemId = invoiceDetail.getDeliveryItemId();
		this.materialId = invoiceDetail.getProductId();
		this.materialCode = invoiceDetail.getProductCode();
		this.materialName = invoiceDetail.getProductName();
		this.specification = invoiceDetail.getProductSpecification();
		this.materialUnit = invoiceDetail.getProductUnit();
		this.code = invoiceDetail.getDeliveryNo();
		this.countQuantity = invoiceDetail.getDeliveryQuantity();
	}

	public InvoiceDetailForm(ExpenseDetail expenseDetail, Payment payment,BigDecimal exchangeRate) {
		this.inTax = Optional.ofNullable(expenseDetail.getAmount()).map(amount -> amount.multiply(exchangeRate)).orElse(null) ;
		this.exTax = Optional.ofNullable(expenseDetail.getExTax()).map(exTax -> exTax.multiply(exchangeRate)).orElse(null) ;
		this.tax = Optional.ofNullable(expenseDetail.getTax()).map(tax -> tax.multiply(exchangeRate)).orElse(null) ;
		this.rate = NumberUtil.null2Zero(expenseDetail.getTaxRate()) ;
		if (BigDecimal.ZERO.compareTo(expenseDetail.getTaxRate()) == 0){
			this.exTax = this.inTax ;
			this.tax = BigDecimal.ZERO ;
		}
		this.expenseTypeId = expenseDetail.getExpenseTypeId();
		this.expenseTypeName = expenseDetail.getExpenseTypeName();
		this.parentExpenseTypeId = expenseDetail.getParentExpenseTypeId();
		this.parentExpenseTypeName = expenseDetail.getParentExpenseTypeName();
		this.parentExpenseTypeCode = expenseDetail.getParentExpenseTypeCode();
		//计算未税单价 expenseDetail.getPrice()是含税的，保留两位小数 this.rate是税率
		this.localPrice = NumberUtil.null2Zero(expenseDetail.getPrice()).divide(BigDecimal.ONE.add(this.rate.divide(BigDecimal.valueOf(100))), 2, RoundingMode.UP);
		this.invoiceQuantity = expenseDetail.getNums();
		this.deliveryId = payment.getId();
		this.deliveryItemId = expenseDetail.getId();
	}

	public InvoiceDetailForm(ExpenseDetail expenseDetail, CollectionVO collectionVO, BigDecimal exchangeRate) {
		this.inTax = Optional.ofNullable(expenseDetail.getAmount()).map(amount -> amount.multiply(exchangeRate)).orElse(null) ;
		this.exTax = Optional.ofNullable(expenseDetail.getExTax()).map(exTax -> exTax.multiply(exchangeRate)).orElse(null) ;
		this.tax = Optional.ofNullable(expenseDetail.getTax()).map(tax -> tax.multiply(exchangeRate)).orElse(null) ;
		this.rate = NumberUtil.null2Zero(expenseDetail.getTaxRate()) ;
		if (BigDecimal.ZERO.compareTo(expenseDetail.getTaxRate()) == 0){
			this.exTax = this.inTax ;
			this.tax = BigDecimal.ZERO ;
		}
		this.expenseTypeId = expenseDetail.getExpenseTypeId();
		this.expenseTypeName = expenseDetail.getExpenseTypeName();
		this.parentExpenseTypeId = expenseDetail.getParentExpenseTypeId();
		this.parentExpenseTypeName = expenseDetail.getParentExpenseTypeName();
		this.parentExpenseTypeCode = expenseDetail.getParentExpenseTypeCode();

		//计算未税单价 expenseDetail.getPrice()是含税的，保留两位小数 this.rate是税率
		this.localPrice = NumberUtil.null2Zero(expenseDetail.getPrice()).divide(BigDecimal.ONE.add(this.rate.divide(BigDecimal.valueOf(100))), 2, RoundingMode.UP);
		this.invoiceQuantity = expenseDetail.getNums();
		this.deliveryId = collectionVO.getId();
		this.deliveryItemId = expenseDetail.getId();

	}

	public InvoiceDetail formCovertToInvoice() {
		InvoiceDetail invoiceDetail = new InvoiceDetail();
		BeanUtils.copyProperties(this, invoiceDetail);
		if (ObjectUtil.isNotEmpty(this.taxRate) && (invoiceDetail.getRate() == null || !ObjectUtil.equal(this.taxRate,invoiceDetail.getRate()))) {
			invoiceDetail.setRate( this.taxRate);
		}
		if (invoiceDetail.getLocalPrice() == null) {
			invoiceDetail.setLocalPrice(this.price);
		}
		if (invoiceDetail.getDeliveryItemId() == null) {
			invoiceDetail.setDeliveryItemId(this.receiptItemId);
		}
		if (invoiceDetail.getProductId() == null) {
			invoiceDetail.setProductId(this.materialId);
		}
		if (invoiceDetail.getProductCode() == null) {
			invoiceDetail.setProductCode(this.materialCode);
		}
		if (invoiceDetail.getProductName() == null) {
			invoiceDetail.setProductName(this.materialName);
		}
		if (invoiceDetail.getProductSpecification() == null) {
			invoiceDetail.setProductSpecification(this.specification);
		}
		if (invoiceDetail.getProductUnit() == null) {
			invoiceDetail.setProductUnit(this.materialUnit);
		}
		if (invoiceDetail.getDeliveryNo() == null) {
			invoiceDetail.setDeliveryNo(this.code);
		}
		if (invoiceDetail.getDeliveryQuantity() == null) {
			invoiceDetail.setDeliveryQuantity(this.countQuantity);
		}
		return invoiceDetail;
	}

	@ApiModelProperty(value = "本位币单价 入库成本价")
    private BigDecimal costPrice;
}
