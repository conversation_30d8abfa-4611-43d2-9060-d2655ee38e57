package com.huayun.modules.erp.pdm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huayun.modules.erp.pdm.constant.enums.product.ProductCodeSettingTypeEnum;
import com.huayun.modules.erp.pdm.entity.ProductCodeSetting;

/**
 * @Description: 产品编码生成规则
 * @Author: jeecg-boot
 * @Date:   2024-09-18
 * @Version: V1.0
 */
public interface IProductCodeSettingService extends IService<ProductCodeSetting> {


    String getNextCode(ProductCodeSettingTypeEnum type, String code);

}
