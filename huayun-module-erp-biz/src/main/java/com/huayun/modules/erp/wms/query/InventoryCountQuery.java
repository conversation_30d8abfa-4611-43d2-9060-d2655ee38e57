package com.huayun.modules.erp.wms.query;

import com.huayun.modules.common.mybatis.BaseQuery;
import com.huayun.modules.erp.wms.constant.enums.count.CountStateEnum;
import com.huayun.modules.erp.wms.constant.enums.count.CountStrategyEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 库存盘点查询条件
 *
 * <AUTHOR>
 * @date 2022/11/10 18:53
 **/
@Data
public class InventoryCountQuery extends BaseQuery {

    /**盘点单号*/
    @ApiModelProperty(value = "盘点单号")
    private String code;

    /**盘点策略*/
    @ApiModelProperty(value = "盘点策略")
    private CountStrategyEnum strategy;

    /**盘点状态*/
    @ApiModelProperty(value = "盘点状态")
    private CountStateEnum state;
    
    /**物料编码*/
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 标签号
     */
    @ApiModelProperty(value = "标签号")
    private String lotNo;
}
