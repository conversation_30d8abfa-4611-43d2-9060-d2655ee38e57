package com.huayun.modules.erp.oms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.common.util.BizAssert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Version;
import java.math.BigDecimal;


/**
 * 寄售库存
 * <AUTHOR>
 * @version 1.0 2024-02-28
 */
@Data
@TableName("oms_consignment_inventory")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ConsignmentInventory extends BaseTenantEntity {

	/**产品ID*/
	private String productId;
	/**产品编码*/
	private String productCode;
	/**客户ID*/
	private String customerId;
	/**库存数量*/
	private BigDecimal quantity;
	/**版本号*/
	@Version
	private Integer version;

	/**
	 * 客户名称（查询值）
	 */
	private transient String customerName;
	/**
	 * 客户编码（查询值）
	 */
	private transient String customerNo;

	/**
	 * 寄售单价
	 */
	private transient BigDecimal price;
	
	/**
	 * 新增寄售
	 * @param quantity
	 */
	public void addQuantity(BigDecimal quantity) {
		this.quantity = this.quantity.add(quantity);
	}

	/**
	 * 销货
	 * @param quantity
	 */
	public void subQuantity(BigDecimal quantity) {
		BigDecimal value = this.quantity.subtract(quantity);
		BizAssert.isTrue(value.compareTo(BigDecimal.ZERO) >= 0, "消耗数量不正确");
		this.quantity = value;
	}

	public ConsignmentInventory(String productId, String productCode, String customerId, BigDecimal quantity) {
		this.productId = productId;
		this.productCode = productCode;
		this.customerId = customerId;
		this.quantity = quantity;
	}
}
