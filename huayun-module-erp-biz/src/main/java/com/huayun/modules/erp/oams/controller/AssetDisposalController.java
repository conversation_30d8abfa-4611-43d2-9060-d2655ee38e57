package com.huayun.modules.erp.oams.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.ArrayListMultimap;
import com.huayun.modules.common.approval.annotation.ApprovalRequired;
import com.huayun.modules.common.constant.Pattern;
import com.huayun.modules.common.log.aspect.annotation.Log;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.erp.oams.constant.AssetChangeTypeConstant;
import com.huayun.modules.erp.oams.constant.OamsConstant;
import com.huayun.modules.erp.oams.constant.enums.DepreciationStateEnum;
import com.huayun.modules.erp.oams.controller.form.AssetDisposalForm;
import com.huayun.modules.erp.oams.controller.form.AssetVoucherForm;
import com.huayun.modules.erp.oams.controller.vo.AssetVO;
import com.huayun.modules.erp.oams.entity.Asset;
import com.huayun.modules.erp.oams.entity.AssetChange;
import com.huayun.modules.erp.oams.entity.AssetDeprecition;
import com.huayun.modules.erp.oams.service.IAssetChangeService;
import com.huayun.modules.erp.oams.service.IAssetDeprecitionService;
import com.huayun.modules.erp.oams.service.IAssetService;
import com.huayun.modules.erp.oams.service.IVoucherService;
import com.huayun.modules.erp.oams.vo.VoucherVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName AssetDisposalController
 * @Description TODO 资产处置
 * <AUTHOR>
 * @Date 2023/9/25 14:31
 */
@Slf4j
@Api(tags = "OAMS-固定资产管理-资产处置")
@RestController
@RequestMapping("/oams")
public class AssetDisposalController {
    @Resource
    private IAssetService assetService;

    @Resource
    private IAssetChangeService assetChangeService;

    @Resource
    private IAssetDeprecitionService assetDepreciationService;

    @Resource
    private IVoucherService voucherService;
    /**
     * 添加
     *
     * @paramassetForm
     */
    @ApiOperation(value="固定资产-资产处置", notes="固定资产-资产处置")
    @ApprovalRequired(resourceType = OamsConstant.ASSET_DISPOSAL, approvalType = "资产处置")
    @PostMapping("/assets/disposal")
    @Log(resourceType = OamsConstant.RESOURCE_ASSET, operateType = "资产处置", template = "资产编码：${assetNo}")
    public Result<AssetVO> createDisposalAsset(@Validated @RequestBody AssetDisposalForm assetDisposalForm) {
        //处置状态变更
        Asset asset = assetService.getById(assetDisposalForm.getId());
        BizAssert.isNotEmpty(asset,"不存在该固定资产");
        AssetVO oldAsset = new AssetVO(asset);
        asset.setAssetDisposalState(assetDisposalForm.getAssetDisposalState());
        assetService.updateById(asset);
        AssetVO assetVO = new AssetVO(asset);
        assetVO.setOldAsset(oldAsset);
        return Result.OK("提交成功！", assetVO);
    }


    /**
     * 生成凭证表单
     *
     * @param assetVoucherForm
     * 1. 资产变更-原值调整生成凭证
     * 2. 资产变更-累计折旧生成的凭证
     * 3. 资产折旧-生成凭证
     * 4. 资产变更-处置生成凭证
     */
    @ApiOperation(value="生成凭证", notes="生成凭证")
    @PostMapping("/assets/voucher/form")
    public Result<VoucherVO>
    getVoucherForm(@Validated @RequestBody AssetVoucherForm assetVoucherForm) {
        VoucherVO voucherVO = assetService.getVoucherForm(assetVoucherForm);
        return Result.OK(voucherVO);
    }

    /**
     * 生成凭证提交
     *
     * @paramassetForm
     */
    @ApiOperation(value="固定资产-生成凭证-提交", notes="固定资产-生成凭证-提交")
    public Result<AssetVO> submitVoucher(@Validated @RequestBody AssetVoucherForm assetVoucherForm) {
        //处置状态变更
        assetService.submitVoucher(assetVoucherForm);
        return Result.OK("添加成功！");
    }


    @ApiOperation(value="结账时判断变更、折旧凭证", notes="结账时判断变更、折旧凭证")
    @PostMapping("/assets/voucher/judge")
    public Result<List<String>> voucher4Judge(@ApiParam(value = "月份 yyyy-MM", required = true) @RequestParam String month) {
        //判断当月变更是否生成凭证
        List<String> msg = new ArrayList<>();
        Date begin = DateUtil.beginOfMonth(DateUtil.parse(month, Pattern.MONTH));
        Date end = DateUtil.endOfMonth(DateUtil.parse(month, Pattern.MONTH));
        LambdaQueryWrapper<AssetChange> changeWr = new LambdaQueryWrapper<>();
        changeWr.between(AssetChange::getCreateTime,begin,end);
        //changeWr.isNull(AssetChange::getVoucher);
        changeWr.in(AssetChange::getChangeType, AssetChangeTypeConstant.ORIGIN,AssetChangeTypeConstant.TOTAL_DEPRECIATION,AssetChangeTypeConstant.DISPOSAL,AssetChangeTypeConstant.CREATE);
        List<AssetChange> assetChangeList = assetChangeService.getAssetChangeList(changeWr);
        List<String> voucherIds = assetChangeList.stream().map(AssetChange::getVoucher).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        ArrayListMultimap<String, String> voucherNameByCompoundIdsList = voucherService.getVoucherNameByCompoundIdsList(voucherIds);

        if (assetChangeList.size() > voucherNameByCompoundIdsList.keySet().size() ){
            msg.add("资产模块存在未生成凭证的变动单");
        }else {
            msg.add("已完成");
        }

        //判断当月是否折旧
        AssetDeprecition assetDeprecition = assetDepreciationService.getAssetDeprecitionByMonth(month);
        //未进行折旧 标识
        Boolean notDepreciationFlag = false;
        if ((!ObjectUtil.isEmpty(assetDeprecition) && ObjectUtil.equal(DepreciationStateEnum.NOT_YET, assetDeprecition.getState()))
                || ObjectUtil.isEmpty(assetDeprecition)
        ){
            notDepreciationFlag = true;
        }

        if (ObjectUtil.isEmpty(assetDeprecition)){
            //当月如果没有资产需要折旧也可以结账
            List<Asset> assetList = assetService.getCanDepreciationList(end);
            if (assetList.size() == 0){
                notDepreciationFlag = false;
            }
        }
        if (notDepreciationFlag)
        {
            msg.add("资产模块未进行折旧");
        }else {
            msg.add("已完成");
        }
        //判断总账 当月原值合计  = 当月新增凭证金额合计 + 当月原值调整凭证
        Boolean generalLedgerFlag = assetChangeService.judgeBalanceGeneralLedger(begin, end);
        if (!generalLedgerFlag){
            msg.add("资产总账不平");
        }else {
            msg.add("已完成");
        }

        return Result.OK(msg);
    }

}
