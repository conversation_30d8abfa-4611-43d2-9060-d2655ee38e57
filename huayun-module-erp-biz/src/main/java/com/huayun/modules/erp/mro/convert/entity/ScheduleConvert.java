package com.huayun.modules.erp.mro.convert.entity;

import com.huayun.modules.erp.mro.constant.enums.DeviceTypeEnums;
import com.huayun.modules.erp.mro.entity.DeviceSchedule;
import com.huayun.modules.erp.mro.entity.DeviceScheduleDetails;
import com.huayun.modules.erp.mro.service.model.CalculateDeviceScheduleModel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/03/03/9:49
 */
@Mapper(imports = {DeviceTypeEnums.class})
public interface ScheduleConvert {
    ScheduleConvert INSTANCE = Mappers.getMapper(ScheduleConvert.class);


    @Mapping(source = "deviceCode", target = "scheduleDevCode")
    @Mapping(source = "deviceId", target = "scheduleDevId")
    @Mapping(source = "deviceName", target = "devName")
    @Mapping(target = "scheduleDetailsType", expression = "java(DeviceTypeEnums.DEVICE.getValue())")
    @Mapping(target = "tags", constant = "false")
    DeviceScheduleDetails getDeviceScheduleDetails(CalculateDeviceScheduleModel.CalculateSchedule calculateSchedule);


    @Mapping(source = "devName", target = "scheduleName")
    @Mapping(source = "scheduleDetailsType", target = "scheduleType")
    DeviceSchedule getDeviceSchedule(DeviceScheduleDetails deviceScheduleDetails);


}
