package com.huayun.modules.erp.oams.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.constant.Pattern;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.common.entity.UploadFile;
import com.huayun.modules.common.mybatis.UploadFileHandler;
import com.huayun.modules.erp.oams.constant.enums.OrderTypeEnum;
import com.huayun.modules.erp.oams.constant.enums.TransactionTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/17
 */
@Data
@TableName(value = "oams_transaction", autoResultMap = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "交易表", description = "交易记录")
public class Transaction extends BaseTenantEntity {

    @ApiModelProperty(value = "交易类型")
    private TransactionTypeEnum type;
    @ApiModelProperty(value = "申请id")
    private String applicationId;
    @ApiModelProperty(value = "交易订单id")
    private String orderId;
    @ApiModelProperty(value = "交易订单类型")
    private OrderTypeEnum orderType;
    @ApiModelProperty(value = "货币")
    private String currency;
    @ApiModelProperty(value = "结算汇率")
    private BigDecimal exchangeRate;
    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;
    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private java.math.BigDecimal discountAmount;
    @ApiModelProperty(value = "收款单/请款单剩余未付金额")
    private BigDecimal remainAmount;
    @ApiModelProperty(value = "备注")
    private String remarks;
    @ApiModelProperty(value = "附件")
    @TableField(typeHandler = UploadFileHandler.class)
    private List<UploadFile> attachments;
    @ApiModelProperty(value = "凭证")
    private String voucherId;
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.DATE)
    @DateTimeFormat(pattern = Pattern.DATE)
    @ApiModelProperty(value = "实际交易日期")
    private Date transactionDate;
    @ApiModelProperty(value = "收款银行 ID")
    private java.lang.String bank;
    @ApiModelProperty(value = "手续费")
    private BigDecimal bankCharge;
    @ApiModelProperty("收款方式")
    private String transactionMethod;
}
