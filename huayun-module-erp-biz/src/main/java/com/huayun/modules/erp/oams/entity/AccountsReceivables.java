package com.huayun.modules.erp.oams.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.constant.Pattern;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.erp.oams.handle.AdjustmentOprHandle;
import com.huayun.modules.erp.oams.util.MonthTransactionTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 应收账单
 * @TableName oams_accounts_receivables
 */
@Data
@TableName(value = "oams_accounts_receivables",autoResultMap = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(value = "应收账单", description = "应付账款")
public class AccountsReceivables extends BaseTenantEntity {
    /**
     * 供应商id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 结算方式
     */
    private String payMethod;

    /**
     * 结算方式名称
     */
    private String payMethodName;

    /**
     * 期初余额
     */
    private BigDecimal initialBalance;

    /**
     * 未收记录
     */
    @TableField(typeHandler = MonthTransactionTypeHandler.class)
    private List<MonthTransaction> uncollectedHistory;

    /**
     * 未收总额
     */
    private BigDecimal totalUncollected;

    /**
     * 本期应收
     */
    private BigDecimal currentReceivable;

    /**
     * 本期已收
     */
    private BigDecimal currentHasReceived;

    /**
     * 期末余额
     */
    private BigDecimal endingBalance;

    /**
     * 结算时间
     */
    @DateTimeFormat(pattern = Pattern.MONTH)
    private Date settlementTime;

    /**
     * 调账金额
     */
    private BigDecimal adjustmentAccount;
    @ApiModelProperty("调整历史")
    @TableField(typeHandler =  AdjustmentOprHandle.class)
    private List<AdjustmentOperation> adjustmentHistory;

    /**
     * 是否导入数据，0：非导入数据 1：导入数据
     */
    private Boolean imported;

    public void updateEndingBalance() {
        //未考虑调账金额时，期末=期初+本期应收-本期已收  
        //考虑调账金额时，业务需求调x元，本期应收-x元，期末余额加x元
        //故公式调账为期末=期初+本期应收-本期已收+2*调账金额
        this.initialBalance = Optional.ofNullable(this.getInitialBalance()).orElse(BigDecimal.ZERO);
        this.currentReceivable = Optional.ofNullable(this.getCurrentReceivable()).orElse(BigDecimal.ZERO);
        this.currentHasReceived = Optional.ofNullable(this.getCurrentHasReceived()).orElse(BigDecimal.ZERO);
        this.adjustmentAccount = Optional.ofNullable(this.getAdjustmentAccount()).orElse(BigDecimal.ZERO);
        this.endingBalance = this.initialBalance.add(this.currentReceivable).subtract(this.currentHasReceived)
                .add(this.adjustmentAccount.multiply(new BigDecimal("2")));
    }

    public AccountsReceivables(AccountsReceivables oldAP) {
        this.setCustomerId(oldAP.getCustomerId());
        this.setCustomerName(oldAP.getCustomerName());
        this.setPayMethod(oldAP.getPayMethod());
        this.setPayMethodName(oldAP.getPayMethodName());
        this.setSettlementTime(DateUtil.beginOfMonth(new Date()));
        this.setCurrentReceivable(BigDecimal.ZERO);
        this.setCurrentHasReceived(BigDecimal.ZERO);
        this.setInitialBalance(oldAP.getEndingBalance());
        this.setEndingBalance(this.getInitialBalance());
        this.setTenantId(oldAP.getTenantId());
    }

    public void updateByOverview(AccountsReceivables overview) {

      if (ObjectUtil.isNotEmpty(overview)){
          BeanUtil.copyProperties(overview,this,"currentReceivable","currentHasReceived","totalUncollected");
      }
        this.adjustmentAccount = Optional.ofNullable(this.getAdjustmentAccount()).orElse(BigDecimal.ZERO);
        this.currentReceivable = Optional.ofNullable(this.getCurrentReceivable()).orElse(BigDecimal.ZERO);
        this.setCurrentReceivable(this.getCurrentReceivable().subtract(this.getAdjustmentAccount()));
        this.updateEndingBalance();
    }
}