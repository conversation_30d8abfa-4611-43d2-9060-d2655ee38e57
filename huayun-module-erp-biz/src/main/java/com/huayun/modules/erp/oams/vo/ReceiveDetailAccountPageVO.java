package com.huayun.modules.erp.oams.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName ReceiveDetailAccountPageVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/29 16:41
 */
@Data
@AllArgsConstructor
public class ReceiveDetailAccountPageVO {
    @ApiModelProperty("分页数据")
    IPage<ReceiveDetailAccountVO> pageData;

    @ApiModelProperty("合计数据")
    ReceiveDetailAccountVO totalData;
}
