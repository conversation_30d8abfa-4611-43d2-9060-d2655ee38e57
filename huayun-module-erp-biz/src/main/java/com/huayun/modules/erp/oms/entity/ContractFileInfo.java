package com.huayun.modules.erp.oms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.common.entity.UploadFile;
import com.huayun.modules.common.mybatis.UploadFileHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 合同文件信息
 * @Author: huayun
 * @Date: 2022-07-12
 * @Version: V1.0
 */
@Data
@TableName(value = "oms_contract_file_info", autoResultMap = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ContractFileInfo extends BaseTenantEntity {

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 文件标题
     */
    private String title;

    /**
     * 附件
     */
    @TableField(typeHandler = UploadFileHandler.class)
    private UploadFile attachment;

    /**
     * 是否为正式合同附件
     */
    private Boolean formal;
}
