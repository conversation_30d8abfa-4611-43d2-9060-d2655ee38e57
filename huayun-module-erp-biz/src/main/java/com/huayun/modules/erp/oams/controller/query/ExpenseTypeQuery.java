package com.huayun.modules.erp.oams.controller.query;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huayun.modules.common.mybatis.BaseQuery;
import com.huayun.modules.erp.oams.constant.enums.ExpenseSourceEnum;
import com.huayun.modules.erp.oams.entity.ExpenseType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;

/**
 * 费用类型表
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExpenseTypeQuery extends BaseQuery {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 类型(收入：IN 支出：OUT)
     */
    @ApiModelProperty(value = "类型(收入：IN 支出：OUT)")
    private String type;
    /**
     * 是否为初始值
     */
    @ApiModelProperty(value = "是否为初始值 0：否 1：是")
    private Integer init;
    /**
     * 0停用 1正常
     */
    @ApiModelProperty(value = "0停用 1正常")
    private Integer status;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    private String typeId;

    /**
     * 父类型编码
     */
    @ApiModelProperty(value = "父类型编码")
    private String parentCode;




    /**
     * 类型(大类)：0 类别（小类）：1
     */
    @ApiModelProperty(value = "类型(大类)：0 类别（小类）：1")
    private Integer categoryType;

    private Boolean filterReturn;


    @ApiModelProperty(value = "是否显示基础数据")
    private Boolean showBase;

    public QueryWrapper<ExpenseType> createQueryWrapper() {
        QueryWrapper<ExpenseType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(this.code), "code", this.code);
        queryWrapper.eq(ObjectUtil.isNotEmpty(this.name), "name", this.name);
        queryWrapper.eq(ObjectUtil.isNotEmpty(this.type), "type", this.type);
        queryWrapper.eq(ObjectUtil.isNotEmpty(this.init), "init", this.init);
        queryWrapper.eq(ObjectUtil.isNotEmpty(this.typeId), "type_id", this.typeId);
        queryWrapper.eq(ObjectUtil.isNotEmpty(this.categoryType), "category_type", this.categoryType);
        queryWrapper.eq(ObjectUtil.isNotEmpty(this.status), "status", this.status);
        queryWrapper.notIn(BooleanUtil.isTrue(this.filterReturn), "code", Arrays.asList(ExpenseSourceEnum.OUTCING_RETURN_COLLECTION.getValue(),
                ExpenseSourceEnum.PURCHASE_RETURN_COLLECTION.getValue()));
        //queryWrapper.eq(!BooleanUtil.isTrue(showBase),"init",false);
        if (ObjectUtil.equal(this.categoryType,0)){
            queryWrapper.orderByAsc("create_time");
        }else {
            queryWrapper.orderByAsc("type_id","create_time");
        }
        return queryWrapper;
    }


}
