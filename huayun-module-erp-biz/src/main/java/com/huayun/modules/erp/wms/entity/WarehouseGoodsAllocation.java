package com.huayun.modules.erp.wms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.common.service.AdvanceParameter;
import com.huayun.modules.erp.wms.constant.enums.warehouse.WarehousePropertyEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 仓库货位管理
*
* <AUTHOR> am I?
* @date   2022/08/23
*/
@Data
@TableName("wms_warehouse_goods_allocation")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WarehouseGoodsAllocation extends BaseTenantEntity implements AdvanceParameter {
    
	/**仓库id*/
	private java.lang.String warehouseId;

	/**货位编号*/
	private java.lang.String goodsAllocationCode;

	/**位置描述*/
	private java.lang.String description;

	/**是否启用*/
	private Boolean enable;

	/**是否为初始货位*/
	private Boolean initial;

	/**仓库属性（枚举）*/
	private WarehousePropertyEnum property;

	//以下两个字段为导入时组装数据使用，不参与序列化，分别为库区编码和库区名称

	@TableField(exist = false)
	private String warehouseNameStr;

	@TableField(exist = false)
	private String warehouseCodeStr;


	@Override
	public String getPk() {
		return this.getId();
	}

	@Override
	public void setParentId(String parentId) {

	}
}
