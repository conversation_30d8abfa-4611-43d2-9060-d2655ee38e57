package com.huayun.modules.erp.oams.amoeba.controller;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.huayun.modules.erp.mms.service.IOrganizationService;
import com.huayun.modules.erp.oams.amoeba.controller.form.AmoebaUnitForm;
import com.huayun.modules.erp.oams.amoeba.controller.form.AmoebaUnitMapForm;
import com.huayun.modules.erp.oams.amoeba.controller.query.AmoebaUnitQuery;
import com.huayun.modules.erp.oams.amoeba.controller.vo.AmoebaUnitVO;
import com.huayun.modules.erp.oams.amoeba.entity.AmoebaUnit;
import com.huayun.modules.erp.oams.amoeba.entity.ElecPrice;
import com.huayun.modules.erp.oams.amoeba.service.IAmoebaUnitService;
import com.huayun.modules.erp.oams.amoeba.service.IElecPriceService;
import com.huayun.modules.erp.oams.controller.IElecPriceController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/14
 */
@Slf4j
@Api(tags = "OAMS-阿米巴电价配置管理")
@RestController
public class ElecPriceController implements IElecPriceController{

    @Resource
    private IAmoebaUnitService amoebaUnitService;
    @Resource
    private IOrganizationService organizationService;
    @Resource
    private IElecPriceService elecPriceService;


	@Override
	public Result<ElecPriceDTO> requestGet() {
		List<ElecPrice> list = elecPriceService.list(); 	
		ElecPrice price = new ElecPrice();
		ElecPriceDTO elecPriceDTO = new ElecPriceDTO();
		if(list!=null && !list.isEmpty()) {
			price = list.get(0);
			elecPriceDTO.setElecPrice(price.getPrice());
		}
		BeanUtils.copyProperties(price, elecPriceDTO);
		return Result.ok(elecPriceDTO);
	}
}
