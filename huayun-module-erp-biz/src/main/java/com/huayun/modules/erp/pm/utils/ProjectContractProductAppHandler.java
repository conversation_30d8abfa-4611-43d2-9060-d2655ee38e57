package com.huayun.modules.erp.pm.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.huayun.modules.erp.pm.vo.ProjectContractProductAppVO;
import org.apache.commons.lang.StringUtils;

import java.util.List;
/**
 * <AUTHOR>
 */
public class ProjectContractProductAppHandler extends FastjsonTypeHandler {

    private static final String ARRAY_START_SYMBOL = "[";
    private static final String OBJECT_START_SYMBOL = "{";

    public ProjectContractProductAppHandler(Class<?> type) {
        super(type);
    }
    @Override
    protected Object parse(String json) {
        if(StringUtils.isNotEmpty(json)) {
            //array类型
            if(json.startsWith(ARRAY_START_SYMBOL)) {
                List<ProjectContractProductAppVO> loadFiles = JSON.parseArray(json, ProjectContractProductAppVO.class);
                return (loadFiles);
            }
            //对象类型
            if(json.startsWith(OBJECT_START_SYMBOL)) {
            	ProjectContractProductAppVO loadFiles = JSON.parseObject(json, ProjectContractProductAppVO.class);
                return  (loadFiles);
            }

        }
        return null;

    }
}
