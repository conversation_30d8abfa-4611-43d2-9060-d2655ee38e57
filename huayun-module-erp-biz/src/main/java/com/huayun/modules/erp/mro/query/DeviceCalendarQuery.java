package com.huayun.modules.erp.mro.query;

import com.huayun.modules.common.entity.BaseEntity;
import com.huayun.modules.erp.mro.constant.enums.ScheduleTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * TODO description
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceCalendarQuery extends BaseEntity {

    /**
     * 设备ID或者工具ID
     */
    @ApiModelProperty(value = "设备ID或者工具ID")
    private List<String> deviceIds;
    /**
     * 是否被替换
     */
    @ApiModelProperty(value = "是否被替换")
    private ScheduleTypeEnums ScheduleType;
    /**
     * 排程码
     */
    @ApiModelProperty(value = "排程码",required = true )
    private String scheduleCode;
}
