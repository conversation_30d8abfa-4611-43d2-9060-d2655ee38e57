package com.huayun.modules.erp.mro.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.mro.constant.enums.DeviceTypeEnums;
import com.huayun.modules.erp.mro.dao.IDeviceScheduleDao;
import com.huayun.modules.erp.mro.dao.IDeviceScheduleDetailDao;
import com.huayun.modules.erp.mro.entity.*;
import com.huayun.modules.erp.mro.form.DeviceScheduleManufactureForm;
import com.huayun.modules.erp.mro.mapper.*;
import com.huayun.modules.erp.mro.query.DeviceScheduleQuery;
import com.huayun.modules.erp.mro.service.IDeviceInfoService;
import com.huayun.modules.erp.mro.service.IDeviceScheduleService;
import com.huayun.modules.erp.mro.service.IDeviceToolInfoService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
/**
 * 设备排程记录
 *
 * <AUTHOR>
 * @date 2022/08/22
 */
@Service
public class DeviceScheduleServiceImpl implements IDeviceScheduleService {
    @Resource
    private DeviceInfoMapper deviceInfoMapper;
    @Resource
    private DeviceScheduleMapper deviceScheduleMapper;
    @Resource
    private DeviceToolInfoMapper deviceToolInfoMapper;
    @Resource
    private DeviceScheduleDetailsMapper scheduleDetailsMapper;
    @Resource
    private ToolAssociatedEquipmentMapper associatedEquipmentMapper;

    @Resource
    private IDeviceInfoService deviceInfoService;

    @Resource
    private IDeviceScheduleDao deviceScheduleDao;

    @Resource
    private IDeviceScheduleDetailDao deviceScheduleDetailDao;

    @Resource
    private IDeviceToolInfoService toolInfoService;


    /**
     * 初始化单个设备的产量值 累加和
     */
    private long total = 0;
    private Integer tag = 0;

    private Map<String, DeviceModels> modelsMap;
    private Map<String, DeviceCodes> codesMap;


    /**
     * 排程过滤
     * @param keyExtractor
     * @param <T>
     * @return
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }


    /***
     *根据排程码查询排程信息
     * @param deviceScheduleQuery 查询参数
     * @return
     */
    @Override
    public   IPage<DeviceSchedule>  findSchedulesByScheduleCode(DeviceScheduleQuery deviceScheduleQuery) {

        LambdaQueryWrapper<DeviceScheduleDetails> wrapper=new LambdaQueryWrapper<>();
        if (deviceScheduleQuery.getType().equals(DeviceTypeEnums.DEVICE.getValue())) {
            wrapper.eq(DeviceScheduleDetails::getScheduleDetailsType, DeviceTypeEnums.DEVICE.getValue());
        } else {
            wrapper.eq(DeviceScheduleDetails::getScheduleDetailsType, DeviceTypeEnums.TOOL.getValue());
        }
        if (deviceScheduleQuery.getScheduleCode() != null) {
            wrapper.and(w -> {
                w.eq(deviceScheduleQuery.getScheduleCode() != null, DeviceScheduleDetails::getScheduleCode, deviceScheduleQuery.getScheduleCode());
            });
        }
        IPage<DeviceSchedule> page = scheduleDetailsMapper.selectPage(new Page<>(deviceScheduleQuery.getPage(),deviceScheduleQuery.getPageSize()), wrapper).convert(DeviceSchedule::new);
        return page;
    }

    /**
     * 移除今日排程
     * @param scheduleManufactureForm
     */
    @Override
    public void removeSchedule(DeviceScheduleManufactureForm scheduleManufactureForm) {
        DeviceScheduleDetails deviceSchedule=null;
        QueryWrapper<DeviceScheduleDetails> wrapper=new QueryWrapper<>();
        wrapper.eq("schedule_dev_code",scheduleManufactureForm);
        //移除今日排程
        if (1==scheduleManufactureForm.getScheduleType()){

            DateTime endDate = DateUtil.endOfDay(scheduleManufactureForm.getScheduleStartTime());
        }else {
            deviceSchedule = new DeviceScheduleDetails();
            deviceSchedule.setEndTime(scheduleManufactureForm.getScheduleStartTime());
        }
        scheduleDetailsMapper.update(deviceSchedule,wrapper);
    }

    /***
     * 根据排程码删除排程信息
     * @param scheduleCode 排程码
     * @return void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDeviceSchedule(String scheduleCode) {
        //deviceScheduleMapper.deleterDeviceSchedule(scheduleCode);
        scheduleDetailsMapper.deleteDeviceScheduleDetails(scheduleCode);
    }

    /**
     * 保存排程数据
     *
     * @param deviceScheduleListHashMap
     * <AUTHOR>
     * @date 2023/3/7 23:21
     */
    private void saveScheduleData(HashMap<DeviceSchedule, List<DeviceScheduleDetails>> deviceScheduleListHashMap) {
        Set<DeviceSchedule> deviceSchedules = deviceScheduleListHashMap.keySet();
        //保存主记录
        deviceScheduleDao.saveBatch(deviceSchedules);
        for (Map.Entry<DeviceSchedule, List<DeviceScheduleDetails>> deviceScheduleListEntry : deviceScheduleListHashMap.entrySet()) {
            DeviceSchedule deviceSchedule = deviceScheduleListEntry.getKey();
            for (DeviceScheduleDetails deviceScheduleDetails : deviceScheduleListEntry.getValue()) {
                deviceScheduleDetails.setScheduleId(deviceSchedule.getId());
            }
            //保存设备详情
            deviceScheduleDetailDao.saveBatch(deviceScheduleListEntry.getValue());
        }
    }

    @Override
    public void updateDoneFlag(String scheduleCode, Boolean doneFlag) {
        LambdaQueryWrapper<DeviceSchedule> deviceScheduleWrapper = new LambdaQueryWrapper<>();
        deviceScheduleWrapper.eq(DeviceSchedule::getScheduleCode, scheduleCode);
        List<DeviceSchedule> deviceScheduleList = deviceScheduleDao.list(deviceScheduleWrapper);
        if (ObjectUtils.isNotEmpty(deviceScheduleList)) {
            List<String> scheduleIds = deviceScheduleList.stream().map(DeviceSchedule::getId).collect(Collectors.toList());
            deviceScheduleList.forEach(item -> item.setDoneFlag(doneFlag));
            deviceScheduleDao.updateBatchById(deviceScheduleList);

            LambdaQueryWrapper<DeviceScheduleDetails> deviceScheduleDetailWrapper = new LambdaQueryWrapper<>();
            deviceScheduleDetailWrapper.in(DeviceScheduleDetails::getScheduleId, scheduleIds);
            List<DeviceScheduleDetails> detailsList = deviceScheduleDetailDao.list(deviceScheduleDetailWrapper);
            detailsList.forEach(item -> item.setDoneFlag(doneFlag));
            deviceScheduleDetailDao.updateBatchById(detailsList);
        }
    }

}
