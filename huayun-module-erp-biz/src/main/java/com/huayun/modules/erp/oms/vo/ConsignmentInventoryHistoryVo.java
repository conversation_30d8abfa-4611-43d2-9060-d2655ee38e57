package com.huayun.modules.erp.oms.vo;

import com.huayun.modules.common.vo.BaseVO;
import com.huayun.modules.erp.oms.constant.enums.ConsignmentTransferType;
import com.huayun.modules.erp.oms.entity.OrderDeliveryItem;
import com.huayun.modules.erp.wms.entity.model.ConsignmentTransferLogModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

@Data
public class ConsignmentInventoryHistoryVo extends BaseVO {
    
    @ApiModelProperty("类型")
    ConsignmentTransferType type;
    
    @ApiModelProperty("关联单号")
    String code;
    
    @ApiModelProperty("数量")
    BigDecimal deliveryQuantity;

    @ApiModelProperty("产品编码")
    String productCode;

    @ApiModelProperty("产品名称")
    String productName;

    @ApiModelProperty("产品规格")
    String productSpecification;

    @ApiModelProperty("产品型号")
    String productModel;

    @ApiModelProperty("产品单位")
    String productUnit;

    @ApiModelProperty("单价")
    BigDecimal price;

    @ApiModelProperty("金额")
    BigDecimal amount;

    public BigDecimal getDeliveryQuantity() {
//        if (OrderDeliveryType.Sell.equals(type) || OrderDeliveryType.Return.equals(type)) {
//            return deliveryQuantity.negate();
//        }
        return deliveryQuantity;
    }
    
    public ConsignmentInventoryHistoryVo(ConsignmentTransferLogModel model) {
        BeanUtils.copyProperties(model, this);
        this.productCode = model.getMaterialCode();
        this.productName = model.getMaterialName();
        this.productSpecification = model.getMaterialSpecification();
//        this.productModel = model.getMaterialModel();
        this.productUnit = model.getMaterialUnit();
        this.deliveryQuantity = model.getQuantity();
//        this.price = model.getPrice();
//        this.amount = model.getAmount();
    }

    public ConsignmentInventoryHistoryVo(OrderDeliveryItem item) {
        BeanUtils.copyProperties(item, this);
    }
}
