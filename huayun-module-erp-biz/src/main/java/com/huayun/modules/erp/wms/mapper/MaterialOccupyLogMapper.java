package com.huayun.modules.erp.wms.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.huayun.modules.common.mybatis.query.MPJLambdaWrapperX;
import com.huayun.modules.erp.wms.MaterialOccupyLogQueryDTO;
import com.huayun.modules.erp.wms.entity.MaterialOccupyLog;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/04/02/21:01
 */
public interface MaterialOccupyLogMapper extends MPJBaseMapper<MaterialOccupyLog> {


    /**
     * CAHXUN1
     *
     * @param materialOccupyLogQueryDTO
     * @return java.util.List<com.huayun.modules.erp.wms.entity.MaterialOccupyLog>
     * <AUTHOR>
     * @date 2025/4/7 17:28
     */
    default List<MaterialOccupyLog> selectMaterialOccupyLogList(MaterialOccupyLogQueryDTO materialOccupyLogQueryDTO) {
        MPJLambdaWrapperX<MaterialOccupyLog> lambdaQueryWrapper = new MPJLambdaWrapperX<>();
        //条件查询
        lambdaQueryWrapper.eqIfPresent(MaterialOccupyLog::getMaterialId, materialOccupyLogQueryDTO.getMaterialId());
        lambdaQueryWrapper.eqIfPresent(MaterialOccupyLog::getMainId, materialOccupyLogQueryDTO.getMainId());
        lambdaQueryWrapper.eqIfPresent(MaterialOccupyLog::getOccupyInventoryDetailId, materialOccupyLogQueryDTO.getOccupyInventoryDetailId());
        if (lambdaQueryWrapper.isEmptyOfWhere()) {
            return Collections.emptyList();
        }
        //排除一些没有实际扣减计划占用的记录
        lambdaQueryWrapper.apply("chang_occupy_qty != 0");
        //查询字段
        lambdaQueryWrapper.selectAll(MaterialOccupyLog.class);
        return selectList(lambdaQueryWrapper);
    }

}
