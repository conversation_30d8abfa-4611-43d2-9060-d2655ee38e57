package com.huayun.modules.erp.oams.controller.query;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.erp.oams.constant.enums.TransactionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @ClassName SupplierTransactionQuery
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/31 13:58
 */
@Data
public class SupplierTransactionQuery {
    @ApiModelProperty(value = "交易类型")
    private TransactionTypeEnum type;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date operateStartTime = DateUtil.offsetMonth(new Date(), -2);
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date operateEndTime = new Date();
    @ApiModelProperty("页数")
    private Integer page = 1;
    @ApiModelProperty("分页大小")
    private Integer pageSize = 10;
}
