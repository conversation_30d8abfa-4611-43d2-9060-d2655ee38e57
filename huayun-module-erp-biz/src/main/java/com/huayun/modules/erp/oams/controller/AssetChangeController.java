package com.huayun.modules.erp.oams.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.oams.controller.form.AssetChangeForm;
import com.huayun.modules.erp.oams.controller.query.AssetChangeQuery;
import com.huayun.modules.erp.oams.entity.AssetChange;
import com.huayun.modules.erp.oams.service.IAssetChangeService;
import com.huayun.modules.erp.oams.vo.AssetChangeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资产变更表
 *
 * <AUTHOR> am I?
 * @date   2023/09/18
 */
@Slf4j
@Api(tags="资产变更表")
@RestController
@RequestMapping("/oams")
public class AssetChangeController {

    @Resource
    private IAssetChangeService assetChangeService;

    /**
     * 分页列表查询
     *
     * @param assetChangeQuery
     */
    @ApiOperation(value="资产变更表-分页列表查询", notes="资产变更表-分页列表查询")
    @GetMapping("/asset-changes-page")
    public Result<IPage<AssetChangeVO>> queryPageList(AssetChangeQuery assetChangeQuery) {
           assetChangeQuery.setDeleted(false);
           QueryWrapper<AssetChange> pageQueryWrapper = assetChangeQuery.createQueryWrapper();
           Page<AssetChange> assetChangePage = new Page<>(assetChangeQuery.getPage(), assetChangeQuery.getPageSize());
           IPage<AssetChangeVO> assetChangePageList = assetChangeService.getAssetChangePage(assetChangePage, pageQueryWrapper);
           return Result.OK(assetChangePageList);
    }


    /**
     * 列表查询
     *
     * @param assetChangeQuery
     */
    @ApiOperation(value="资产变更表-列表查询", notes="资产变更表-列表查询")
    @GetMapping("/asset-changes")
    public Result<List<AssetChangeVO>> queryList(AssetChangeQuery assetChangeQuery) {
           QueryWrapper<AssetChange> queryWrapper = assetChangeQuery.createQueryWrapper();
           List<AssetChange> assetChangeList = assetChangeService.getAssetChangeList(queryWrapper);
           return Result.OK(assetChangeList.stream().map(AssetChangeVO::new).collect(Collectors.toList()));
    }

    /**
     * 添加
     *
     * @paramassetChangeForm
     */
    @ApiOperation(value="资产变更表-添加", notes="资产变更表-添加")
    @PostMapping("/asset-changes")
    public Result<AssetChangeVO> createAssetChange(@Validated @RequestBody AssetChangeForm assetChangeForm) {
        AssetChange assetChange = assetChangeService.createAssetChange(assetChangeForm);
        return Result.OK("变更成功！", new AssetChangeVO(assetChange));
    }

    /**
     * 编辑
     *
     * @param assetChangeId
     * @param assetChangeForm
     */
    @ApiOperation(value="资产变更表-编辑", notes="资产变更表-编辑")
    @PutMapping("/asset-changes/{assetChangeId}")
    public Result<AssetChangeVO> updateAssetChange(@PathVariable String assetChangeId, @Validated @RequestBody AssetChangeForm assetChangeForm) {
        AssetChange assetChange = assetChangeService.updateAssetChange(assetChangeId, assetChangeForm);
        return Result.OK("编辑成功!", new AssetChangeVO(assetChange));
    }

    /**
     * 通过id删除
     *
     * @param assetChangeId
     */
    @ApiOperation(value="资产变更表-通过id删除", notes="资产变更表-通过id删除")
    @DeleteMapping("/asset-changes/{assetChangeId}")
    public Result<AssetChangeVO> deleteAssetChange(@PathVariable String assetChangeId) {
        assetChangeService.deleteAssetChange(assetChangeId);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param assetChangeId
     */
    @ApiOperation(value="资产变更表-通过id查询", notes="资产变更表-通过id查询")
    @GetMapping("/asset-changes/{assetChangeId}")
    public Result<AssetChangeVO> getAssetChange(@PathVariable String assetChangeId) {
        AssetChange assetChange = assetChangeService.getAssetChange(assetChangeId);
        return Result.OK("查询成功！", new AssetChangeVO(assetChange));
    }
}