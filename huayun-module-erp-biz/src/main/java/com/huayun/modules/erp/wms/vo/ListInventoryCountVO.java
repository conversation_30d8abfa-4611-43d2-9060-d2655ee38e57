package com.huayun.modules.erp.wms.vo;

import com.huayun.modules.common.vo.BaseVO;
import com.huayun.modules.erp.wms.constant.enums.count.CountStateEnum;
import com.huayun.modules.erp.wms.constant.enums.count.CountStrategyEnum;
import com.huayun.modules.erp.wms.entity.model.InventoryCountModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * 库存盘点单据信息
 *
 * <AUTHOR>
 * @date 2022/11/10 18:52
 **/
@Data
@NoArgsConstructor
public class ListInventoryCountVO extends BaseVO {

    /**盘点单号*/
    @ApiModelProperty(value = "盘点单号")
    private String code;

    /**是否为初盘*/
    @ApiModelProperty(value = "是否为初盘")
    private Boolean firstTime;

    /**盘点策略*/
    @ApiModelProperty(value = "盘点策略")
    private CountStrategyEnum strategy;

    /**盘点区域名称*/
    @ApiModelProperty(value = "盘点区域名称")
    private String warehouseName;

    /**盘点完成时间*/
    @ApiModelProperty(value = "盘点完成时间")
    private Date completeTime;

    /**盘点结果是否异常*/
    @ApiModelProperty(value = "盘点结果是否异常")
    private Boolean exception;

    /**盘点状态*/
    @ApiModelProperty(value = "盘点状态")
    private CountStateEnum state;

    public ListInventoryCountVO(InventoryCountModel model) {
        init(model);
    }

    private void init(InventoryCountModel model) {
        BeanUtils.copyProperties(model, this);
    }
}
