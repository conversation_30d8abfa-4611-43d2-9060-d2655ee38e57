package com.huayun.modules.erp.oms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huayun.modules.erp.oms.entity.OrderFileInfo;
import com.huayun.modules.erp.oms.mapper.OrderFileInfoMapper;
import com.huayun.modules.erp.oms.service.IOrderFileInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 订单文件信息
 * @Author: huayun
 * @Date: 2022-07-12
 * @Version: V1.0
 */
@Service
public class OrderFileInfoServiceImpl extends ServiceImpl<OrderFileInfoMapper, OrderFileInfo> implements IOrderFileInfoService {

    @Override
    public OrderFileInfo getByOrderId(String orderId, String type) {
        return this.baseMapper.getByOrderId(orderId, type);
    }

    @Override
    public List<OrderFileInfo> getIgnoreDeletedByColumn(String column, String value) {
        return this.baseMapper.getIgnoreDeletedByColumn(column, value);
    }
}
