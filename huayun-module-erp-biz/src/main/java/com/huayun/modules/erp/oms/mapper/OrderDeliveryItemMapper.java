package com.huayun.modules.erp.oms.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.oms.entity.OrderDeliveryItem;
import com.huayun.modules.erp.oms.entity.model.OrderDeliveryItemModel;
import com.huayun.modules.erp.oms.entity.model.OrderDeliveryItemPriceModel;
import com.huayun.modules.erp.oms.entity.model.OrderDeliveryItemProfitModel;
import com.huayun.modules.erp.oms.entity.model.ProductDeliveryQuantityModel;
import com.huayun.modules.erp.oms.query.OrderDeliveryQuery;
import com.huayun.modules.erp.wms.entity.model.InvoiceDeliveryDetailModel;
import com.huayun.modules.erp.wms.query.InvoiceDeliveryDetailQuery;
import com.huayun.modules.mes.dto.query.ProductionOrderQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface OrderDeliveryItemMapper extends BaseMapper<OrderDeliveryItem> {

    /**
     * 根据发货单id查询清单
     *
     * <AUTHOR>
     * @date 2023/2/25 10:40
     * @param deliveryId
     * @return java.util.List<com.huayun.modules.erp.oms.entity.model.OrderDeliveryItemModel>
     */
    List<OrderDeliveryItemModel> findListByDeliveryId(@Param("deliveryId") String deliveryId);

    /**
     * 列表查询
     *
     * <AUTHOR>
     * @date 2023/2/28 16:42
     * @param wrapper
     * @return java.util.List<com.huayun.modules.erp.oms.entity.model.OrderDeliveryItemModel>
     */
    List<OrderDeliveryItemModel> findList(@Param(Constants.WRAPPER) QueryWrapper<OrderDeliveryItem> wrapper);

    /**
     * findPage:分页查询带价格的出库单明细.
     *
     * <AUTHOR>
     * @param page
     * @param
     * @return
     * @since version 1.0
     */
    IPage<OrderDeliveryItemPriceModel> findPage(Page<OrderDeliveryItem> page, @Param("query")OrderDeliveryQuery query);
    /**
     * 查询产品交付数量
     *
     * <AUTHOR>
     * @date 2023/7/12 17:44
     * @param wrapper
     * @return java.util.List<com.huayun.modules.erp.oms.entity.OrderDeliveryItem>
     */
    List<ProductDeliveryQuantityModel> findProductDeliveryQuantity(@Param(Constants.WRAPPER) QueryWrapper<OrderDeliveryItem> wrapper);


    ProductDeliveryQuantityModel sumDeliveryQuantity(@Param("query")OrderDeliveryQuery query);


    /**
     * sumAmoutMonth:按月统计交付金额.
     *
     * <AUTHOR>
     * @param query
     * @return
     * @since version 1.0
     */
    BigDecimal sumAmoutMonth(@Param("query") ProductionOrderQueryDTO query);

    /**
     * findListForInvoice:发票-查询发货明细.
     * @param page
     * @param queryWrapper
     * @param query
     * @param acSetId
     * @return
     */
    Page<InvoiceDeliveryDetailModel> findListForInvoice(Page<InvoiceDeliveryDetailModel> page, @Param(Constants.WRAPPER) QueryWrapper<InvoiceDeliveryDetailModel> queryWrapper,@Param("query") InvoiceDeliveryDetailQuery query, @Param("acSetId") String acSetId);

    List<OrderDeliveryItemModel> findOqcByDeliveryId(@Param("deliveryId")  String id);

    List<OrderDeliveryItemModel> findByOrderNo(@Param("orderNo") String orderNo);

    IPage<OrderDeliveryItemProfitModel> getSaleProfitReport(Page<OrderDeliveryItemProfitModel> page, @Param("query")OrderDeliveryQuery query);

    List<OrderDeliveryItemProfitModel> sumSaleProfitReport(@Param("query")OrderDeliveryQuery query);


    List<OrderDeliveryItemProfitModel> getSaleProfitReport(@Param("query")OrderDeliveryQuery query);

    /**
     *
     * <AUTHOR>
     * @description 查询出库物料信息和质检记录id
     * @date 2024/12/24 18:11
     * @param ids
     */
    List<OrderDeliveryItemModel> selectListByDeliveryIds(@Param("ids") Set<String> ids);
}
