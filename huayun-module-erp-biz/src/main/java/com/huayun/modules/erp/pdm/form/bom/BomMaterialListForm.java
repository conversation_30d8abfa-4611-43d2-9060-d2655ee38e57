package com.huayun.modules.erp.pdm.form.bom;

import java.util.List;

import javax.validation.Valid;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: BomMaterialListForm   
 * Function: 保存的配置物料列表.    
 * date: 2023年8月22日 下午3:09:42   
 *  
 * <AUTHOR>  
 * @version   
 * @since version 1.0
 */
@Data
@ApiModel(value = "产品元素", description = "产品元素")
public class BomMaterialListForm {

	@Valid
	@ApiModelProperty(value = "保存的配置物料列表")	
    private List<BomMaterialForm> materials;
}
