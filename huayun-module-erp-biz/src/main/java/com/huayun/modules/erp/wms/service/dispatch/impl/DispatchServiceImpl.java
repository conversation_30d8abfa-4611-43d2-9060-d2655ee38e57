package com.huayun.modules.erp.wms.service.dispatch.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.common.entity.UploadFile;
import com.huayun.modules.common.enums.YesOrNOEnum;
import com.huayun.modules.common.log.constant.OperateTypeConstant;
import com.huayun.modules.common.log.util.LogUtils;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.common.util.CodeGenerator;
import com.huayun.modules.erp.pms.entity.OutsourcingOrderItem;
import com.huayun.modules.erp.pms.entity.Supplier;
import com.huayun.modules.erp.pms.mapper.SupplierMapper;
import com.huayun.modules.erp.pms.service.IOutsourcingOrderItemService;
import com.huayun.modules.erp.utils.ValidationUtils;
import com.huayun.modules.erp.wms.constant.WmsCodeConstant;
import com.huayun.modules.erp.wms.constant.WmsConstant;
import com.huayun.modules.erp.wms.constant.biz.InventoryLogTypeConstant;
import com.huayun.modules.erp.wms.constant.enums.dispatch.DispatchStateEnum;
import com.huayun.modules.erp.wms.constant.enums.dispatch.DispatchTypeEnum;
import com.huayun.modules.erp.wms.constant.enums.picking.PickingStateEnum;
import com.huayun.modules.erp.wms.constant.enums.warehouse.WarehouseEnum;
import com.huayun.modules.erp.wms.dao.IDispatchDao;
import com.huayun.modules.erp.wms.entity.*;
import com.huayun.modules.erp.wms.entity.model.*;
import com.huayun.modules.erp.wms.form.DispatchForm;
import com.huayun.modules.erp.wms.message.PickScheduleMessage;
import com.huayun.modules.erp.wms.query.DispatchQuery;
import com.huayun.modules.erp.wms.service.dispatch.IDispatchService;
import com.huayun.modules.erp.wms.service.material.IMaterialService;
import com.huayun.modules.erp.wms.service.material.InventoryLogItemService;
import com.huayun.modules.erp.wms.service.picking.IPickingItemService;
import com.huayun.modules.erp.wms.service.picking.IPickingRecordService;
import com.huayun.modules.erp.wms.service.picking.IPickingService;
import com.huayun.modules.erp.wms.utils.InventoryLogUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发货单据管理
 *
 * <AUTHOR>
 * @date 2023/1/5 12:02
 **/
@Service
public class DispatchServiceImpl implements IDispatchService {

    @Resource
    private IDispatchDao dispatchDao;

    @Resource
    private IPickingService pickingService;

    @Resource
    private IPickingItemService pickingItemService;

    @Resource
    private IPickingRecordService pickingRecordService;

    @Resource
    private CodeGenerator codeGenerator;

    @Resource
    protected IMaterialService materialService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource 
    private SupplierMapper supplierMapper;

    @Resource
    private InventoryLogItemService inventoryLogItemService;

    @Resource
    private IOutsourcingOrderItemService outsourcingOrderItemService;

    @Override
    public IPage<DispatchModel> queryPageList(Page<Dispatch> page, DispatchQuery query) {
        QueryWrapper<Dispatch> wrapper = new QueryWrapper<Dispatch>()
                .eq("dis.deleted", YesOrNOEnum.NO.getValue())
                .eq(StringUtils.isNotEmpty(query.getConsigneeBy()), "dis.consignee_by", query.getConsigneeBy())
                .between(query.getOccStart()!=null, "dis.occ_date", query.getOccStart(), query.getOccEnd())
                .like(StringUtils.isNotEmpty(query.getCode()), "dis.code", query.getCode())
                .like(StringUtils.isNotEmpty(query.getRelationCode()), "dis.relation_code", query.getRelationCode())
                .orderByDesc("dis.create_time");
        if (ObjectUtils.isNotEmpty(query.getState())){
            if (DispatchStateEnum.PICKING.equals(query.getState())){
                wrapper.eq( "dis.state", DispatchStateEnum.TO_DO_DISPATCH)
                        .in("picking.state", PickingStateEnum.PENDING.getValue(), PickingStateEnum.PICKING.getValue(), PickingStateEnum.REVOKE.getValue());
            }else{
                //避免查出待备料的数据
                if(query.getState().equals(DispatchStateEnum.TO_DO_DISPATCH)){
                    wrapper.eq( "dis.state", query.getState())
                            .in("picking.state", PickingStateEnum.COMPLETED, PickingStateEnum.REQUISITIONED);
                }else{
                    //之所以加上 PickingStateEnum.REVOKE.getValue() 是因为”已取消“状态的发料单，可能备料单还在”待取消“状态
                    wrapper.eq( "dis.state", query.getState())
                            .in("picking.state", PickingStateEnum.COMPLETED, PickingStateEnum.REQUISITIONED, PickingStateEnum.REVOKE.getValue(), PickingStateEnum.CANCEL);
                }
            }
        }
        //入库范围时间查询
        if (ObjectUtils.isNotEmpty(query.getStartDate())
                && ObjectUtils.isNotEmpty(query.getEndDate())){
            //申请开始和结束时间都不为空就添加申请时间条件查询；DateUtil.beginOfDay获取一天的开始、DateUtil.endOfDay获取一天的结束
            wrapper.between("dis.dispatch_date",
                    DateUtil.beginOfDay(query.getStartDate()),
                    DateUtil.endOfDay(query.getEndDate()));
        }
        return dispatchDao.queryPage(page, wrapper);
    }

    @Override
    public List<Dispatch> findListByPickingIds(Collection<String> pickingIds) {
        if (CollectionUtils.isEmpty(pickingIds)){
            return Collections.emptyList();
        }
        QueryWrapper<Dispatch> wrapper = new QueryWrapper<Dispatch>()
                .eq("deleted", YesOrNOEnum.NO.getValue())
                .eq("state", DispatchStateEnum.TO_DO_DISPATCH.getValue())
                .in("picking_id", pickingIds);
        return dispatchDao.list(wrapper);
    }

    @Override
    public Dispatch findListByPickingId(String pickingId) {
        QueryWrapper<Dispatch> wrapper = new QueryWrapper<Dispatch>()
                .eq("deleted", YesOrNOEnum.NO.getValue())
               // .eq("state", DispatchStateEnum.TO_DO_DISPATCH.getValue())
                .in("picking_id", pickingId);
        return dispatchDao.getOne(wrapper);
    }

    @Override
    public List<DispatchModel> findList(DispatchQuery query){
        QueryWrapper<Dispatch> wrapper = new QueryWrapper<Dispatch>()
                .eq("dis.deleted", YesOrNOEnum.NO.getValue())
                .eq(StringUtils.isNotEmpty(query.getRelationCode()), "dis.relation_code", query.getRelationCode());
        return dispatchDao.findList(wrapper);
    }

    @Override
    public Dispatch getById(String id) {
        return dispatchDao.getById(id);
    }

    @Override
    public DispatchModel getDispatchById(String id) {
        DispatchModel dispatchModel = dispatchDao.getDetailById(id);
        BizAssert.isNotNull(dispatchModel, "查询失败，发货单据不存在");
        Supplier supplier = supplierMapper.selectById(dispatchModel.getConsigneeBy());
        dispatchModel.setConsigneeCode(supplier.getCode());
        String pickingId = dispatchModel.getPickingId();
        PickingModel picking = pickingService.getPickingDetail(pickingId);
        BizAssert.isNotNull(picking, "查询失败，备料任务不存在");
        //查询备料任务下的物料清单
        List<PickingItemModel> itemModelList;
        //只有待出库时才需要显示合并
        if (DispatchStateEnum.TO_DO_DISPATCH.equals(dispatchModel.getState())
                && StringUtils.isNotBlank(picking.getMergeCode())){
            Set<String> pickIds = picking.getMergePickList()
                    .stream()
                    .map(Picking::getId)
                    .filter(p -> !pickingId.equals(p))
                    .collect(Collectors.toSet());
            List<Dispatch> dispatchList = getMergeDispatches(pickIds);
            //合并单号
            dispatchModel.setMergeCode(picking.getMergeCode());
            //其他委外发料单
            dispatchModel.setOtherDispatchList(dispatchList);

            //合并物料清单
            itemModelList = pickingItemService.findMergeListByPickingId(pickingId, true);
        }else{
            //单个物料清单
            itemModelList = pickingItemService.findMergeListByPickingId(pickingId, false);
        }
        setDispatchItems(dispatchModel, itemModelList);

        return dispatchModel;
    }

    private List<Dispatch> getMergeDispatches(Set<String> pickIds) {
        //根据备料id查询出所有委外发料单号
        LambdaQueryWrapper<Dispatch> wrapper = new LambdaQueryWrapper<Dispatch>()
                .in(Dispatch::getPickingId, pickIds);
        return dispatchDao.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Dispatch> saveDispatch(List<DispatchForm> forms) {
        //数据校验
        BizAssert.isNotEmpty(forms, "保存失败，发货单不存在");
        BizAssert.isFalse(forms.isEmpty(), "保存失败，发货单为空");
        Set<String> pickIds = new HashSet<>();
        for (DispatchForm dispatchForm : forms) {
            ValidationUtils.validateEntity(dispatchForm);
            pickIds.add(dispatchForm.getPickingId());
        }

        //校验是否有发货单使用该备料任务
        QueryWrapper<Dispatch> wrapper = new QueryWrapper<Dispatch>()
                .eq("deleted", YesOrNOEnum.NO.getValue())
                .in("picking_id", pickIds);
        long alikeCount = dispatchDao.count(wrapper);
        BizAssert.isFalse(alikeCount > 0, "保存发货单失败，该备料任务已关联发货单");

        List<Dispatch> dispatchList = forms.stream().map(dispatchForm -> {
            Dispatch dispatch = new Dispatch();
            BeanUtils.copyProperties(dispatchForm, dispatch);
            //出库单号
            dispatch.setCode(codeGenerator.next(WmsCodeConstant.DISPATCH_TITLE));
            dispatch.setState(DispatchStateEnum.TO_DO_DISPATCH);
            return dispatch;
        }).collect(Collectors.toList());
        //保存发货单据
        dispatchDao.saveBatch(dispatchList);

        //保存操作日志
        LogUtils.saveLog(WmsConstant.RESOURCE_OUTSOURCE_DISPATCH, OperateTypeConstant.INSERT, "新增委外发料单据，出库单号：“${code}”", dispatchList);

        //将委外备料单改成已排程
        PickScheduleMessage pickScheduleMessage = new PickScheduleMessage();
        pickScheduleMessage.setPickIds(pickIds);
        rabbitTemplate.convertAndSend(WmsConstant.PICK_SCHEDULING_QUEUE_NAME, pickScheduleMessage);
        return dispatchList;
    }

    private void setDispatchItems(DispatchModel dispatchModel, List<PickingItemModel> pickingItems) {
        List<DispatchItemModel> itemModels = pickingItems.stream().map(item -> {
            DispatchItemModel dispatchItemModel = new DispatchItemModel();
            BeanUtils.copyProperties(item, dispatchItemModel);
            dispatchItemModel.setPickingItemId(item.getId());
            return dispatchItemModel;
        }).collect(Collectors.toList());
        dispatchModel.setDispatchItems(itemModels);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dispatchComplete(Dispatch dispatch) {
        PickingModel picking = pickingService.getPickingDetail(dispatch.getPickingId());
        //校验备料任务是否存在
        BizAssert.isNotEmpty(picking, "出库失败，备料任务不存在");
        //未完成备料不可发货
        BizAssert.isTrue(PickingStateEnum.COMPLETED.equals(picking.getState()), "出库失败，未完成备料不可发货");
        //如果已出库，就不执行以下操作
        BizAssert.isFalse(PickingStateEnum.REQUISITIONED.equals(picking.getState()), "出库失败，不可重复发货");

        //合并发料
        List<Picking> pickingList = Collections.singletonList(picking);
        List<Dispatch> dispatchList = Collections.singletonList(dispatch);
        if (StringUtils.isNotBlank(picking.getMergeCode())){
            pickingList = picking.getMergePickList();
            Set<String> pickIds = pickingList.stream()
                    .map(Picking::getId)
                    .collect(Collectors.toSet());
            dispatchList = getMergeDispatches(pickIds);
            BizAssert.isTrue(pickIds.size() == dispatchList.size(), "出库失败，存在未绑定备料任务的发料单");
        }

        //交付出库状态修改
        Date date = new Date();
        dispatchList.forEach(d -> {
            BizAssert.isFalse(DispatchStateEnum.DISPATCH.equals(d.getState()), "出库失败，不允许重复出库");
            UploadFile attachment = dispatch.getDispatchAttachment();
            if (ObjectUtils.isNotEmpty(attachment)){
                d.setDispatchAttachment(attachment);
            }
            d.setState(DispatchStateEnum.DISPATCH);
            d.setDispatchDate(date);
        });
        dispatchDao.updateBatchById(dispatchList);

        //备料任务已领料
        List<PickingItem> pickingItems = pickingService.requisitioned(pickingList);

        //保存库存明细记录
        if (!CollectionUtils.isEmpty(pickingItems)) {
            List<InventoryLog> inventoryLogs = getInventoryLogs(dispatchList, pickingItems);
            InventoryLogUtils.saveBatch(inventoryLogs);
        }

        //更新委外单明细的第一次出库时间
        Set<String> outsourcingCodes = new HashSet<>();
        for (Dispatch dispatch1 : dispatchList) {
            //不确定以后会不会加其他类型，所以还是加一个判断为好
            if (DispatchTypeEnum.OUTSOURCE.equals(dispatch1.getType())){
                outsourcingCodes.add(dispatch1.getRelationCode());
            }
        }
        List<OutsourcingOrderItem> byOrderNos = outsourcingOrderItemService.findByOrderNos(outsourcingCodes);
        List<OutsourcingOrderItem> updateList = new ArrayList<>();
        for (OutsourcingOrderItem byOrderNo : byOrderNos) {
            //存在已发料量 并且 第一次出库时间为空
            if (byOrderNo.getIssuedQuantity().compareTo(BigDecimal.ZERO) > 0 && byOrderNo.getFirstDeliveryTime() == null){
                byOrderNo.setFirstDeliveryTime(new Date());
                updateList.add(byOrderNo);
            }
        }
        outsourcingOrderItemService.updateBatchById(updateList);
        //保存操作日志
        LogUtils.saveLog(WmsConstant.RESOURCE_OUTSOURCE_DISPATCH, "确认出库", "委外发料单据确认出库：出库单号”${code}“", dispatchList);
    }

    @NotNull
    private List<InventoryLog> getInventoryLogs(List<Dispatch> dispatches, List<PickingItem> pickingItems) {
        //出库记录根据备料id分组
        Map<String, Dispatch> dispatchMap = dispatches.stream()
                .collect(Collectors.toMap(Dispatch::getPickingId, Function.identity()));
        //备料记录根据备料单id分组
        Map<String, List<PickingItem>> itemMap = pickingItems.stream()
                .collect(Collectors.groupingBy(PickingItem::getPickingId));

        //查询备料标签记录
        Map<String, List<PickingRecordModel>> pickingRecordModelMap = pickingRecordService.findListByPickingId(itemMap.keySet()).stream().collect(Collectors.groupingBy(PickingRecord::getPickingItemId));
        //取出备料记录中所有物料id
        Set<String> materialIdSet = pickingItems.stream()
                .map(PickingItem::getMaterialId)
                .collect(Collectors.toSet());
        //根据物料id查询物料信息
        Map<String, MaterialModel> materialMap = materialService.findInventoryListByIds(materialIdSet)
                .stream()
                .peek(material -> material.setInventory(material.getAvailableInventory().add(material.getLockInventory())))
                .collect(Collectors.toMap(MaterialModel::getId, Function.identity()));
        List<InventoryLog> inventoryLogs = new LinkedList<>();
        List<InventoryLogItem> inventoryLogItems = new LinkedList<>();
        itemMap.forEach((pickId, itemList) -> {
            Dispatch dispatch = dispatchMap.get(pickId);
            inventoryLogs.addAll(itemList.stream()
                    .map(item -> getInventoryLog(materialMap, dispatch, item,pickingRecordModelMap,inventoryLogItems))
                    .collect(Collectors.toList()));
        });
        inventoryLogItemService.saveBatch(inventoryLogItems);
        return inventoryLogs;
    }

    @NotNull
    private InventoryLog getInventoryLog(Map<String, MaterialModel> materialMap,
                                         Dispatch dispatch,
                                         PickingItem pickingItem,Map<String, List<PickingRecordModel>> pickingRecordModelMap,List<InventoryLogItem> inventoryLogItems) {
        MaterialModel materialModel = materialMap.get(pickingItem.getMaterialId());
        InventoryLog inventoryLog = new InventoryLog();
        inventoryLog.setId(IdWorker.getIdStr());
        inventoryLog.setBizCode(dispatch.getRelationCode());
        inventoryLog.setTaskId(dispatch.getId());
        inventoryLog.setTaskCode(dispatch.getCode());
        inventoryLog.setBizPersonId(dispatch.getConsigneeBy());
        inventoryLog.setBizPersonName(dispatch.getConsigneeName());
        
        //主仓备料
        inventoryLog.setWarehouseCode(WarehouseEnum.MAIN);
        //获取库存流水类型
        inventoryLog.setType(InventoryLogTypeConstant.OUTSOURCE_DISPATCH);
        inventoryLog.setMaterialId(materialModel.getId());
        inventoryLog.setMaterialCode(materialModel.getMaterialCode());
        inventoryLog.setMaterialName(materialModel.getName());
        inventoryLog.setMaterialProperty(materialModel.getProperty().getValue());
        inventoryLog.setMaterialSpecification(materialModel.getSpecification());
        inventoryLog.setMaterialUnit(materialModel.getUnit());
        //数量
        BigDecimal quantity = pickingItem.getPickingQuantity();
        inventoryLog.setNegate(true);
        inventoryLog.setQuantity(quantity);
        //总价
        BigDecimal total = pickingItem.getTotal();
        inventoryLog.setTotal(total);
        //单价
        BigDecimal unitPrice = pickingItem.getUnitPrice();
        inventoryLog.setUnitPrice(unitPrice);
        //物料库存
        BigDecimal inventory = materialModel.getInventory();
        //变更后库存
        inventoryLog.setAfterInventory(inventory);
        //变更前库存
        BigDecimal beforeInventory = inventory.add(quantity);
        inventoryLog.setBeforeInventory(beforeInventory);
        //更新库存，以防后面需要用到还要累加
        materialModel.setInventory(beforeInventory);

        for (PickingRecordModel pickingRecordModel : pickingRecordModelMap.get(pickingItem.getId())) {
            InventoryLogItem inventoryLogItem = new InventoryLogItem();
            inventoryLogItem.setLogId(inventoryLog.getId());
            inventoryLogItem.setLotNo(pickingRecordModel.getLotNo());
            inventoryLogItem.setQuantity(pickingRecordModel.getPickingQuantity());
            inventoryLogItems.add(inventoryLogItem);
        }
        return inventoryLog;
    }

    @Override
    public List<PickingRecordModel> findRecordsByItemId(String id, String itemId) {
        Dispatch dispatch = dispatchDao.getById(id);
        BizAssert.isNotNull(dispatch, "查询失败，发货单据不存在");

        return pickingRecordService.findListByItemId(itemId);
    }

    @Override
    public List<PickingRecordModel> findItemByRelationCode(String relationCode) {
        //根据关联单号查询委外单号
        List<Dispatch> dispatchList = this.findDispatchListByRelationCode(relationCode);
        if (!CollectionUtils.isEmpty(dispatchList)) {
            //根据发料单中的备料单id 查询 备料记录
            Set<String> pickIds = dispatchList.stream().map(Dispatch::getPickingId).collect(Collectors.toSet());
            return pickingRecordService.findMaterialRecordByPickId(pickIds);
        }
        return Collections.emptyList();
    }

    @Override
    public void cancelByPickIds(Collection<String> pickIds) {
        if (CollectionUtils.isEmpty(pickIds)){
            return;
        }

        QueryWrapper<Dispatch> wrapper = new QueryWrapper<Dispatch>()
                .eq("deleted", YesOrNOEnum.NO.getValue())
                .in("picking_id", pickIds);
        List<Dispatch> dispatchList = dispatchDao.list(wrapper);
        if (CollectionUtils.isEmpty(dispatchList)){
            return;
        }

        dispatchList.forEach(dispatch -> dispatch.setState(DispatchStateEnum.CANCEL));
        dispatchDao.updateBatchById(dispatchList);
    }

    private List<Dispatch> findDispatchListByRelationCode(String relationCode) {
        LambdaQueryWrapper<Dispatch> wrapper = new LambdaQueryWrapper<Dispatch>()
                .eq(Dispatch::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(Dispatch::getRelationCode, relationCode)
                .eq(Dispatch::getState, DispatchStateEnum.DISPATCH.getValue());
        return dispatchDao.list(wrapper);
    }
}
