package com.huayun.modules.erp.oams.entity;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.erp.oams.constant.enums.AssetCheckResultEnum;
import com.huayun.modules.erp.oams.constant.enums.AssetCheckStateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* 固定资产盘点详情表
*
* <AUTHOR> am I?
* @date   2023/09/18
*/
@Data
@NoArgsConstructor
@TableName("oams_asset_check_item")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oams_asset_check_item对象", description="固定资产盘点详情表")
public class AssetCheckItem extends BaseTenantEntity {
    
	/**盘点id*/
    @ApiModelProperty(value = "盘点id")
	private String checkId;
	/**盘点单编码*/
    @ApiModelProperty(value = "盘点单编码")
	private String checkNo;
	/**资产id*/
    @ApiModelProperty(value = "资产id")
	private String assetId;
	/**资产编码*/
    @ApiModelProperty(value = "资产编码")
	private String assetNo;
	/**资产类型id*/
    @ApiModelProperty(value = "资产类型id")
	private String assetType;
	@ApiModelProperty(value = "状态;盘点中COUNTING、待盘点UNCOUNTED、已盘点COUNTED")
	private AssetCheckStateEnum state;
	/**盘点结果;正常、盘盈、盘亏*/
    @ApiModelProperty(value = "盘点结果;正常、盘盈、盘亏")
	private AssetCheckResultEnum result;

	public AssetCheckItem(Asset asset) {
		BeanUtil.copyProperties(asset,this,"id","updateBy","createBy","createTime","updateTime","state");
		this.assetId = asset.getId();
	}

	public AssetCheckItem(Asset asset, AssetCheck assetCheck) {
		this.assetId = asset.getId();
		this.assetNo = asset.getAssetNo();
		this.checkId = assetCheck.getId();
		this.checkNo = assetCheck.getCheckNo();
		this.assetType = asset.getAssetType();
	}
}
