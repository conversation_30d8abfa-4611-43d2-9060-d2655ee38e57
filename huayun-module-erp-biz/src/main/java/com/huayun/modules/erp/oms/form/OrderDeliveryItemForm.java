package com.huayun.modules.erp.oms.form;

import com.huayun.modules.erp.oams.controller.form.BaseForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 订单发货清单
 * @date 2022/8/11 12:34
 **/
@Data
@ApiModel(value = "订单发货清单", description = "订单发货清单")
public class OrderDeliveryItemForm {
    @ApiModelProperty(value = "id")
    @NotBlank(message = "Id不能为空", groups = {BaseForm.Update.class})
    private String id;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @NotBlank(message = "订单编号不可为空")
    private String orderNo;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @NotBlank(message = "产品id不可为空")
    private String productId;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不可为空")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @NotBlank(message = "产品名称不可为空")
    private String productName;

    /**
     * 产品规格
     */
    @ApiModelProperty(value = "产品规格")
    private String productSpecification;

    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    /**
     * 产品单位
     */
    @ApiModelProperty(value = "产品单位")
    @NotBlank(message = "产品单位不可为空")
    private String productUnit;

    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量")
    @NotNull(message = "发货数量不可为空")
    @Positive(message = "发货数量必须为正数")
    private BigDecimal deliveryQuantity;

    @ApiModelProperty("是否赠品")
    private boolean isGift;

    /**物料副单位*/
    @ApiModelProperty("物料副单位")
    private String secondUnit;
    /**辅单位采购数量*/
    @ApiModelProperty("辅单位采购数量")
    private BigDecimal secondCount;
    /**单位换算率*/
    @ApiModelProperty("单位换算率")
    private BigDecimal unitConvertRate;
    
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "含税单价 来自订单 可修改")
    private BigDecimal orderTaxPrice;

    @ApiModelProperty(value = "不含税单价 来自订单 可修改")
    private BigDecimal orderPrice;

    @ApiModelProperty(value = "未税总金额 来自订单 可修改")
    private BigDecimal orderTotalAmount;

    @ApiModelProperty(value = "税额 来自订单 可修改")
    private BigDecimal orderTaxAmount;

    @ApiModelProperty(value = "含税总金额 来自订单 可修改")
    private BigDecimal orderInTaxAmount;

    @ApiModelProperty(value = "税率 来自订单 可修改")
    private BigDecimal taxRate;
}
