package com.huayun.modules.erp.wms.service.qc.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.pms.entity.Supplier;
import com.huayun.modules.erp.pms.service.ISupplierService;
import com.huayun.modules.erp.utils.ProcessUtils;
import com.huayun.modules.erp.wms.dao.IQualityControlDao;
import com.huayun.modules.erp.wms.entity.QualityControl;
import com.huayun.modules.erp.wms.entity.model.QualityControlModel;
import com.huayun.modules.erp.wms.query.QualityControlQuery;
import com.huayun.modules.erp.wms.service.qc.IQualityControlService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
* 质检记录
 *
 * <AUTHOR> am I?
 * @date 2022/09/08
 */
@Service
public class QualityControlServiceImpl implements IQualityControlService {

    @Resource
    private IQualityControlDao qualityControlDao;

    @Resource
    private ISupplierService supplierService;

    @Override
    public IPage<QualityControlModel> getQualityControlModelPage(Page<QualityControlModel> page, QualityControlQuery qualityControlQuery) {

        //查询供应商ID
        List<Supplier> suppliers = supplierService.getSuplliersByLikeCondition(qualityControlQuery.getSupplierCode(), qualityControlQuery.getSupplierName());
        if (ObjectUtil.isNotEmpty(qualityControlQuery.getSupplierCode()) || ObjectUtil.isNotEmpty(qualityControlQuery.getSupplierName())) {
            List<String> supplierIds = suppliers.stream().map(Supplier::getId).collect(Collectors.toList());
            //防止报错
            supplierIds.add("NONE");
            qualityControlQuery.setSupplierIds(supplierIds);
        }

        IPage<QualityControlModel> qualityControlModelPage = qualityControlDao.getQualityControlModelPage(page, qualityControlQuery.createPageQueryWrapper());
        List<String> supplierIds = qualityControlModelPage.getRecords().stream().map(QualityControlModel::getSupplierId).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(supplierIds)) {
            List<Supplier> supplierList = supplierService.findSupplierList(supplierIds);
            Map<String, Supplier> supplierMap = supplierList.stream().collect(Collectors.toMap(Supplier::getId, java.util.function.Function.identity()));
            qualityControlModelPage.getRecords().
                    forEach(qualityControlModel -> {
                        qualityControlModel.setSupplierName(Optional.ofNullable(supplierMap.get(qualityControlModel.getSupplierId())).map(Supplier::getName).orElse(null));
                        qualityControlModel.setSupplierCode(Optional.ofNullable(supplierMap.get(qualityControlModel.getSupplierId())).map(Supplier::getCode).orElse(null));
                    });
        }
        return qualityControlModelPage;
    }

    @Override
    public List<QualityControlModel> findListByReceiptId(String receiptId) {
        return qualityControlDao.findListByReceiptId(receiptId, false);
    }

    @Override
    public List<QualityControlModel> findReturnListByReceiptId(String receiptId) {
        return qualityControlDao.findListByReceiptId(receiptId, true);
    }

    @Override
    public List<String> findReturnRelationCodeList(String type) {
        return qualityControlDao.findReturnRelationCodeList(type);
    }

    @Override
    public List<QualityControlModel> findReturnMaterialListByRelationCode(String relationCode) {
        return qualityControlDao.findReturnMaterialListByRelationCode(relationCode);
    }

    @Override
    public QualityControlModel getByItemId(String receiptItemId) {
        return qualityControlDao.getByItemId(receiptItemId);
    }

    @Override
    public QualityControlModel getDetailById(String id) {
        return qualityControlDao.getDetailById(id);
    }

    @Override
    public List<QualityControl> list(Wrapper<QualityControl> wrapper) {
        return qualityControlDao.list(wrapper);
    }

    @Override
    public IPage<QualityControlModel> queryMaterialPassRatePage(Page<QualityControl> page, QueryWrapper<QualityControl> wrapper,String group) {
        IPage<QualityControlModel> pageList = qualityControlDao.queryMaterialPassRatePage(page, wrapper,group);
        pageList.getRecords().forEach(qc -> {
            /*合格率 =（质检数量 - 不合格数量）/ 质检数量 * 100%*/
            BigDecimal qualifiedQuantity = ProcessUtils.bigDecimalSubPositiveOrZero(qc.getQcQuantity(), qc.getUnqualifiedQuantity());
            //向下保留四位小数
            BigDecimal pass = NumberUtil.div(qualifiedQuantity, qc.getQcQuantity(), 4, RoundingMode.DOWN);
            BigDecimal passRate = NumberUtil.mul(pass, new BigDecimal("100"));
            qc.setPassRate(passRate);
        });
        return pageList;
    }

    @Override
    public void deleteByReceiptIds(Collection<String> receiptIds) {
        qualityControlDao.deleteByReceiptIds(receiptIds);
    }

    @Override
    public void deleteByReceiptItemIds(Collection<String> receiptItemIds) {
        qualityControlDao.deleteByReceiptItemIds(receiptItemIds);
    }

    @Override
    public List<QualityControlModel> selectQualityByReceiptId(List<String> receiptIds) {
        List<QualityControlModel> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(receiptIds)){
            return resultList;
        }
        return qualityControlDao.selectQualityByReceiptId(receiptIds);
    }
}
