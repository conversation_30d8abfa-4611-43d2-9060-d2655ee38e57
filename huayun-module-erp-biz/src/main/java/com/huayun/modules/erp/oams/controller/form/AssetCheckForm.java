package com.huayun.modules.erp.oams.controller.form;

import com.huayun.modules.erp.oams.constant.enums.AssetCheckResultEnum;
import com.huayun.modules.erp.oams.constant.enums.AssetCheckStateEnum;
import com.huayun.modules.erp.oams.entity.CheckAssetCategory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* TODO description
*
* <AUTHOR>
* @date 2022/7/27
*/
@Data
public class AssetCheckForm {

	/**盘点编号*/
    @ApiModelProperty(value = "盘点编号")
	private String checkNo;
	/**盘点日期*/
    @ApiModelProperty(value = "盘点日期")
	private String checkDate;
	/**盘点部门id*/
    @ApiModelProperty(value = "盘点部门id")
	private String departmentId;
	/**盘点部门名称*/
    @ApiModelProperty(value = "盘点部门名称")
	private String departmentName;
	/**资产类型列表*/
    @ApiModelProperty(value = "资产类型列表")
	private List<CheckAssetCategory> assetCategory;
	/**盘点人员id*/
    @ApiModelProperty(value = "盘点人员id")
	private String employeeId;
	/**盘点人员名称*/
    @ApiModelProperty(value = "盘点人员名称")
	private String employeeName;
	/**状态;盘点中、待盘点、已盘点*/
    @ApiModelProperty(value = "状态;盘点中、待盘点、已盘点")
	private AssetCheckStateEnum state;
	/**盘点结果;盘盈、盘亏、正常*/
    @ApiModelProperty(value = "盘点结果;盘盈、盘亏、正常")
	private AssetCheckResultEnum result;
	@ApiModelProperty(value = "备注")
	private String remark;
}
