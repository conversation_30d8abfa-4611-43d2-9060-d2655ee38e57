package com.huayun.modules.erp.oams.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huayun.modules.common.auth.context.SessionContext;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.erp.oams.controller.form.AssetCategoryForm;
import com.huayun.modules.erp.oams.controller.query.AssetCategoryQuery;
import com.huayun.modules.erp.oams.controller.vo.AssetCategoryVO;
import com.huayun.modules.erp.oams.entity.Asset;
import com.huayun.modules.erp.oams.entity.AssetCategory;
import com.huayun.modules.erp.oams.mapper.AssetCategoryMapper;
import com.huayun.modules.erp.oams.service.IAssetCategoryService;
import com.huayun.modules.erp.oams.service.IAssetService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【oams_asset_category(固定资产类型)】的数据库操作Service实现
* @createDate 2023-09-08 17:45:38
*/
@Service
public class AssetCategoryServiceImpl extends ServiceImpl<AssetCategoryMapper, AssetCategory>
    implements IAssetCategoryService {
    @Resource
    AssetCategoryMapper assetCategoryMapper;
    @Resource
    IAssetService assetService;

    @Override
    public List<AssetCategoryVO> getAssetCategoryTree(AssetCategoryQuery query) {
        AssetCategory assetCategory = this.getOne(new LambdaQueryWrapper<AssetCategory>().last("LIMIT 1"), false);
        if (ObjectUtil.isEmpty(assetCategory)){
            //进行初始化操作
            String tenant = SessionContext.getTenant();
            assetCategoryMapper.init(tenant);
        }
        List<AssetCategory> assetCategoryList = this.list();
        List<AssetCategoryVO> assetCategoryVOListList = assetCategoryList.stream().map(AssetCategoryVO::new).collect(Collectors.toList());
        //过滤出顶级
        List<AssetCategoryVO> targetList = assetCategoryVOListList.stream().filter(vo -> ObjectUtil.isEmpty(vo.getParentId())).collect(Collectors.toList());
        this.setChildrenData(targetList,assetCategoryVOListList,"","");

        return targetList;
    }

    @Override
    public List<AssetCategory> getAssetCategoryByIds(List<String> ids) {
        if (ObjectUtil.isEmpty(ids)){
            return Collections.emptyList();
        }
        return this.listByIds(ids);
    }

    /**
     * 获取分类名称类似于 一级类日/二级类日/本类目
     * @param categoryId
     * @param categoryMap
     * @return
     */
    @Override
    public String getCategoryNameWithParent(String categoryId, Map<String, AssetCategory> categoryMap) {
        AssetCategory category = categoryMap.get(categoryId);
        if (category == null ) {
            return null;
        }
        //避免死循环
        if(ObjectUtil.equal(category.getId(),category.getParentId())){
            return null;
        }

        StringBuilder categoryNameBuilder = new StringBuilder(category.getName());
        String parentId = category.getParentId();
        while (parentId != null) {
            AssetCategory parentCategory = categoryMap.get(parentId);
            if (parentCategory == null) {
                break;
            }
            categoryNameBuilder.insert(0, parentCategory.getName() + "/");
            parentId = parentCategory.getParentId();
        }

        return categoryNameBuilder.toString();
    }

    /**
     * 获取分类名称类似于 一级类日/二级类日/本类目 递归实现方式
     * @param categoryId
     * @return
     */
    private String getCategoryNameWithParentByRecursive(String categoryId) {
        AssetCategory category = this.getById(categoryId);
        if (category == null) {
            return null;
        }

        List<String> categoryNames = new ArrayList<>();
        getCategoryNamesRecursive(category, categoryNames);

        return String.join("/", categoryNames);
    }

    private void getCategoryNamesRecursive(AssetCategory category, List<String> categoryNames) {
        if (category.getParentId() != null) {
            AssetCategory parentCategory = this.getById(category.getParentId());
            if (parentCategory != null) {
                getCategoryNamesRecursive(parentCategory, categoryNames);
            }
        }
        categoryNames.add(category.getName());
    }

    /**
     * 批量获取分类名称类似于 一级类日/二级类日/本类目
     * @param
     * @return
     */
    @Override
    public Map<String, String> getCategoryNamesWithParent(List<String> categoryIds) {
        Map<String, String> result = new HashMap<>();
        if (ObjectUtil.isEmpty(categoryIds)) {
            return result;
        }
        List<AssetCategory> allCategories = this.list();
        Map<String, AssetCategory> categoryMap = allCategories.stream()
                .collect(Collectors.toMap(AssetCategory::getId, Function.identity()));
        for (String categoryId : categoryIds) {
            String categoryName = getCategoryNameWithParent(categoryId,categoryMap);
            result.put(categoryId, categoryName==null?"":categoryName);
        }

        return result;
    }


    /**
     * 递归获取属性结构
     */
    private void setChildrenData(List<AssetCategoryVO> targetList, List<AssetCategoryVO> sourceList, String parentFullName, String parentBeforeCode) {

        for (AssetCategoryVO target : targetList) {
            target.setFullName(parentFullName.isEmpty() ? target.getName() : parentFullName + "/" + target.getName());
            target.setBeforeCategoryCode(parentFullName.isEmpty() ? target.getCode() : parentBeforeCode + target.getCode());
            List<AssetCategoryVO> children = sourceList.
                    stream().
                    filter(source -> ObjectUtil.equal(source.getParentId(),target.getId())).
                    collect(Collectors.toList());
            target.setChildren(children);
            if (ObjectUtil.isNotEmpty(children)) {
                String childFullName = target.getFullName();
                String childBeforeCode = target.getBeforeCategoryCode();
                this.setChildrenData(children, sourceList, childFullName, childBeforeCode);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssetCategory createAssetCategory(AssetCategoryForm assetCategoryForm) {
        LambdaQueryWrapper<AssetCategory> categoryWr = Wrappers.lambdaQuery();
        AssetCategory one = this.getOne(categoryWr.eq(AssetCategory::getCode, assetCategoryForm.getCode()).last("LIMIT 1"));
        BizAssert.isEmpty(one,"已有相同的编码");
        AssetCategory assetCategory = new AssetCategory();
        BeanUtils.copyProperties(assetCategoryForm, assetCategory, "parent_id");
        this.save(assetCategory);
        return assetCategory;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssetCategory updateAssetCategory(String assetCategoryId, AssetCategoryForm assetCategoryForm) {
        LambdaQueryWrapper<AssetCategory> categoryWr = Wrappers.lambdaQuery();
        categoryWr.eq(AssetCategory::getCode, assetCategoryForm.getCode())
                .ne(AssetCategory::getId, assetCategoryId)
                .last("LIMIT 1");
        AssetCategory one = this.getOne(categoryWr.eq(AssetCategory::getCode, assetCategoryForm.getCode()).last("LIMIT 1"));
        BizAssert.isEmpty(one,"已有相同的编码");
        AssetCategory assetCategory = new AssetCategory();
        BeanUtils.copyProperties(assetCategoryForm, assetCategory);
        assetCategory.setId(assetCategoryId);
        this.updateById(assetCategory);
        return this.getById(assetCategoryId);
    }

    @Override
    public AssetCategory deleteAssetCategory(String assetCategoryId) {
        //查询子类
        List<AssetCategory> assetCategoryList = this.list();
        List<AssetCategory> resultList = getChildCategoryList(assetCategoryId,assetCategoryList);
        BizAssert.isEmpty(resultList,"该类别下有子项，请先删除子项！");

        //删除前判断是否由被用到
        AssetCategory assetCategory = this.getById(assetCategoryId);
        List<Asset> assetList = assetService.list(new LambdaQueryWrapper<Asset>().eq(Asset::getAssetType, assetCategoryId));
        BizAssert.isEmpty(assetList,"固定资产使用到该类别，不能删除，固定资产编号：" + assetList.stream().map(Asset::getAssetNo).collect(Collectors.joining(",")));
        //硬删除
        assetCategoryMapper.deleteById(assetCategoryId);
        return assetCategory;
    }

    /**
     * 获取所有的子类
     * @param categoryId 目标id
     * @param categoryList 列表
     * @return
     */
    private List<AssetCategory> getChildCategoryList(String categoryId, List<AssetCategory> categoryList) {
        List<AssetCategory> resultList = new ArrayList<>();
        // 使用Lambda表达式筛选出parentId等于categoryId的子类，并递归获取子类的子类
        categoryList.stream()
                .filter(category -> ObjectUtil.equal(category.getParentId(),categoryId) )
                .forEach(category -> {
                    resultList.add(category);
                    resultList.addAll(getChildCategoryList(category.getId(), categoryList));
                });
            return resultList;
    }

    @Override
    public AssetCategory getAssetCategory(String assetCategoryId) {
        return this.getById(assetCategoryId);
    }

    @Override
    public AssetCategoryVO getCategoryDetail(String id) {
        AssetCategory category = this.getById(id);
        BizAssert.isNotEmpty(category,"该类别不存在");
        AssetCategoryVO categoryVO = new AssetCategoryVO(category);
        List<String> fullNameAndCode = getFullNameAndCode(category);
        String fullName = fullNameAndCode.get(0);
        String beforeCategoryCode = fullNameAndCode.get(1);

        categoryVO.setFullName(fullName);
        categoryVO.setBeforeCategoryCode(beforeCategoryCode);

        return categoryVO;
    }

    private List<String> getFullNameAndCode(AssetCategory category) {
        if (ObjectUtil.isEmpty(category) || ObjectUtil.isEmpty(category.getParentId()) || ObjectUtil.equal(category.getParentId(),category.getId())) {
            return Arrays.asList(category.getName(), category.getCode());
        }

        AssetCategory parentCategory = this.getById(category.getParentId());
        List<String> parentFullNameAndCode = getFullNameAndCode(parentCategory);
        String parentFullName = parentFullNameAndCode.get(0);
        String parentCategoryCode = parentFullNameAndCode.get(1);

        String fullName = parentFullName + "/" + category.getName();
        String beforeCategoryCode = parentCategoryCode + "/" + category.getCode();

        return Arrays.asList(fullName, beforeCategoryCode);
    }
}




