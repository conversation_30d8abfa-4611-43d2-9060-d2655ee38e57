package com.huayun.modules.erp.wms.service.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.wms.entity.Dispatch;
import com.huayun.modules.erp.wms.entity.model.DispatchModel;
import com.huayun.modules.erp.wms.entity.model.PickingRecordModel;
import com.huayun.modules.erp.wms.form.DispatchForm;
import com.huayun.modules.erp.wms.query.DispatchQuery;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 发货单据管理
 *
 * <AUTHOR>
 */
public interface IDispatchService {

    /**
     * 分页查询
     *
     * @param page
     * @param query
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.huayun.modules.erp.wms.entity.Dispatch>
     * <AUTHOR>
     * @date 2023/1/5 13:40
     */
    IPage<DispatchModel> queryPageList(Page<Dispatch> page, DispatchQuery query);

    /**
     * 根据备料任务id查询待出库的委外发料单
     *
     * <AUTHOR>
     * @date 2023/1/12 15:54
     * @param pickingIds
     * @return java.util.List<com.huayun.modules.erp.wms.entity.model.DispatchModel>
     */
    List<Dispatch> findListByPickingIds(Collection<String> pickingIds);

    /**
     * 根据备料任务id查询待出库的委外发料单
     *
     * <AUTHOR>
     * @date 2023/1/12 15:54
     * @param pickingId
     * @return java.util.List<com.huayun.modules.erp.wms.entity.model.DispatchModel>
     */
    Dispatch findListByPickingId(String pickingId);

    /**
     * 列表查询
     *
     * <AUTHOR>
     * @date 2023/1/12 15:54
     * @param query
     * @return java.util.List<com.huayun.modules.erp.wms.entity.model.DispatchModel>
     */
    List<DispatchModel> findList(DispatchQuery query);

    /**
     * 查询基本信息
     *
     * <AUTHOR>
     * @date 2023/1/5 13:45
     * @param id
     * @return com.huayun.modules.erp.wms.entity.Dispatch
     */
    Dispatch getById(String id);

    /**
     * 查询详情信息（包含备料物料信息）
     *
     * <AUTHOR>
     * @date 2023/1/5 14:15
     * @param id
     * @return com.huayun.modules.erp.wms.entity.model.DispatchModel
     */
    DispatchModel getDispatchById(String id);

    /**
     * 保存发货单
     *
     * @param form
     * @return com.huayun.modules.erp.wms.entity.Dispatch
     * <AUTHOR>
     * @date 2023/1/5 15:46
     */
    List<Dispatch> saveDispatch(List<DispatchForm> form);

    /**
     * 完成入库
     *
     * <AUTHOR>
     * @date 2023/1/5 13:59
     * @param dispatch
     * @return void
     */
    void dispatchComplete(Dispatch dispatch);

    /**
     * 查询委外发料物料备料记录
     *
     * <AUTHOR>
     * @date 2023/4/20 17:36
     * @param id
     * @param itemId
     * @return java.util.List<com.huayun.modules.erp.wms.entity.model.PickingRecordModel>
     */
    List<PickingRecordModel> findRecordsByItemId(String id, String itemId);

    /**
     * 根据委外订单查询发料物料清单
     *
     * <AUTHOR>
     * @date 2023/6/15 20:52
     * @param relationCode
     * @return java.util.List<com.huayun.modules.erp.wms.entity.model.PickingRecordModel>
     */
    List<PickingRecordModel> findItemByRelationCode(String relationCode);

    /**
     * 根据备料单id取消委外发料单
     *
     * <AUTHOR>
     * @date 2023/10/18 16:02
     * @param pickIds
     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    void cancelByPickIds(Collection<String> pickIds);
}
