package com.huayun.modules.erp.mms.service.model.salary;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/08/08/14:43
 */
@Data
public class ExportSalaryModel {

    @Excel(name = "员工名称")
    private String name;

    @ExcelCollection(name = "应付工资")
    private List<ExcelExportEntity> wagesPayable;
}
