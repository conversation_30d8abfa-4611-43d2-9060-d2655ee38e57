package com.huayun.modules.erp.wms.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 调整物料列表
 *
 * <AUTHOR>
 * @date 2022/11/7 17:32
 **/
@Data
public class InventoryAdjustmentItemForm {

    /**物料标签号*/
    @ApiModelProperty(value = "物料标签号")
    @NotBlank(message = "物料标签号不可为空")
    private String lotNo;

    /**调整数量*/
    @ApiModelProperty(value = "调整数量")
    @NotNull(message = "调整数量不可为空")
    private BigDecimal adjustmentQuantity;
}
