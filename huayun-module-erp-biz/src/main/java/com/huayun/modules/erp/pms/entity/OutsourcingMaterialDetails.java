package com.huayun.modules.erp.pms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.entity.BaseTenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
* 委外物料明细
*
* <AUTHOR> am I?
* @date   2023/01/03
*/
@Data
@TableName("pms_outsourcing_material_details")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="pms_outsourcing_material_details对象", description="委外物料明细")
public class OutsourcingMaterialDetails extends BaseTenantEntity {
	/**计划编号*/
	@ApiModelProperty(value = "计划编号")
	private String materialPlanCode;
	/**委外物料编码*/
    @ApiModelProperty(value = "委外物料编码")
	private String materialCode;
	/**委外物料编码*/
	@ApiModelProperty(value = "委外物料名称")
	private String materialName;
	/**委外物料规格*/
	@ApiModelProperty(value = "委外物料规格")
	private String materialSpecifications;
	/**委外物料规格*/
	@ApiModelProperty(value = "委外物料单位")
	private String materialUnit;
	/**委外物料规格*/
	@ApiModelProperty(value = "委外物料损耗率")
	private BigDecimal materialLossRate;
	/**物料用量*/
	@ApiModelProperty(value = "物料用量")
	private  java.math.BigDecimal  materialUsage;
	/**需求量*/
	@ApiModelProperty(value = "需求量")
	private  String materialRequirement;
	/**委外已发料量*/
    @ApiModelProperty(value = "委外已发料量")
	private java.math.BigDecimal issuedQuantity;
	/**委外本次发货量*/
    @ApiModelProperty(value = "委外本次发货量")
	private java.math.BigDecimal currentIssuedQuantity;

	@ApiModelProperty(value = "可用库存")
	private  BigDecimal	materialAvailableInventory;
}
