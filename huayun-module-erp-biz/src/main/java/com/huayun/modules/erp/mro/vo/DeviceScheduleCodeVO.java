package com.huayun.modules.erp.mro.vo;

import com.huayun.modules.erp.mro.entity.DeviceSchedule;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DeviceScheduleCodeVO {


    public DeviceScheduleCodeVO(DeviceSchedule deviceSchedule) {
        init(deviceSchedule);
    }

    private void init(DeviceSchedule deviceSchedule) {
        BeanUtils.copyProperties(deviceSchedule, this);
    }
}
