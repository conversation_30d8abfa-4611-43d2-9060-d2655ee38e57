package com.huayun.modules.erp.mms.service;

import com.huayun.modules.erp.mms.entity.MmsMaterialRequisitionItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huayun.modules.erp.mms.entity.model.RequisitionItemModel;
import com.huayun.modules.erp.mms.entity.model.RequisitionRecordModel;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 领料料物料清单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
public interface IMmsMaterialRequisitionItemService extends IService<MmsMaterialRequisitionItem> {

    List<RequisitionItemModel> findListByRequisitionId(Collection<String> requisitionIds);
}
