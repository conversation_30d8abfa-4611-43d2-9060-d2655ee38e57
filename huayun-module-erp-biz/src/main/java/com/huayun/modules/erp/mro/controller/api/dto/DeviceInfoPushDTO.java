package com.huayun.modules.erp.mro.controller.api.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.huayun.modules.erp.mro.entity.component.DeviceDataSubscription;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeviceInfoPushDTO implements Serializable {
    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型")
    private String msgType;
    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码")
    private String devCode;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String devType;
    /**
     * 设备型号
     */
    @ApiModelProperty(value = "设备型号")
    private String devModel;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String devName;
    /**
     * 设备生产厂家
     */
    @ApiModelProperty(value = "设备生产厂家")
    private String devManufacturer;
    /**
     * 设备序号
     */
    @ApiModelProperty(value = "设备序号")
    private String devSequenceNum;

    @ApiModelProperty(value = "设备订阅")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<DeviceDataSubscription> devSubscriptionMetrics;
}
