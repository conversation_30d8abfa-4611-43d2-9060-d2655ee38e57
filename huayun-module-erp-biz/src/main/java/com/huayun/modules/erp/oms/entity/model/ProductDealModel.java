package com.huayun.modules.erp.oms.entity.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.constant.Pattern;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProductDealModel {

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 下单日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = Pattern.DATETIME)
    @DateTimeFormat(pattern = Pattern.DATETIME)
    private Date orderTime;

    /**
     * 订单数量
     */
    private BigDecimal quantity;
    /**
     * 本位币单价
     */
    private BigDecimal localPrice;

    /**
     * 总价
     */
    private BigDecimal localAmount;
}
