package com.huayun.modules.erp.wms.vo;

import com.huayun.modules.common.util.OssUtils;
import com.huayun.modules.erp.wms.constant.enums.receipt.QualityResultEnum;
import com.huayun.modules.erp.wms.entity.model.QualityControlModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

/**
 * 采购退货物料
 *
 * <AUTHOR>
 * @date 2022/10/26 16:26
 **/
@Data
@NoArgsConstructor
public class ReturnQualityControlVO {

    /**id*/
    @ApiModelProperty(value = "id")
    private String id;

    /**关联单号*/
    @ApiModelProperty(value = "关联单号")
    private String relationCode;

    /**采购入库物料id*/
    @ApiModelProperty(value = "采购入库物料id")
    private String receiptItemId;

    /**不合格数量*/
    @ApiModelProperty(value = "不合格数量")
    private BigDecimal unqualifiedQuantity;

    /**质检结果*/
    @ApiModelProperty(value = "质检结果")
    private QualityResultEnum qualityResult;

    /**物料id*/
    @ApiModelProperty(value = "物料id")
    private String materialId;

    /**物料编码*/
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**物料主图url*/
    @ApiModelProperty(value = "物料主图url")
    private String masterDrawingUrl;

    /**物料名称*/
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**物料单位*/
    @ApiModelProperty(value = "物料单位")
    private String unit;

    public ReturnQualityControlVO(QualityControlModel qualityControlModel) {
        init(qualityControlModel);
    }

    private void init(QualityControlModel qualityControlModel) {
        BeanUtils.copyProperties(qualityControlModel, this);
        if (StringUtils.isNotEmpty(qualityControlModel.getMasterDrawingUrl())) {
            //物料主图拼接域名
            this.setMasterDrawingUrl(OssUtils.getUrl(qualityControlModel.getMasterDrawingUrl()));
        }
    }
}
