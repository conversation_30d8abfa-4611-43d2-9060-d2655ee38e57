package com.huayun.modules.erp.auth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseEntity;
import com.huayun.modules.pasm.bean.IMenu;
import com.huayun.modules.pasm.bean.IPermission;
import com.huayun.modules.pasm.bean.IPrivilege;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/26
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@TableName("sys_application_permission")
@ApiModel(value = "sys_application_permission对象", description = "应用管理")
public class Permission extends BaseEntity implements IPermission, IMenu, IPrivilege {
    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String parentId;
    /**
     * 客户端
     */
    @ApiModelProperty(value = "客户端")
    private Client client;
    /**
     * 菜单标题
     */
    @ApiModelProperty(value = "菜单标题")
    private String name;
    /**
     * 路径
     */
    @ApiModelProperty(value = "路径")
    private String url;
    /**
     * 组件
     */
    @ApiModelProperty(value = "组件")
    private String component;
    /**
     * 组件名字
     */
    @ApiModelProperty(value = "组件名字")
    private String componentName;
    /**
     * 一级菜单跳转地址
     */
    @ApiModelProperty(value = "一级菜单跳转地址")
    private String redirect;
    /**
     * 菜单类型(0:一级菜单; 1:子菜单:2:按钮权限)
     */
    @ApiModelProperty(value = "菜单类型(0:一级菜单; 1:子菜单:2:按钮权限)")
    private Type type;
    /**
     * 菜单权限编码
     */
    @ApiModelProperty(value = "菜单权限编码")
    private String perms;
    /**
     * 菜单排序
     */
    @ApiModelProperty(value = "排序")
    private Double sortNo;
    /**
     * 聚合子路由: 1:是 0:否
     */
    @ApiModelProperty(value = "聚合子路由: 1是0否")
    private Boolean alwaysShow;
    /**
     * 菜单图标
     */
    @ApiModelProperty(value = "菜单图标")
    private String icon;
    /**
     * 是否路由菜单: 0:不是  1:是（默认值1）
     */
    @ApiModelProperty(value = "是否路由菜单: 0:不是  1:是（默认值1）")
    private Boolean route;
    /**
     * 是否缓存该页面:    1:是   0:不是
     */
    @ApiModelProperty(value = "是否缓存该页面:    1:是   0:不是")
    private Boolean keepAlive;
    /**
     * 是否隐藏路由: 0否,1是
     */
    @ApiModelProperty(value = "是否隐藏路由: 0否,1是")
    private Boolean hidden;
    /**
     * 是否为首页: 0否,1是
     */
    @ApiModelProperty(value = "是否为首页: 0否,1是")
    private Boolean homePage;
    /**
     * 是否隐藏tab: 0否,1是
     */
    @ApiModelProperty(value = "是否隐藏tab: 0否,1是")
    private Boolean hideTab;
    /**
     * 外链菜单打开方式 0/内部打开 1/外部打开
     */
    @ApiModelProperty(value = "外链菜单打开方式 0/内部打开 1/外部打开")
    private Boolean internalOrExternal;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 帮助页URL
     */
    @ApiModelProperty(value = "帮助页URL")
    @TableField(exist = false)
    private String helpLink;

    public Permission(Object object) {
        BeanUtils.copyProperties(object, this);
    }

}
