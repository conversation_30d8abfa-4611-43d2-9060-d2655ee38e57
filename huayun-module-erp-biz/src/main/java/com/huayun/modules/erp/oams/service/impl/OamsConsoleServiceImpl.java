package com.huayun.modules.erp.oams.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.common.approval.controller.vo.ApprovalVO;
import com.huayun.modules.common.approval.dao.ApprovalDAO;
import com.huayun.modules.common.approval.entity.Approval;
import com.huayun.modules.erp.oams.constant.enums.CollectionStateEnum;
import com.huayun.modules.erp.oams.constant.enums.OrderTypeEnum;
import com.huayun.modules.erp.oams.constant.enums.PaymentStateEnum;
import com.huayun.modules.erp.oams.controller.query.ExpenseQuery;
import com.huayun.modules.erp.oams.controller.query.VoucherQuery;
import com.huayun.modules.erp.oams.controller.vo.CollectionStatics;
import com.huayun.modules.erp.oams.controller.vo.Inventory;
import com.huayun.modules.erp.oams.controller.vo.OamsConsoleVO;
import com.huayun.modules.erp.oams.controller.vo.OamsConsoleVO.Amoeba;
import com.huayun.modules.erp.oams.controller.vo.OamsConsoleVO.NeedDeal;
import com.huayun.modules.erp.oams.controller.vo.OamsConsoleVO.ProduceAmoebas;
import com.huayun.modules.erp.oams.controller.vo.OamsConsoleVO.TopProfit;
import com.huayun.modules.erp.oams.controller.vo.OamsHomeVO;
import com.huayun.modules.erp.oams.controller.vo.OamsHomeVO.BillingPeriod;
import com.huayun.modules.erp.oams.controller.vo.OamsHomeVO.BusinessAnaly;
import com.huayun.modules.erp.oams.controller.vo.OamsHomeVO.CashAnaly;
import com.huayun.modules.erp.oams.controller.vo.OamsHomeVO.CostAnaly;
import com.huayun.modules.erp.oams.controller.vo.OamsHomeVO.FundingMetrics;
import com.huayun.modules.erp.oams.controller.vo.OamsHomeVO.FundingTran;
import com.huayun.modules.erp.oams.controller.vo.PayMentStatics;
import com.huayun.modules.erp.oams.dao.CollectionDAO;
import com.huayun.modules.erp.oams.dao.PaymentDAO;
import com.huayun.modules.erp.oams.entity.AccountColse;
import com.huayun.modules.erp.oams.entity.Collection;
import com.huayun.modules.erp.oams.entity.LedgerAccount;
import com.huayun.modules.erp.oams.entity.LedgerAccountInit;
import com.huayun.modules.erp.oams.entity.LedgerAccountInitMonth;
import com.huayun.modules.erp.oams.entity.OrderAccounting;
import com.huayun.modules.erp.oams.entity.Payment;
import com.huayun.modules.erp.oams.entity.ProductionMonth;
import com.huayun.modules.erp.oams.entity.Voucher;
import com.huayun.modules.erp.oams.mapper.CollectionMapper;
import com.huayun.modules.erp.oams.mapper.LedgerAccountMapper;
import com.huayun.modules.erp.oams.mapper.PaymentMapper;
import com.huayun.modules.erp.oams.mapper.ProductionMonthMapper;
import com.huayun.modules.erp.oams.mapper.VoucherMapper;
import com.huayun.modules.erp.oams.service.IAccountColseService;
import com.huayun.modules.erp.oams.service.IExpenseService;
import com.huayun.modules.erp.oams.service.ILedgerAccountInitMonthService;
import com.huayun.modules.erp.oams.service.ILedgerAccountInitService;
import com.huayun.modules.erp.oams.service.IOamsConsoleService;
import com.huayun.modules.erp.oams.service.IOrderAccountingService;
import com.huayun.modules.erp.oams.service.IVoucherReportService;
import com.huayun.modules.erp.oams.service.IVoucherService;
import com.huayun.modules.erp.oams.vo.BalanceSheetReportVO;
import com.huayun.modules.erp.oams.vo.OamsProfitReportVO;
import com.huayun.modules.erp.oams.vo.VoucherSumVO;
import com.huayun.modules.erp.wms.mapper.MaterialMapper;
import com.huayun.modules.mes.api.ProductionOrderApi;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

/**
 * ClassName: OamsConsoleServiceImpl
 * Function: 获取财务控制台数据.
 * date: 2023年7月7日 下午1:57:22
 *
 * <AUTHOR>
 * @version
 * @since version 1.0
 */
@Service
public class OamsConsoleServiceImpl implements IOamsConsoleService {
    @Resource
    private CollectionDAO collectionDAO;

    @Resource
    private PaymentDAO paymentDAO;

    @Resource
    private CollectionMapper collectionMapper;

    @Resource
    private PaymentMapper paymentMapper;

    @Resource
    private MaterialMapper materialMapper;

    @Resource
    private ProductionOrderApi productionOrderApi;

    @Resource
    private ApprovalDAO approvalDAO;

    @Resource
    private ProductionMonthMapper productionMonthMapper;

    @Resource
    private IOrderAccountingService orderAccountingService;

    @Resource
    private IExpenseService expenseService;

    @Resource
    private IVoucherService voucherService;

    @Resource
    private IAccountColseService accountColseService;

    @Resource
    private VoucherMapper voucherMapper;

    @Resource
    private LedgerAccountMapper ledgerAccountMapper;

    @Resource
    private ILedgerAccountInitMonthService ledgerAccountInitMonthService;

    @Resource
    private ILedgerAccountInitService ledgerAccountInitService;

    @Resource
    private IVoucherReportService voucherReportService;
	/**
	 * TODO 生成财务控制台数据.
	 * @see com.huayun.modules.erp.oams.service.IOamsConsoleService#getOamsConsole()
	 */
	@Override
	public OamsConsoleVO getOamsConsole() {
		OamsConsoleVO res = new OamsConsoleVO();
		this.getShortMenu(res);
		this.getCollectionStatics(res);
		this.getInventory(res);
		List<NeedDeal> needs = this.getNeedDeal();
		res.setNeedDeals(needs);
		this.getPayMentStatics(res);
		this.getTop10(res);
		this.setProduceAmoebas(res);
		return res;
	}

	/**
	 * setProduceAmoebas:制造阿米巴数据.
	 *
	 * <AUTHOR>
	 * @param res
	 * @since version 1.0
	 */
	private void setProduceAmoebas(OamsConsoleVO res) {
		//查询车间的 type=2 查询时间为一年内
		String start = DateUtil.format(DateUtils.addYears(new Date(), -1), "yyyy-MM") ;
		String end = DateUtil.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM") ;

		QueryWrapper<ProductionMonth> query = new QueryWrapper<>();
		query.between("month", start, end);
		query.eq("type", "2");
		List<ProductionMonth> amoebas = productionMonthMapper.selectList(query);
		List<Amoeba> produceAmoebas = new ArrayList<>();
		for(ProductionMonth amoeba : amoebas) {
			Amoeba produceAmoeba = new Amoeba();
			BeanUtils.copyProperties(amoeba, produceAmoeba);
			produceAmoeba.setHourProfit(amoeba.getProfitHour());
			produceAmoebas.add(produceAmoeba);
		}
		ProduceAmoebas produceAmoeba =  new ProduceAmoebas();
		produceAmoeba.setProduceAmoebas(produceAmoebas);
		List<String> months = new ArrayList<>();
		Date startDate = DateUtils.addYears(new Date(), -1);
		Date current = startDate;
		for(;;) {
			if(current.compareTo(new Date())!=-1 ) {

				break;
			}
			current = DateUtils.addMonths(current, 1);
			months.add(DateUtil.format(current, "yyyy-MM"));
		}

		produceAmoeba.setProduceAmoebaMonths(months);
		res.setProduceAmoebas(produceAmoeba);
	}

	/**
	 * getShortMenu: 快捷菜单.
	 * 收款单:统计截止到当前时间所有客户待收款状态的收款单合计金额.
	 * 付款单:统计截止到当前时间所有采购和委外待付款状态的请款单合计金额.
	 * 费用单: 统计截止到当前时间所有费用待付款状态的请款单合计金额.
	 *
	 * <AUTHOR>
	 * @param res
	 * @since version 1.0
	 */
	private void getShortMenu(OamsConsoleVO res) {
		QueryWrapper<Collection> collectQuery = new QueryWrapper<>();
		collectQuery.eq("state", CollectionStateEnum.PENDING.getValue());
		collectQuery.select("sum(total_amount*exchange_rate) as total_amount");
		Collection collection = collectionDAO.getOne(collectQuery);

		QueryWrapper<Payment> paymentQuery = new QueryWrapper<>();
		paymentQuery.eq("state", PaymentStateEnum.PENDING.getValue());
		paymentQuery.select("sum((total_amount - discount_amount)*exchange_rate) as total_amount");
		Payment paymentAll = paymentDAO.getOne(paymentQuery);

		paymentQuery.in("order_type", OrderTypeEnum.OUTSOURCING, OrderTypeEnum.PURCHASE);
		Payment payment = paymentDAO.getOne(paymentQuery);

		OamsConsoleVO.Menu menu = new OamsConsoleVO.Menu();
		if(collection!=null) {
			menu.setCollection(collection.getTotalAmount());
		}else {
			menu.setCollection(BigDecimal.ZERO);
		}
		if(payment!=null) {
			menu.setPayMent(payment.getTotalAmount());
		}else {
			menu.setPayMent(BigDecimal.ZERO);
		}
		if(paymentAll!=null) {
			menu.setBill(paymentAll.getTotalAmount());
		}else {
			menu.setBill(BigDecimal.ZERO);
		}

		res.setShortMenu(menu);
	}

	/**
	 * getCollectionStatics:应收款统计.
	 按月统计近1年各月份应收款总金额 取各月份收款单应收金额合计数据
	 注:已驳回的收款单不参与统计 REJECTED
	 注:统计到本月累计数据
	 *
	 * <AUTHOR>
	 * @since version 1.0
	 */
	private void getCollectionStatics(OamsConsoleVO res) {
		Date queryDate = DateUtils.addMonths(new Date(), -11);
		List<CollectionStatics> collections = collectionMapper.sumMonthly(queryDate);
		Map<String, CollectionStatics> map = new HashMap<>();
		for(CollectionStatics collection : collections) {
			map.put(collection.getMonth(), collection);
		}
		Date startDate = DateUtils.addYears(new Date(), -1);
		Date current = startDate;
		for(;;) {
			if(current.compareTo(new Date())!=-1 ) {

				break;
			}
			current = DateUtils.addMonths(current, 1);
			String month = DateUtil.format(current, "yyyy-MM");
			if(!map.containsKey(month)) {
				CollectionStatics collect = new CollectionStatics();
				collect.setMonth(month);
				collections.add(collect);
			}
		}
		Collections.sort(collections, new Comparator<CollectionStatics>() {

			@Override
			public int compare(CollectionStatics o1, CollectionStatics o2) {

				return o1.getMonth().compareTo(o2.getMonth());
			}
		});
		res.setCollectionStatics(collections);
	}

	/**
	 * getPayMentStatics:应付款统计.
		按月统计近1年财务各月份应付款总金额(取各月份请款单应付金额合计数据，包括采购应付、委外应付、其他应付款注:已驳回的请款单不参与统计
		注:统计到本月累计数据
	 *
	 * <AUTHOR>
	 * @since version 1.0
	 */
    private void getPayMentStatics(OamsConsoleVO res) {
    	Date queryDate = DateUtils.addMonths(new Date(), -11);
    	List<PayMentStatics> payments = paymentMapper.sumMonthly(queryDate);
    	Map<String, PayMentStatics> map = new HashMap<>();
		for(PayMentStatics collection : payments) {
			map.put(collection.getMonth(), collection);
		}
		Date startDate = DateUtils.addYears(new Date(), -1);
		Date current = startDate;
		for(;;) {
			if(current.compareTo(new Date())!=-1 ) {

				break;
			}
			current = DateUtils.addMonths(current, 1);
			String month = DateUtil.format(current, "yyyy-MM");
			if(!map.containsKey(month)) {
				PayMentStatics collect = new PayMentStatics();
				collect.setMonth(month);
				payments.add(collect);
			}
		}
		Collections.sort(payments, new Comparator<PayMentStatics>() {

			@Override
			public int compare(PayMentStatics o1, PayMentStatics o2) {

				return o1.getMonth().compareTo(o2.getMonth());
			}
		});
    	res.setPayMentStatics(payments);
	}

    /**
     * getInventory:存货占比.
		数据来源存货核算
		统计截止到当前截止时间(实时数据)的存货金额占比
     *
     * <AUTHOR>
     * @since version 1.0
     */
    private void getInventory(OamsConsoleVO res) {
    	List<Inventory> proportions = materialMapper.sumProportionBill();
    	res.setInventorys(proportions);
	}

    /**
     * getNeedDeal:待办.
		待办时间取最早提示时间
		待审批收款单: 取收款审核页面审核状态为待审核/待批准的收款单合计数量待审批请款单:
		取请款审核页面审核状态为待审核/待批准的请款单合计数量包括采购请款单、委外请款单、其他请款单)鼠标点击跳转到对应页面
     * <AUTHOR>
     * @since version 1.0
     */
    private List<NeedDeal> getNeedDeal() {
    	List<NeedDeal> needs = new ArrayList<>();

    	QueryWrapper<Approval> collectQuery = new QueryWrapper<>();
    	collectQuery.eq("resource_type", "collection");
    	collectQuery.eq("state", "PENDING");
    	//long collect = approvalDAO.count(collectQuery);
    	collectQuery.orderByAsc("create_time");
    	Page<Approval> collectPage = approvalDAO.page(Page.of(1, 1), collectQuery);
    	if(collectPage.getTotal()>0) {
    		NeedDeal needCollect = new NeedDeal();
    		needCollect.setNums(collectPage.getTotal());
    		needCollect.setTime(collectPage.getRecords().get(0).getCreateTime());
    		needCollect.setType("待审批收款单");
    		needs.add(needCollect);
    	}else {
    		NeedDeal needCollect = new NeedDeal();
    		needCollect.setNums(collectPage.getTotal());
    		needCollect.setTime(new Date());
    		needCollect.setType("待审批收款单");
    		needs.add(needCollect);
    	}


    	QueryWrapper<Approval> paymentQuery = new QueryWrapper<>();
    	paymentQuery.eq("resource_type", "payment");
    	paymentQuery.eq("state", "PENDING");
    	paymentQuery.orderByAsc("create_time");
    	Page<Approval> paymentPage = approvalDAO.page(Page.of(1, 1), paymentQuery);

    	ExpenseQuery expenseQuery = new ExpenseQuery();
    	expenseQuery.setPageSize(1);
    	expenseQuery.setPage(1);
    	expenseQuery.setSelectAll(true);
    	expenseQuery.setState("PENDING_APPROVE");
    	///oams/expenses/approveList?_t=1703754270&page=1&pageSize=10&selectAll=true&state=PENDING_APPROVE
    	IPage<ApprovalVO> page = expenseService.getApprovalPage(expenseQuery, Page.of(expenseQuery.getPage(), expenseQuery.getPageSize())).convert(ApprovalVO::new);
    	if(paymentPage.getTotal()+page.getTotal()>0) {
    		NeedDeal needPayment = new NeedDeal();
    		needPayment.setNums(paymentPage.getTotal()+page.getTotal());
    		if(paymentPage.getTotal()>0) {
    			needPayment.setTime(paymentPage.getRecords().get(0).getCreateTime());
    		} else if(page.getTotal()>0) {
    			needPayment.setTime(page.getRecords().get(0).getCreateTime());
    		}

    		needPayment.setType("待审批请款单");
    		needs.add(needPayment);
    	}else {
    		NeedDeal needPayment = new NeedDeal();
    		needPayment.setNums(paymentPage.getTotal());
    		needPayment.setTime(new Date());
    		needPayment.setType("待审批请款单");
    		needs.add(needPayment);
    	}
    	return needs;
		//res.setNeedDeals(needs);
	}

    /**
     * getTop10:订单利润排行榜TOP10.
		展示上个月数据
		按订单核算利润前TOP10降序展示 (统计上个月数据取订单核算页面订单利润排名前10的数据
     *
     * <AUTHOR>
     * @since version 1.0
     */
    private void getTop10(OamsConsoleVO res) {
    	DateTime startTime = DateUtil.beginOfMonth(DateUtils.addMonths(new Date(), -1) );
    	DateTime endTime = DateUtil.endOfMonth(DateUtils.addMonths(new Date(), -1) );
    	QueryWrapper<OrderAccounting> orderQuery = new QueryWrapper<>();
    	orderQuery.between("start_date", startTime, endTime);
    	orderQuery.orderByDesc("profit", "profit_hour");
    	orderQuery.last("limit 10");
    	List<OrderAccounting> topProfits = orderAccountingService.list(orderQuery);
    	List<TopProfit> top10 = new ArrayList<>();
    	int index = 1;
    	for(OrderAccounting order : topProfits) {
    		TopProfit profit = new TopProfit();
    		profit.setProfit(order.getProfit());
    		profit.setHourProfit(order.getProfitHour()!=null?order.getProfitHour():BigDecimal.ZERO);
    		profit.setOrderNo(order.getOrderNo());
    		profit.setIndex(index);
    		index = index+1;
    		top10.add(profit);
    	}
		res.setTop10(top10);
	}

    /**
	 * TODO 生成财务控制台数据.
	 * @see com.huayun.modules.erp.oams.service.IOamsConsoleService#getOamsConsole()
	 */
	@Override
	public OamsHomeVO getOamsHome() {
		OamsHomeVO res = new OamsHomeVO();
		BillingPeriod billingPeriod = getBillingPeriod();
		String monthStr = DateUtil.format(new Date(), "yyyy-MM");
		String monthStrLast = DateUtil.format(DateUtils.addMonths(new Date(), -1), "yyyy-MM");
		//利润表
		Map<String, OamsProfitReportVO> profits = voucherReportService.getProfitReport(monthStr).stream().collect(Collectors.toMap(
				OamsProfitReportVO::getCode,
				OamsProfitReportVO -> OamsProfitReportVO));
		//资产负债表
		VoucherQuery bsQuery = new VoucherQuery();
		bsQuery.setMonthStrat(monthStr);
        Map<String, BalanceSheetReportVO> balanceSheets = voucherReportService.getBalanceSheetReport(bsQuery).stream().collect(Collectors.toMap(
        		BalanceSheetReportVO::getCodeAss,
        		BalanceSheetReportVO -> BalanceSheetReportVO));

        //利润表
		Map<String, OamsProfitReportVO> profitsLast = voucherReportService.getProfitReport(monthStrLast).stream()
				.collect(Collectors.toMap(OamsProfitReportVO::getCode, OamsProfitReportVO -> OamsProfitReportVO));
		// 资产负债表
		VoucherQuery bsQueryLast = new VoucherQuery();
		bsQueryLast.setMonthStrat(monthStrLast);
		Map<String, BalanceSheetReportVO> balanceSheetsLast = voucherReportService.getBalanceSheetReport(bsQueryLast)
				.stream().collect(Collectors.toMap(BalanceSheetReportVO::getCodeAss,
						BalanceSheetReportVO -> BalanceSheetReportVO));

        QueryWrapper<LedgerAccountInitMonth> initMonthQuery = new QueryWrapper<>();
		int year = DateUtil.year(new Date());
		int month = DateUtil.month(new Date())+1;
		initMonthQuery.eq("year", year);
		initMonthQuery.eq("month", month);
		initMonthQuery.in("code", "1001", "1002", "1122", "2202", "1601", "1602");
		Map<String, LedgerAccountInitMonth> initMonths = ledgerAccountInitMonthService.list(initMonthQuery).stream().collect(Collectors.toMap(
				LedgerAccountInitMonth::getCode,
				LedgerAccountInitMonth -> LedgerAccountInitMonth));

		QueryWrapper<LedgerAccountInitMonth> initMonthQueryLast = new QueryWrapper<>();
		int yearLast = DateUtil.year(DateUtils.addMonths(new Date(), -1));
		int monthLast = DateUtil.month(DateUtils.addMonths(new Date(), -1))+1;
		initMonthQueryLast.eq("year", yearLast);
		initMonthQueryLast.eq("month", monthLast);
		initMonthQueryLast.in("code", "1001", "1002", "1122", "2202", "1601", "1602");
		Map<String, LedgerAccountInitMonth> initMonthsLast = ledgerAccountInitMonthService.list(initMonthQuery).stream().collect(Collectors.toMap(
				LedgerAccountInitMonth::getCode,
				LedgerAccountInitMonth -> LedgerAccountInitMonth));

		Map<String, LedgerAccountInit> inits = new HashMap<>();
		QueryWrapper<LedgerAccountInit> queryInit = new QueryWrapper<>();
		// 期初有多币别，这里取本位币求和
		queryInit.select("code", "sum(local_opening) as opening", "sum(local_borrower) as borrower", "sum(local_lender) as lender", "sum(local_opening_year) as opening_year");
		queryInit.in("code", "1001", "1002", "1122", "2202", "1601", "1602");
		queryInit.groupBy("code");
		inits = ledgerAccountInitService.list(queryInit).stream().collect(Collectors.toMap(
				LedgerAccountInit::getCode,
				LedgerAccountInit -> LedgerAccountInit));

        VoucherQuery query = new VoucherQuery();
		List<String> codes = new ArrayList<>();
		QueryWrapper<LedgerAccount> queryLa = new QueryWrapper<>();
		List<LedgerAccount> ledgerAccounts = ledgerAccountMapper.selectList(queryLa);
		for(LedgerAccount ledgerAccount : ledgerAccounts) {
			if(ledgerAccount.getCode().contains("560") || ledgerAccount.getCode().contains("540") || ledgerAccount.getCode().contains("570")) {
				codes.add(ledgerAccount.getCode());
			}
		}
		query.setMonthStrat(monthStr);
		query.setMonthEnd(monthStr);
		query.setVcodes(codes);
		List<VoucherSumVO> sumList = voucherMapper.sumVoucher(query);

		//查询今年每个月的
		String monthEnd = DateUtil.format(new Date(), "yyyy-MM");
		String monthStart = DateUtil.format(DateUtil.beginOfYear(new Date()), "yyyy-MM");
		VoucherQuery queryMonth = new VoucherQuery();
		queryMonth.setMonthStrat(monthStart);
		queryMonth.setMonthEnd(monthEnd);
		queryMonth.setVcodes(codes);
		List<VoucherSumVO> sumMonthList = voucherMapper.sumVoucherMonth(queryMonth);
        setCashAnalyYear(sumMonthList, res);

		res.setBillingPeriod(billingPeriod);
		CashAnaly cashAnaly = getCashAnaly(initMonths, inits);
		res.setCashAnaly(cashAnaly);
		CostAnaly costAnaly = getCostAnaly(sumList);
		res.setCostAnaly(costAnaly);
		FundingMetrics fundingMetrics = getFundingMetrics(profits, balanceSheets, initMonths, inits);
		res.setFundingMetrics(fundingMetrics);
		FundingTran fundingTran1 = getFundingTran(initMonths, inits);
		res.setFundingTran(fundingTran1);
		FundingMetrics fundingMetricsLast = getFundingMetrics(profitsLast, balanceSheetsLast, initMonthsLast, inits);
		res.setFundingMetricsLast(fundingMetricsLast);
		BusinessAnaly businessAnaly = getBusinessAnaly(profits, sumList);
		res.setBusinessAnaly(businessAnaly);
		List<NeedDeal> needs = this.getNeedDeal();
		res.setNeedDeals(needs);
		return res;
	}

	/**
	 * setCashAnalyYear:设置年度支出.
	 * Date:2024年6月4日下午8:05:58
	 * <AUTHOR>
	 * @param sumMonthList
	 * @param res
	 * @since version 1.0
	 */
	private void setCashAnalyYear(List<VoucherSumVO> sumMonthList, OamsHomeVO res) {
		CostAnaly costYear = new CostAnaly();
		BigDecimal financeExpenses = BigDecimal.ZERO;
		BigDecimal managerExpenses = BigDecimal.ZERO;
		BigDecimal saleExpenses = BigDecimal.ZERO;
		BigDecimal taxes = BigDecimal.ZERO;
		//List<CostAnaly> costs = new ArrayList<>();
		int currentMonth = DateUtil.month(new Date())+1;
		Map<String, CostAnaly> costMonths = new TreeMap<>();
		for(int index=1; index<=currentMonth;index++) {
			String monthStr = index+"月";
			CostAnaly costAnaly = new CostAnaly(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, monthStr);
			costMonths.put(monthStr, costAnaly);
		}
		for(VoucherSumVO sum : sumMonthList) {
			String month = sum.getMonth();
			Date date = DateUtil.parse(month, "yyyy-MM");
			String monthStr = (DateUtil.month(date)+1)+"月";
			if(sum.getCode().startsWith("5601")) {
				saleExpenses = saleExpenses.add(sum.getAmoutIn());
				costMonths.get(monthStr).setSaleExpenses(costMonths.get(monthStr).getSaleExpenses().add(sum.getAmoutIn()));
			}
			if(sum.getCode().startsWith("5602")) {
				managerExpenses = managerExpenses.add(sum.getAmoutIn());
				costMonths.get(monthStr).setManagerExpenses((costMonths.get(monthStr).getManagerExpenses().add(sum.getAmoutIn())));
			}
			if(sum.getCode().startsWith("5603")) {
				financeExpenses = financeExpenses.add(sum.getAmoutIn());
				costMonths.get(monthStr).setFinanceExpenses((costMonths.get(monthStr).getFinanceExpenses().add(sum.getAmoutIn())));
			}
			if(sum.getCode().startsWith("5403")) {
				taxes = taxes.add(sum.getAmoutIn());
				costMonths.get(monthStr).setTaxes((costMonths.get(monthStr).getTaxes().add(sum.getAmoutIn())));
			}
		}
		costYear.setFinanceExpenses(financeExpenses);
		costYear.setManagerExpenses(managerExpenses);
		costYear.setSaleExpenses(saleExpenses);
		costYear.setTaxes(taxes);
		res.setCostAnalyYear(costYear);

		res.setCostAnalys(costMonths.values());
	}

	private CashAnaly getCashAnaly(Map<String, LedgerAccountInitMonth> initMonths, Map<String, LedgerAccountInit> inits) {
		CashAnaly cashAnaly = new CashAnaly();

		if(initMonths.containsKey("1001")) {
			cashAnaly.setBlance1001(initMonths.get("1001").getMonthOpening());
        } else if(initMonths.containsKey("1001")) {
        	cashAnaly.setBlance1001(inits.get("1001").getOpening());
        }
		if(initMonths.containsKey("1002")) {
			cashAnaly.setBlance1002(initMonths.get("1002").getMonthOpening());
        } else if(initMonths.containsKey("1002")) {
        	cashAnaly.setBlance1002(inits.get("1002").getOpening());
        }

		return cashAnaly;
	}

	/**
	 * ClassName: BusinessAnaly
	 * Function: 经营状况分析.
	 * date: 2023年12月11日 下午3:53:52
	 *
	 * <AUTHOR>
	 * @version OamsHomeVO
	 * @param profits
	 * @param sumList
	 * @since version 1.0
	 *
	 * 收入:利润表--营业收入的本期金额 1
	 * 成本:利润表--营业成本的本期金额 2
	   费用: 5601销售费用借方发生额+5602管理费用借方发生额+5603财务费用借方发生额+5403税金及附加 +5701资产减值损失
	   净利润: 利润表--净利润的本期金额 17
	 */
	private BusinessAnaly getBusinessAnaly(Map<String, OamsProfitReportVO> profits, List<VoucherSumVO> sumList) {
		BusinessAnaly businessAnaly = new BusinessAnaly();
		BigDecimal expense = BigDecimal.ZERO;
		for(VoucherSumVO sum : sumList) {
			if(sum.getCode().startsWith("5601") || sum.getCode().startsWith("5602") || sum.getCode().startsWith("5603")
					|| sum.getCode().startsWith("5403") || sum.getCode().startsWith("5701")) {
				expense = expense.add(sum.getAmoutIn());
			}
		}
		businessAnaly.setP1(profits.get("1").getAccumulated());
		businessAnaly.setP2(profits.get("2").getAccumulated());
		businessAnaly.setP17(profits.get("17").getAccumulated());
		businessAnaly.setExpenses(expense);
		return businessAnaly;
	}

	/**
	 * getFundingTran:获取资金往来.
       数据时间范围：当月1号至当前时间
       查银行存款、库存现金、应收账款、应付账款的期末余额
	 *
	 * <AUTHOR>
	 * @param inits
	 * @param initMonths
	 * @return
	 * @since version 1.0
	 */
	private FundingTran getFundingTran(Map<String, LedgerAccountInitMonth> initMonths, Map<String, LedgerAccountInit> inits) {
		FundingTran fundingTran = new FundingTran();

		if(initMonths.containsKey("1001")) {
			fundingTran.setBlance1001(initMonths.get("1001").getMonthOpening());
        } else if(initMonths.containsKey("1001")) {
        	fundingTran.setBlance1001(inits.get("1001").getOpening());
        }
		if(initMonths.containsKey("1002")) {
			fundingTran.setBlance1002(initMonths.get("1002").getMonthOpening());
        } else if(initMonths.containsKey("1002")) {
        	fundingTran.setBlance1002(inits.get("1002").getOpening());
        }
		if(initMonths.containsKey("1122")) {
			fundingTran.setBlance1122(initMonths.get("1122").getMonthOpening());
        } else if(initMonths.containsKey("1122")) {
        	fundingTran.setBlance1122(inits.get("1122").getOpening());
        }
		if(initMonths.containsKey("2202")) {
			fundingTran.setBlance2202(initMonths.get("2202").getMonthOpening());
        } else if(initMonths.containsKey("2202")) {
        	fundingTran.setBlance2202(inits.get("2202").getOpening());
        }
		return fundingTran;
	}

	/**
	 *  货币资金：资产负债表--货币资金的期末余额
		存货：资产负债表--存货的期末余额
		固定资产：1601固定资产(期末余额)-1602累计折旧(期末余额)
		营业收入：利润表--营业收入的本期金额
		利润额：利润表--利润总额的本期金额
		净利润：利润表--净利润的本期金额
		净利率：净利率=(净利润/营业收入)×100%
	 * getFundingMetrics:财务指标.
	 *
	 * <AUTHOR>
	 * @param inits
	 * @param initMonths
	 * @param balanceSheets
	 * @param profits
	 * @return
	 * @since version 1.0
	 */
	private FundingMetrics getFundingMetrics(Map<String, OamsProfitReportVO> profits, Map<String, BalanceSheetReportVO> balanceSheets,
			Map<String, LedgerAccountInitMonth> initMonths, Map<String, LedgerAccountInit> inits) {
		FundingMetrics fundingMetrics = new FundingMetrics();
		fundingMetrics.setA2(balanceSheets.get("A2").getBlanceAss());
		fundingMetrics.setA10(balanceSheets.get("A10").getBlanceAss());
		fundingMetrics.setA23(balanceSheets.get("A23").getBlanceAss());
		fundingMetrics.setP1(profits.get("1").getAccumulated());
		fundingMetrics.setP15(profits.get("15").getAccumulated());
		fundingMetrics.setP15(profits.get("17").getAccumulated());;
		return fundingMetrics;
	}

	/**
	 * getCostAnaly:成本分析.

 	 *  销售费用:销售费用发生额   5601
		管理费用:管理费用发生额   5602
		财务费用:财务费用发生额   5603
		税金费用:税金费用发生额   5403
	 *
	 * <AUTHOR>
	 * @param sumList
	 * @return
	 * @since version 1.0
	 */
	private CostAnaly getCostAnaly(List<VoucherSumVO> sumList) {

		CostAnaly res = new CostAnaly();
		BigDecimal financeExpenses = BigDecimal.ZERO;
		BigDecimal managerExpenses = BigDecimal.ZERO;
		BigDecimal saleExpenses = BigDecimal.ZERO;
		BigDecimal taxes = BigDecimal.ZERO;
		for(VoucherSumVO sum : sumList) {
			if(sum.getCode().startsWith("5601")) {
				saleExpenses = saleExpenses.add(sum.getAmoutIn());
			}
			if(sum.getCode().startsWith("5602")) {
				managerExpenses = managerExpenses.add(sum.getAmoutIn());
			}
			if(sum.getCode().startsWith("5603")) {
				financeExpenses = financeExpenses.add(sum.getAmoutIn());
			}
			if(sum.getCode().startsWith("5403")) {
				taxes = taxes.add(sum.getAmoutIn());
			}
		}
		res.setFinanceExpenses(financeExpenses);
		res.setManagerExpenses(managerExpenses);
		res.setSaleExpenses(saleExpenses);
		res.setTaxes(taxes);
		return res;
	}

	/**
	 * getBillingPeriod:凭证数据.
	 *
	 * <AUTHOR>
	 * @return
	 * @since version 1.0
	 */
	private BillingPeriod getBillingPeriod() {
		BillingPeriod billingPeriod = new BillingPeriod();
		Date startDay = DateUtil.beginOfMonth(new Date());
		Date endDay = DateUtil.endOfMonth(new Date());
		QueryWrapper<Voucher> queryVou = new QueryWrapper<>();
		queryVou.select("count(1) as indexs, sum(vouchers_num) vouchers_num");
		queryVou.between("vdate", startDay, endDay);
		Voucher voucher = voucherService.getOne(queryVou);
		if(voucher != null && voucher.getVouchersNum()!=null) {
			billingPeriod.setAnnexNums(voucher.getVouchersNum());
			billingPeriod.setVouNums(voucher.getIndexs().longValue());
		}

		boolean carry = false;
		queryVou.eq("close_type", 1);
		Voucher voucherClose = voucherService.getOne(queryVou);
		if(voucherClose.getIndexs()>0) {
			carry = true;
		}
		billingPeriod.setCarry(carry);

		int year = DateUtil.year(new Date());
		int month = DateUtil.month(new Date())+1;
		QueryWrapper<AccountColse> queryClose = new QueryWrapper<>();
		queryClose.eq("year", year);
		queryClose.eq("month", month);
		AccountColse close = accountColseService.getOne(queryClose);
		if(close!=null && close.getStatus()==1) {
			billingPeriod.setCheckout(true);
		} else {
			billingPeriod.setCheckout(false);
		}
		return billingPeriod;
	}
}
