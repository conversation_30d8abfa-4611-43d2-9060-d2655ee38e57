package com.huayun.modules.erp.mms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.common.entity.LevelEntity;
import com.huayun.modules.erp.mms.constant.enums.CliamTypeEnum;
import com.huayun.modules.erp.mms.entity.handler.CliamEmpsHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 组织管理
 *
 * <AUTHOR>
 * @date 2022/07/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("mms_organization")
@Accessors(chain = true)
@NoArgsConstructor
public class Organization extends BaseTenantEntity implements LevelEntity {

    /**
     * 上级组织id
     */
    @ApiModelProperty(value = "上级组织id")
    private String parentId;
    /**
     * 组织路径
     */
    @ApiModelProperty(value = "组织路径")
    private String path;
    /**
     * 组织负责人id
     */
    @ApiModelProperty(value = "组织负责人id")
    private String supervisorId;
    /**
     * 职能id
     */
    @ApiModelProperty(value = "职能id")
    private String functionId;
    /**
     * 组织类型id
     */
    @ApiModelProperty(value = "组织类型id")
    private String typeId;
    /**
     * 组织班次id
     */
    @ApiModelProperty(value = "组织班次id")
    private String shiftId;
    /**
     * 组织图标URI
     */
    @ApiModelProperty(value = "组织图标URI")
    private String iconUri;
    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String name;
    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    private String code;
    /**
     * 组织描述
     */
    @ApiModelProperty(value = "组织描述")
    private String description;
    /**
     * 是否启用，0:不启用；1:启用
     */
    @ApiModelProperty(value = "是否启用，0:不启用；1:启用")
    private Boolean disabled;



    /**
     * 领料规则
     */
    @ApiModelProperty(value = "领料规则")
    private CliamTypeEnum cliamType;


    /**
     * 领料的物料员
     */
    @ApiModelProperty(value = "领料的物料员")
    @TableField(typeHandler = CliamEmpsHandler.class)
    private List<CliamEmps> cliamEmps;

    @ApiModelProperty(value = "超量报工（%）")
    private Integer excessReport;


    // NOT EXISTS IN TABLE =============================================================================================

    /**
     * 上级组织
     */
    @ApiModelProperty(value = "上级组织")
    @TableField(exist = false)
    private String parentName;
    /**
     * 上级部门路径
     */
    @ApiModelProperty(value = "上级部门路径")
    @TableField(exist = false)
    private String parentPath;
    /**
     * 组织负责人
     */
    @ApiModelProperty(value = "组织负责人")
    @TableField(exist = false)
    private String supervisorName;
    /**
     * 职能
     */
    @ApiModelProperty(value = "职能")
    @TableField(exist = false)
    private String functionName;
    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型")
    @TableField(exist = false)
    private String typeName;
    /**
     * 组织班次
     */
    @ApiModelProperty(value = "组织班次")
    @TableField(exist = false)
    private String shiftName;

    @Override
    public void setPath(String path) {
        this.path = path;
    }
}
