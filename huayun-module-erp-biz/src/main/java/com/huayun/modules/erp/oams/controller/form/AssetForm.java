package com.huayun.modules.erp.oams.controller.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.erp.oams.constant.enums.AssetStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Positive;
import java.math.BigDecimal;

@Data
public class AssetForm {
	/**年份*/
    @ApiModelProperty(value = "年份")
	private java.lang.Integer year;
	/**月份*/
    @ApiModelProperty(value = "月份")
	private java.lang.Integer month;
	/**资产id*/
    @ApiModelProperty(value = "(设备)资产id")
	private java.lang.String assetId;
	/**资产编码*/
    @ApiModelProperty(value = "资产编码")
	private java.lang.String assetNo;
	/**资产名称*/
    @ApiModelProperty(value = "资产名称")
	private java.lang.String assetName;
	/**型号*/
    @ApiModelProperty(value = "型号")
	private java.lang.String model;
	/**资产类型*/
    @ApiModelProperty(value = "资产类型")
	private java.lang.String assetType;
	/**入账日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "入账日期")
	private java.util.Date activeDate;
	/**原值*/
	@Positive(message = "原值必须大于0")
	@ApiModelProperty(value = "原值")
	private java.math.BigDecimal originAmount;
	/**使用年限*/
    @ApiModelProperty(value = "使用年限")
	private java.lang.Integer serviceLife;
	/**残值率*/
    @ApiModelProperty(value = "残值率")
	private java.math.BigDecimal residualValueRate;
	/**累计折旧*/
    @ApiModelProperty(value = "累计折旧")
	private java.math.BigDecimal depreciationAmount;
	/**总利息*/
    @ApiModelProperty(value = "总利息")
	private java.math.BigDecimal commissionAmount;
	/**本月维保费*/
    @ApiModelProperty(value = "本月维保费")
	private java.math.BigDecimal maintenanceAmount;
	/**折旧方法*/
    @ApiModelProperty(value = "折旧方法")
	private java.lang.String depreciationMethod;
	/**是否已经按月计算*/
    @ApiModelProperty(value = "是否已经按月计算")
	private boolean monthStatus;
	/**数量 不填默认为1*/
    @ApiModelProperty(value = "数量 不填默认为1")
	private BigDecimal nums;
	/**单位*/
    @ApiModelProperty(value = "单位")
	private java.lang.String unit;
	/**设备编码*/
    @ApiModelProperty(value = "设备编码")
	private java.lang.String deviceCode;
	/**使用部门*/
    @ApiModelProperty(value = "使用部门")
	private java.lang.String useDepartment;
	/**摆放地点*/
    @ApiModelProperty(value = "摆放地点")
	private java.lang.String deviceAddress;
	/**来源*/
    @ApiModelProperty(value = "来源")
	private java.lang.String source;
	/**开始使用日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "开始使用日期")
	private java.util.Date useDate;
	/**录入当期是否折旧*/
    @ApiModelProperty(value = "录入当期是否折旧")
	private boolean depreciateThisPeriod;
	/**资产科目*/
    @ApiModelProperty(value = "资产科目")
	private java.lang.String assertSubject;
	/**累计折旧科目*/
    @ApiModelProperty(value = "累计折旧科目")
	private java.lang.String depreciationSubject;
	/**资产清理科目*/
    @ApiModelProperty(value = "资产清理科目")
	private java.lang.String assetClearingSubject;
	/**折旧费用科目*/
    @ApiModelProperty(value = "折旧费用科目")
	private java.lang.String depreciationExpenseSubject;
	/**预计使用月份*/
    @ApiModelProperty(value = "预计使用月份")
	@Positive(message = "预计使用月份必须大于0")
	private java.lang.Integer estimatedUsageMonths;
	/**已折旧月份*/
    @ApiModelProperty(value = "已折旧月份")
	private java.lang.Integer accumulatedDepreciationMonths;
	/**财务备注*/
    @ApiModelProperty(value = "财务备注")
	private java.lang.String financialRemark;
	/**资产备注*/
    @ApiModelProperty(value = "资产备注")
	private java.lang.String assetRemark;
	/**是否是设备信息*/
    @ApiModelProperty(value = "是否是设备信息")
	private boolean deviceFlag;
	/**设备序列码*/
    @ApiModelProperty(value = "设备序列码")
	private java.lang.String devSequenceNum;

	/**供应商id*/
	@ApiModelProperty(value = "供应商id")
	private String supplierId;

	/**供应商名称*/
	@ApiModelProperty(value = "供应商名称")
	private String supplierName;


	@ApiModelProperty(value = "状态")
	private AssetStateEnum state;


}
