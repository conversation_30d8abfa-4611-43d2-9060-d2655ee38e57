package com.huayun.modules.erp.mro.entity.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
public class DevicePersonnelModel {
    /**
     * 设备类型
     */
    @NotEmpty(message = "人员ID不为空！")
    @ApiModelProperty(value = "人员ID")
    private String id;
    /**
     * 真实姓名
     */
    @NotEmpty(message = "真实姓名不为空!")
    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @Override
    public String toString() {
        return realName;
    }
}
