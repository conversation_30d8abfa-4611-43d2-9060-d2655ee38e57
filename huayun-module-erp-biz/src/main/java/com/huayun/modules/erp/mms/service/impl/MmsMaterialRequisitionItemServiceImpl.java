package com.huayun.modules.erp.mms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.huayun.modules.common.enums.YesOrNOEnum;
import com.huayun.modules.erp.mms.entity.MmsMaterialRequisitionItem;
import com.huayun.modules.erp.mms.entity.model.RequisitionItemModel;
import com.huayun.modules.erp.mms.mapper.MmsMaterialRequisitionItemMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huayun.modules.erp.mms.service.IMmsMaterialRequisitionItemService;
import com.huayun.modules.erp.wms.entity.Material;
import com.huayun.modules.erp.wms.service.material.IMaterialService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 领料料物料清单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
@Service
public class MmsMaterialRequisitionItemServiceImpl extends ServiceImpl<MmsMaterialRequisitionItemMapper, MmsMaterialRequisitionItem> implements IMmsMaterialRequisitionItemService {

    @Resource
    private IMaterialService iMaterialService;
    
    @Override
    public List<RequisitionItemModel> findListByRequisitionId(Collection<String> requisitionIds) {
        if(CollUtil.isEmpty(requisitionIds)){
            return Collections.emptyList();
        }
        List<RequisitionItemModel> models = new LinkedList<>();
        LambdaQueryWrapper<MmsMaterialRequisitionItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MmsMaterialRequisitionItem::getDeleted, YesOrNOEnum.NO.getValue())
                .in(CollUtil.isNotEmpty(requisitionIds),MmsMaterialRequisitionItem::getRequisitionId,requisitionIds);
        List<MmsMaterialRequisitionItem> materialRequisitionItems = this.list(wrapper);

        List<String> materialIds = materialRequisitionItems.stream().map(MmsMaterialRequisitionItem::getMaterialId).collect(Collectors.toList());
        List<Material> materialList = iMaterialService.getMaterialListByIds(materialIds);

        Map<String, Material> materialMap = materialList.stream().collect(Collectors.toMap(Material::getId, Function.identity()));

        for (MmsMaterialRequisitionItem materialRequisitionItem : materialRequisitionItems) {
            RequisitionItemModel requisitionItemModel = new RequisitionItemModel();
            Material material = materialMap.get(materialRequisitionItem.getMaterialId());
            BeanUtil.copyProperties(materialRequisitionItem,requisitionItemModel);
            requisitionItemModel.setMaterialName(material.getName());
            requisitionItemModel.setUnit(material.getUnit());
            requisitionItemModel.setSpecification(material.getSpecification());
            models.add(requisitionItemModel);
        }

        return models;
    }
}
