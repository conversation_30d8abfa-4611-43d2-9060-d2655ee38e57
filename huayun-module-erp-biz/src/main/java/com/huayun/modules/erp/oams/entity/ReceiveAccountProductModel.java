package com.huayun.modules.erp.oams.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 应收账龄
 *
 * <AUTHOR>
 **/
@Data
public class ReceiveAccountProductModel {

    /**客户id*/
    @ApiModelProperty(value = "客户id")
    private String customerId;

    /**客户名称*/
    @ApiModelProperty(value = "客户名称")
    private String customerName;


    @ApiModelProperty(value = "一个月内 金额")
    private BigDecimal zeroAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "一个月内 客户数量")
    private BigDecimal zeroNumber = BigDecimal.ZERO;

    @ApiModelProperty(value = "1~2个月 金额")
    private BigDecimal oneAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "1~2个月 客户数量")
    private BigDecimal oneNumber = BigDecimal.ZERO;

    @ApiModelProperty(value = "2~3个月 金额")
    private BigDecimal twoAmount= BigDecimal.ZERO;
    @ApiModelProperty(value = "2~3个月 客户数量")
    private BigDecimal twoNumber = BigDecimal.ZERO;

    @ApiModelProperty(value = "3~6个月 金额")
    private BigDecimal threeAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "3~6个月 客户数量")
    private BigDecimal threeNumber = BigDecimal.ZERO;

    @ApiModelProperty(value = "6~12个月 金额")
    private BigDecimal fourAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "6~12个月 客户数量")
    private BigDecimal fourNumber = BigDecimal.ZERO;
    
    @ApiModelProperty(value = "1~2年 金额")
    private BigDecimal fiveAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "1~2年 客户数量")
    private BigDecimal fiveNumber = BigDecimal.ZERO;

    @ApiModelProperty(value = "2~3年 金额")
    private BigDecimal sixAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "2~3年 客户数量")
    private BigDecimal sixNumber = BigDecimal.ZERO;

    @ApiModelProperty(value = "3年以上 金额")
    private BigDecimal sevenAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "3年以上 客户数量")
    private BigDecimal sevenNumber = BigDecimal.ZERO;



    /**合计金额*/
    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalAmount = BigDecimal.ZERO;
}
