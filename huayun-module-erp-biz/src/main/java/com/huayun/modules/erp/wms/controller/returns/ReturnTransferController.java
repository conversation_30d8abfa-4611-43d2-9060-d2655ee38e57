package com.huayun.modules.erp.wms.controller.returns;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huayun.modules.common.log.aspect.annotation.Log;
import com.huayun.modules.common.log.constant.OperateTypeConstant;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.erp.wms.constant.WmsConstant;
import com.huayun.modules.erp.wms.entity.ReturnTransfer;
import com.huayun.modules.erp.wms.form.*;
import com.huayun.modules.erp.wms.query.DetachQuery;
import com.huayun.modules.erp.wms.query.ReturnTransferQuery;
import com.huayun.modules.erp.wms.service.returns.IReturnTransferService;
import com.huayun.modules.erp.wms.valid.DetachValid;
import com.huayun.modules.erp.wms.vo.ReturnTransferLotNoVo;
import com.huayun.modules.erp.wms.vo.ReturnTransferVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @description 退货仓调拨单
 * @date 2024/10/14 15:59
 */
@Slf4j
@Api(tags = "WMS-退货仓调拨单")
@RestController
@RequestMapping("/wms/return-transfer")
public class ReturnTransferController {


    @Resource
    private IReturnTransferService returnTransferService;


    @Autowired
    private DetachValid detachValid;



    @Log(resourceType = WmsConstant.RETURN_TRANSFER, operateType = OperateTypeConstant.INSERT, template = "退货仓添加调拨单,调拨单号：“${transferNo}”")
    @ApiOperation(value = "新增退货仓调拨单", notes = "新增退货仓调拨单")
    @PostMapping("/infos")
    public Result<ReturnTransferVO> createTransferInfo(@Validated @RequestBody ReturnTransferForm returnTransferForm) {
        //校验
        String validResult = detachValid.returnTransferInsertValid(returnTransferForm);
        BizAssert.isTrue(StringUtils.isBlank(validResult), validResult);
        //新增
        ReturnTransfer returnTransfer = returnTransferService.createTransferInfo(returnTransferForm);
        return Result.OK("添加成功！",new ReturnTransferVO(returnTransfer));
    }

    @ApiOperation(value = "暂存退货仓调拨单", notes = "暂存退货仓调拨单")
    @PostMapping("/stage")
    public Result<ReturnTransferVO> stageTransferInfo(@Validated @RequestBody ReturnTransferForm returnTransferForm) {
        //校验
        String validResult = detachValid.returnTransferInsertValid(returnTransferForm);
        BizAssert.isTrue(StringUtils.isBlank(validResult), validResult);
        //新增
        ReturnTransfer returnTransfer = returnTransferService.stageTransferInfo(returnTransferForm);
        return Result.OK("添加成功！",new ReturnTransferVO(returnTransfer));
    }



    @Log(resourceType = WmsConstant.RETURN_TRANSFER, operateType = OperateTypeConstant.UPDATE, template = "退货仓添加调拨单,调拨单号：“${transferNo}”")
    @ApiOperation(value = "编辑退货仓调拨单", notes = "编辑退货仓调拨单")
    @PutMapping("/edit")
    public Result<ReturnTransferVO> updateTransferInfo(@Validated @RequestBody ReturnTransferForm returnTransferForm) {
        //校验
        String validResult = detachValid.returnTransferInsertValid(returnTransferForm);
        BizAssert.isTrue(StringUtils.isBlank(validResult), validResult);
        //编辑
        ReturnTransfer returnTransfer = returnTransferService.updateTransferInfo(returnTransferForm);
        return Result.OK("添加成功！",new ReturnTransferVO(returnTransfer));
    }



    /**
     * 分页列表查询退货仓调拨单
     *
     */
    @ApiOperation(value = "分页列表查询退货仓调拨单", notes = "分页列表查询退货仓调拨单")
    @GetMapping("/page")
    public Result<IPage<ReturnTransferVO>> queryPageList(ReturnTransferQuery returnTransferQuery) {
        IPage<ReturnTransferVO> pageList = returnTransferService.queryPageList(returnTransferQuery);
        return Result.OK("查询成功！", pageList);
    }




    /**
     * 根据id查询调拨单明细信息
     */
    @ApiOperation(value = "根据id查询调拨单明细信息", notes = "根据id查询调拨单明细信息")
    @GetMapping("/infoById/{id}")
    public Result<ReturnTransferVO> getInfoById(@PathVariable String id) {
        ReturnTransferVO returnTransferVO = returnTransferService.getInfoById(id);
        return Result.OK("查询成功！", returnTransferVO);
    }


    /**
     * 根据id删除拆卸单信息
     */
    @ApiOperation(value = "根据id删除调拨单信息", notes = "根据id删除调拨单信息")
    @DeleteMapping("/info/{id}")
    public Result deleteReturnTransfer(@PathVariable String id) {
        returnTransferService.deleteReturnTransfer(id);
        return Result.OK("删除成功!");
    }


    /**
     * 上架物料
     * @param form
     * @return
     */
    @ApiOperation(value="上架", notes="上架")
    @PostMapping("/storages")
    public Result<String> storages(@Validated @RequestBody ReturnTransferStorageForm form) {
        returnTransferService.storages(form);
        return Result.OK("上架成功！");
    }



    @ApiOperation(value="确认入库", notes="确认入库")
    @PutMapping("/receiptStorages")
    public Result<String> receiptStorages(@Validated @RequestBody ReturnTransferReceiptForm form) {
        returnTransferService.receiptStorages(form);
        return Result.OK("确认入库成功");
    }


    /**
     *
     * <AUTHOR>
     * @description 查询标签号层级的拆卸单信息 - app，序列号按调拨单查
     * @date 2025/5/22 13:45
     * @param query
     * @return org.jeecg.common.api.vo.Result<java.util.List<com.huayun.modules.erp.wms.vo.DetachLotNoVo>>
     */
    @ApiOperation(value="查询标签号层级的拆卸单信息", notes="查询标签号层级的拆卸单信息")
    @GetMapping("/getAllLotNo")
    public Result<List<ReturnTransferLotNoVo>> getAllLotNo(ReturnTransferQuery query) {
        return Result.OK(returnTransferService.getAllLotNo(query));
    }

}
