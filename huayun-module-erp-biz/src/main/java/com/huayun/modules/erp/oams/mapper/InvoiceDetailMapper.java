package com.huayun.modules.erp.oams.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.oams.controller.query.InvoiceQuery;
import com.huayun.modules.erp.oams.entity.InvoiceDetail;
import com.huayun.modules.erp.oams.vo.InvoiceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 发票详情
 * @Author: jeecg-boot
 * @Date:   2023-10-20
 * @Version: V1.0
 */
public interface InvoiceDetailMapper extends BaseMapper<InvoiceDetail> {

	void delete(@Param("query") InvoiceDetail del);

    Page<InvoiceVO> getTicketRecords(Page<InvoiceVO> invoiceVOPage, @Param(Constants.WRAPPER) QueryWrapper<InvoiceQuery> queryWrapper);

    /**
     * 查找开票数量：订单-发货单-产品--》数量
     * @param queryWrapper
     * @return
     */
    List<InvoiceDetail> selectAggregatedInvoiceDetails(@Param(Constants.WRAPPER)  QueryWrapper<InvoiceQuery> queryWrapper);

    List<InvoiceDetail> getInvoiceQuantityGroupByMaterialId(@Param(Constants.WRAPPER)  QueryWrapper<InvoiceQuery> queryWrapper);
}
