package com.huayun.modules.erp.oms.service;

import com.huayun.modules.erp.oms.entity.Order;
import com.huayun.modules.erp.oms.entity.OrderLog;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IOrderLogService {
    
    /**
     * 保存订单状态变更日志
     *
     * @param order
     * @param type
     * @return void
     * <AUTHOR>
     * @date 2022/12/27 11:52
     */
    @Transactional(rollbackFor = Exception.class)
    void save(Order order, String type);

    /**
     * 根据订单id查询状态变更日志
     *
     * <AUTHOR>
     * @date 2022/12/27 16:57
     * @param id
     * @return java.util.List<com.huayun.modules.erp.oms.entity.OrderLog>
     */
    List<OrderLog> listByOrderId(String id);
}
