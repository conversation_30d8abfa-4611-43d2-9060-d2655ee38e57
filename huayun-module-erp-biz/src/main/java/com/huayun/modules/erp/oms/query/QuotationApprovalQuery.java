package com.huayun.modules.erp.oms.query;

import com.huayun.modules.common.mybatis.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 报价单审批查询条件
 */
@Data
public class QuotationApprovalQuery extends BaseQuery {

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 报价单号
     */
    private String orderNo;

    /**
     * 审批状态
     */
    private String state;

    @ApiModelProperty("业务员")
    private String supervisor;

    @ApiModelProperty("业务员名称")
    private String supervisorName;

    /**
     * 交付开始日期
     */
    @ApiModelProperty(value = "交付开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDeliveryDate;

    /**
     * 交付结束日期
     */
    @ApiModelProperty(value = "交付结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDeliveryDate;
}
