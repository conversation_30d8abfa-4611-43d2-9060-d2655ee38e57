package com.huayun.modules.erp.oams.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
@TableName(value = "oams_currency", autoResultMap = true)
public class Currency extends BaseTenantEntity {

    @ApiModelProperty(value = "货币名称")
    @TableField(exist = false)
    private String name;
    @ApiModelProperty(value = "货币编码")
    private String code;
    @ApiModelProperty(value = "汇率")
    private BigDecimal exchangeRate;
    @ApiModelProperty(value = "是否为默认货币：0-否；1-是|本位币")
    private Boolean isDefault;
    @ApiModelProperty(value = "是否生效：0-否；1-是")
    private Boolean isActive;
}
