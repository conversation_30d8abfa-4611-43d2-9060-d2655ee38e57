package com.huayun.modules.erp.mro.vo;

import com.huayun.modules.erp.mro.entity.DeviceTypeFunction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * TODO description
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@Data
@NoArgsConstructor
public class DeviceTypeFunctionVO {
    /**
     * 工具功能编码类型
     */
    @ApiModelProperty(value = "工具id")
    private String id;
    /**
     * 工具功能编码类型
     */
    @ApiModelProperty(value = "工具功能编码类型")
    private String funTypeCode;
    /**
     * 工具功能编码类型名称
     */
    @ApiModelProperty(value = "工具功能编码类型名称")
    private String funTypeName;

    public DeviceTypeFunctionVO(DeviceTypeFunction deviceTypeFunction) {
        init(deviceTypeFunction);
    }

    private void init(DeviceTypeFunction deviceTypeFunction) {
        BeanUtils.copyProperties(deviceTypeFunction, this);
    }
}