package com.huayun.modules.erp.oams.controller.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.huayun.modules.common.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 资产列表导出实体
 * @date 2024/10/28 13:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AssetExportVO extends BaseVO {


    /**
     * 资产编码
     */
    @Excel(name = "资产编码", width = 15)
    @ApiModelProperty(value = "资产编码")
    private String assetNo;


    /**
     * 资产名称
     */
    @Excel(name = "资产名称", width = 15)
    @ApiModelProperty(value = "资产名称")
    private String assetName;


    /**
     * 资产类别"
     */
    @Excel(name = "资产类别", width = 15)
    @ApiModelProperty(value = "资产类别")
    private String assetTypeName;


    /**
     * 部门
     */
    @Excel(name = "部门", width = 15)
    @ApiModelProperty(value = "部门")
    private String useDepartmentName;


    /**
     * 资产规格
     */
    @Excel(name = "资产规格", width = 15)
    @ApiModelProperty(value = "资产规格")
    private String model;


    /**
     * 开始使用时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始使用时间", width = 15, format = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始使用时间")
    private Date useDate;


    /**
     * 折旧方法
     */
    @Excel(name = "折旧方法", width = 15)
    @ApiModelProperty(value = "折旧方法")
    private String depreciationMethod;


    /**
     * 原值
     */
    @Excel(name = "原值", width = 15)
    @ApiModelProperty(value = "原值")
    private BigDecimal originAmount;


    /**
     * 残值率
     */
    @Excel(name = "残值率", width = 15)
    @ApiModelProperty(value = "残值率")
    private BigDecimal residualValueRate;


    /**
     * 净残值
     */
    @Excel(name = "净残值", width = 15)
    @ApiModelProperty(value = "净残值")
    private BigDecimal salvageValue;


    /**
     * 预计使用月份
     */
    @Excel(name = "预计使用月份", width = 15)
    @ApiModelProperty(value = "预计使用月份")
    private Integer estimatedUsageMonths;


    /**
     * 已计提月份
     */
    @Excel(name = "已计提月份", width = 15)
    @ApiModelProperty(value = "已计提月份")
    private Integer accumulatedDepreciationMonths;


    /**
     * 本月折旧额
     */
    @Excel(name = "本月折旧额", width = 15)
    @ApiModelProperty(value = "本月折旧额")
    private BigDecimal monthlyDepreciationAmount;


    /**
     * 累计折旧
     */
    @Excel(name = "累计折旧", width = 15)
    @ApiModelProperty(value = "累计折旧")
    private BigDecimal depreciationAmount;


    /**
     * 净值
     */
    @Excel(name = "净值", width = 15)
    @ApiModelProperty(value = "净值")
    private BigDecimal networthValue;


    public AssetExportVO(AssetVO asset) {
        BeanUtils.copyProperties(asset, this);
        this.salvageValue = asset.getSalvageValue().setScale(2, RoundingMode.HALF_UP);


    }

    public AssetExportVO() {

    }


}
