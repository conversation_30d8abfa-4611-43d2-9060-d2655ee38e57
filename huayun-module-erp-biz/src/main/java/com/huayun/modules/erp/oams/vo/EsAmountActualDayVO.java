package com.huayun.modules.erp.oams.vo;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;

import org.apache.commons.beanutils.BeanUtilsBean2;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.BeanUtils;

import com.huayun.modules.common.vo.BaseVO;
import com.huayun.modules.erp.oams.entity.EsAmount;
import com.huayun.modules.erp.oams.entity.EsAmountActual;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 阿米巴预定
 * @Author: jeecg-boot
 * @Date: 2024-07-08
 * @Version: V1.0
 */

@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "阿米巴实绩", description = "阿米巴实绩")
public class EsAmountActualDayVO extends BaseVO implements Serializable {

	/** 项目 */
	@Excel(name = "项目", width = 15)
	@ApiModelProperty(value = "项目")
	private java.lang.String project;
	/** 类别 */
	@Excel(name = "类别", width = 15)
	@ApiModelProperty(value = "类别")
	private java.lang.String category;
	/** 科目 */
	@Excel(name = "科目", width = 15)
	@ApiModelProperty(value = "科目")
	private java.lang.String account;
	/** 部门 */
	@Excel(name = "部门", width = 15)
	@ApiModelProperty(value = "部门")
	private java.lang.String dept;
	/** 部门 巴 */
	@Excel(name = "部门 巴", width = 15)
	@ApiModelProperty(value = "部门 巴")
	private java.lang.String deptSun;
	/** 年份 */
	@Excel(name = "年份", width = 15)
	@ApiModelProperty(value = "年份")
	private java.lang.Integer year;
	/** 经营科目编码 */
	@Excel(name = "经营科目编码", width = 15)
	@ApiModelProperty(value = "经营科目编码")
	private java.lang.String accountCode;
	/** 会计科目编码 */
	@Excel(name = "会计科目编码", width = 15)
	@ApiModelProperty(value = "会计科目编码")
	private java.lang.String ledgerCode;
	/** 模板类型 */
	@Excel(name = "模板类型", width = 15)
	@ApiModelProperty(value = "模板类型")
	private java.lang.Integer templateType;

	@ApiModelProperty(value = "占比_计算公式")
	private java.lang.String cronPro;

	@ApiModelProperty(value = "占比_百分比")
	private BigDecimal pro;

	@ApiModelProperty(value = "比例")
	private java.math.BigDecimal proportion;

	@ApiModelProperty(value = "是否按预定值均摊")
	private Boolean avg;

	/** 排序 */
	@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "排序")
	private java.lang.Integer sort;
	/** 计算公式 */
	@Excel(name = "计算公式", width = 15)
	@ApiModelProperty(value = "计算公式")
	private java.lang.String cron;

	/** 月份 */
	@Excel(name = "月份", width = 15)
	@ApiModelProperty(value = "月份")
	private java.lang.Integer month;
	/** 1号 */
	@Excel(name = "1号", width = 15)
	@ApiModelProperty(value = "1号")
	private java.math.BigDecimal day1;
	/** 2号 */
	@Excel(name = "2号", width = 15)
	@ApiModelProperty(value = "2号")
	private java.math.BigDecimal day2;
	/** 3号 */
	@Excel(name = "3号", width = 15)
	@ApiModelProperty(value = "3号")
	private java.math.BigDecimal day3;
	/** 4号 */
	@Excel(name = "4号", width = 15)
	@ApiModelProperty(value = "4号")
	private java.math.BigDecimal day4;
	/** 5号 */
	@Excel(name = "5号", width = 15)
	@ApiModelProperty(value = "5号")
	private java.math.BigDecimal day5;
	/** 6号 */
	@Excel(name = "6号", width = 15)
	@ApiModelProperty(value = "6号")
	private java.math.BigDecimal day6;
	/** 7号 */
	@Excel(name = "7号", width = 15)
	@ApiModelProperty(value = "7号")
	private java.math.BigDecimal day7;
	/** 8号 */
	@Excel(name = "8号", width = 15)
	@ApiModelProperty(value = "8号")
	private java.math.BigDecimal day8;
	/** 9号 */
	@Excel(name = "9号", width = 15)
	@ApiModelProperty(value = "9号")
	private java.math.BigDecimal day9;
	/** 10号 */
	@Excel(name = "10号", width = 15)
	@ApiModelProperty(value = "10号")
	private java.math.BigDecimal day10;
	/** 11号 */
	@Excel(name = "11号", width = 15)
	@ApiModelProperty(value = "11号")
	private java.math.BigDecimal day11;
	/** 12号 */
	@Excel(name = "12号", width = 15)
	@ApiModelProperty(value = "12号")
	private java.math.BigDecimal day12;
	/** 13号 */
	@Excel(name = "13号", width = 15)
	@ApiModelProperty(value = "13号")
	private java.math.BigDecimal day13;
	/** 14号 */
	@Excel(name = "14号", width = 15)
	@ApiModelProperty(value = "14号")
	private java.math.BigDecimal day14;
	/** 15号 */
	@Excel(name = "15号", width = 15)
	@ApiModelProperty(value = "15号")
	private java.math.BigDecimal day15;
	/** 16号 */
	@Excel(name = "16号", width = 15)
	@ApiModelProperty(value = "16号")
	private java.math.BigDecimal day16;
	/** 17号 */
	@Excel(name = "17号", width = 15)
	@ApiModelProperty(value = "17号")
	private java.math.BigDecimal day17;
	/** 18号 */
	@Excel(name = "18号", width = 15)
	@ApiModelProperty(value = "18号")
	private java.math.BigDecimal day18;
	/** 19号 */
	@Excel(name = "19号", width = 15)
	@ApiModelProperty(value = "19号")
	private java.math.BigDecimal day19;
	/** 20号 */
	@Excel(name = "20号", width = 15)
	@ApiModelProperty(value = "20号")
	private java.math.BigDecimal day20;
	/** 21号 */
	@Excel(name = "21号", width = 15)
	@ApiModelProperty(value = "21号")
	private java.math.BigDecimal day21;
	/** 22号 */
	@Excel(name = "22号", width = 15)
	@ApiModelProperty(value = "22号")
	private java.math.BigDecimal day22;
	/** 23号 */
	@Excel(name = "23号", width = 15)
	@ApiModelProperty(value = "23号")
	private java.math.BigDecimal day23;
	/** 24号 */
	@Excel(name = "24号", width = 15)
	@ApiModelProperty(value = "24号")
	private java.math.BigDecimal day24;
	/** 25号 */
	@Excel(name = "25号", width = 15)
	@ApiModelProperty(value = "25号")
	private java.math.BigDecimal day25;
	/** 26号 */
	@Excel(name = "26号", width = 15)
	@ApiModelProperty(value = "26号")
	private java.math.BigDecimal day26;
	/** 27号 */
	@Excel(name = "27号", width = 15)
	@ApiModelProperty(value = "27号")
	private java.math.BigDecimal day27;
	/** 28号 */
	@Excel(name = "28号", width = 15)
	@ApiModelProperty(value = "28号")
	private java.math.BigDecimal day28;
	/** 29号 */
	@Excel(name = "29号", width = 15)
	@ApiModelProperty(value = "29号")
	private java.math.BigDecimal day29;
	/** 30号 */
	@Excel(name = "30号", width = 15)
	@ApiModelProperty(value = "30号")
	private java.math.BigDecimal day30;
	/** 31号 */
	@Excel(name = "31号", width = 15)
	@ApiModelProperty(value = "31号")
	private java.math.BigDecimal day31;

	@ApiModelProperty(value = "实绩 按本月汇总")
	private java.math.BigDecimal actual;

	@ApiModelProperty(value = "预计 本月")
	private java.math.BigDecimal es;

	@ApiModelProperty(value = "差异")
	private java.math.BigDecimal diff;

	@ApiModelProperty(value = "达成率")
	private java.math.BigDecimal ach;

	@ApiModelProperty(value = "预计 下月")
	private java.math.BigDecimal esNext;

	@ApiModelProperty(value = "占比_百分比 预定")
	private BigDecimal proEs;

	@ApiModelProperty(value = "占比_百分比 预定 下月")
	private BigDecimal proEsNext;

	@ApiModelProperty(value = "备注")
	private java.lang.String remark;

	@ApiModelProperty(value = "模板类型 来源")
	private Integer templateSource;

    @ApiModelProperty(value = "计算公式 来源 和templateSource配合使用")
	private java.lang.String cronSource;

    @ApiModelProperty(value = "问号显示的内容")
	private java.lang.String tips;

    @ApiModelProperty(value = "父级编码")
	private java.lang.String parentCode;

	public EsAmountActualDayVO(EsAmountActual ea) {

		this.setData(ea);
	}

	public EsAmountActualDayVO() {

	}

	public void setData(EsAmountActual esAmountActual) {

		BeanUtils.copyProperties(esAmountActual, this);

	}

	public java.lang.String getProject() {
		return project;
	}

	public void setProject(java.lang.String project) {
		this.project = project;
	}

	public java.lang.String getCategory() {
		return category;
	}

	public void setCategory(java.lang.String category) {
		this.category = category;
	}

	public java.lang.String getAccount() {
		return account;
	}

	public void setAccount(java.lang.String account) {
		this.account = account;
	}

	public java.lang.String getDept() {
		return dept;
	}

	public void setDept(java.lang.String dept) {
		this.dept = dept;
	}

	public java.lang.String getDeptSun() {
		return deptSun;
	}

	public void setDeptSun(java.lang.String deptSun) {
		this.deptSun = deptSun;
	}

	public java.lang.Integer getYear() {
		return year;
	}

	public void setYear(java.lang.Integer year) {
		this.year = year;
	}

	public java.lang.String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(java.lang.String accountCode) {
		this.accountCode = accountCode;
	}

	public java.lang.String getLedgerCode() {
		return ledgerCode;
	}

	public void setLedgerCode(java.lang.String ledgerCode) {
		this.ledgerCode = ledgerCode;
	}

	public java.lang.Integer getTemplateType() {
		return templateType;
	}

	public void setTemplateType(java.lang.Integer templateType) {
		this.templateType = templateType;
	}

	public java.lang.String getCronPro() {
		return cronPro;
	}

	public void setCronPro(java.lang.String cronPro) {
		this.cronPro = cronPro;
	}

	public java.lang.Integer getSort() {
		return sort;
	}

	public void setSort(java.lang.Integer sort) {
		this.sort = sort;
	}

	public java.lang.String getCron() {
		return cron;
	}

	public void setCron(java.lang.String cron) {
		this.cron = cron;
	}

	public java.lang.Integer getMonth() {
		return month;
	}

	public void setMonth(java.lang.Integer month) {
		this.month = month;
	}

	public java.math.BigDecimal getDay1() {
		if(day1==null) {
			return BigDecimal.ZERO;
		}
		return day1;
	}

	public void setDay1(java.math.BigDecimal day1) {
		this.day1 = day1;
	}

	public java.math.BigDecimal getDay2() {
		if(day2==null) {
			return BigDecimal.ZERO;
		}
		return day2;
	}

	public void setDay2(java.math.BigDecimal day2) {
		this.day2 = day2;
	}

	public java.math.BigDecimal getDay3() {
		if(day3==null) {
			return BigDecimal.ZERO;
		}
		return day3;
	}

	public void setDay3(java.math.BigDecimal day3) {
		this.day3 = day3;
	}

	public java.math.BigDecimal getDay4() {
		if(day4==null) {
			return BigDecimal.ZERO;
		}
		return day4;
	}

	public void setDay4(java.math.BigDecimal day4) {
		this.day4 = day4;
	}

	public java.math.BigDecimal getDay5() {
		if(day5==null) {
			return BigDecimal.ZERO;
		}
		return day5;
	}

	public void setDay5(java.math.BigDecimal day5) {
		this.day5 = day5;
	}

	public java.math.BigDecimal getDay6() {
		if(day6==null) {
			return BigDecimal.ZERO;
		}
		return day6;
	}

	public void setDay6(java.math.BigDecimal day6) {
		this.day6 = day6;
	}

	public java.math.BigDecimal getDay7() {
		if(day7==null) {
			return BigDecimal.ZERO;
		}
		return day7;
	}

	public void setDay7(java.math.BigDecimal day7) {
		this.day7 = day7;
	}

	public java.math.BigDecimal getDay8() {
		if(day8==null) {
			return BigDecimal.ZERO;
		}
		return day8;
	}

	public void setDay8(java.math.BigDecimal day8) {
		this.day8 = day8;
	}

	public java.math.BigDecimal getDay9() {
		if(day9==null) {
			return BigDecimal.ZERO;
		}
		return day9;
	}

	public void setDay9(java.math.BigDecimal day9) {
		this.day9 = day9;
	}

	public java.math.BigDecimal getDay10() {
		if(day10==null) {
			return BigDecimal.ZERO;
		}
		return day10;
	}

	public void setDay10(java.math.BigDecimal day10) {
		this.day10 = day10;
	}

	public java.math.BigDecimal getDay11() {
		if(day11==null) {
			return BigDecimal.ZERO;
		}
		return day11;
	}

	public void setDay11(java.math.BigDecimal day11) {
		this.day11 = day11;
	}

	public java.math.BigDecimal getDay12() {
		if(day12==null) {
			return BigDecimal.ZERO;
		}
		return day12;
	}

	public void setDay12(java.math.BigDecimal day12) {
		this.day12 = day12;
	}

	public java.math.BigDecimal getDay13() {
		if(day13==null) {
			return BigDecimal.ZERO;
		}
		return day13;
	}

	public void setDay13(java.math.BigDecimal day13) {
		this.day13 = day13;
	}

	public java.math.BigDecimal getDay14() {
		if(day14==null) {
			return BigDecimal.ZERO;
		}
		return day14;
	}

	public void setDay14(java.math.BigDecimal day14) {
		this.day14 = day14;
	}

	public java.math.BigDecimal getDay15() {
		if(day15==null) {
			return BigDecimal.ZERO;
		}
		return day15;
	}

	public void setDay15(java.math.BigDecimal day15) {
		this.day15 = day15;
	}

	public java.math.BigDecimal getDay16() {
		if(day16==null) {
			return BigDecimal.ZERO;
		}
		return day16;
	}

	public void setDay16(java.math.BigDecimal day16) {
		this.day16 = day16;
	}

	public java.math.BigDecimal getDay17() {
		if(day17==null) {
			return BigDecimal.ZERO;
		}
		return day17;
	}

	public void setDay17(java.math.BigDecimal day17) {
		this.day17 = day17;
	}

	public java.math.BigDecimal getDay18() {
		if(day18==null) {
			return BigDecimal.ZERO;
		}
		return day18;
	}

	public void setDay18(java.math.BigDecimal day18) {
		this.day18 = day18;
	}

	public java.math.BigDecimal getDay19() {
		if(day19==null) {
			return BigDecimal.ZERO;
		}
		return day19;
	}

	public void setDay19(java.math.BigDecimal day19) {
		this.day19 = day19;
	}

	public java.math.BigDecimal getDay20() {
		if(day20==null) {
			return BigDecimal.ZERO;
		}
		return day20;
	}

	public void setDay20(java.math.BigDecimal day20) {
		this.day20 = day20;
	}

	public java.math.BigDecimal getDay21() {
		if(day21==null) {
			return BigDecimal.ZERO;
		}
		return day21;
	}

	public void setDay21(java.math.BigDecimal day21) {
		this.day21 = day21;
	}

	public java.math.BigDecimal getDay22() {
		if(day22==null) {
			return BigDecimal.ZERO;
		}
		return day22;
	}

	public void setDay22(java.math.BigDecimal day22) {
		this.day22 = day22;
	}

	public java.math.BigDecimal getDay23() {
		if(day23==null) {
			return BigDecimal.ZERO;
		}
		return day23;
	}

	public void setDay23(java.math.BigDecimal day23) {
		this.day23 = day23;
	}

	public java.math.BigDecimal getDay24() {
		if(day24==null) {
			return BigDecimal.ZERO;
		}
		return day24;
	}

	public void setDay24(java.math.BigDecimal day24) {
		this.day24 = day24;
	}

	public java.math.BigDecimal getDay25() {
		if(day25==null) {
			return BigDecimal.ZERO;
		}
		return day25;
	}

	public void setDay25(java.math.BigDecimal day25) {
		this.day25 = day25;
	}

	public java.math.BigDecimal getDay26() {
		if(day26==null) {
			return BigDecimal.ZERO;
		}
		return day26;
	}

	public void setDay26(java.math.BigDecimal day26) {
		this.day26 = day26;
	}

	public java.math.BigDecimal getDay27() {
		if(day27==null) {
			return BigDecimal.ZERO;
		}
		return day27;
	}

	public void setDay27(java.math.BigDecimal day27) {
		this.day27 = day27;
	}

	public java.math.BigDecimal getDay28() {
		if(day28==null) {
			return BigDecimal.ZERO;
		}
		return day28;
	}

	public void setDay28(java.math.BigDecimal day28) {
		this.day28 = day28;
	}

	public java.math.BigDecimal getDay29() {
		if(day29==null) {
			return BigDecimal.ZERO;
		}
		return day29;
	}

	public void setDay29(java.math.BigDecimal day29) {
		this.day29 = day29;
	}

	public java.math.BigDecimal getDay30() {
		if(day30==null) {
			return BigDecimal.ZERO;
		}
		return day30;
	}

	public void setDay30(java.math.BigDecimal day30) {
		this.day30 = day30;
	}

	public java.math.BigDecimal getDay31() {
		if(day31==null) {
			return BigDecimal.ZERO;
		}
		return day31;
	}

	public void setDay31(java.math.BigDecimal day31) {
		this.day31 = day31;
	}

	public java.math.BigDecimal getDay(int day) {
		String value = null;
		try {
			value = BeanUtilsBean2.getInstance().getProperty(this, "day" + day);
		} catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {

		}
		if(StringUtils.isEmpty(value)) {
			return BigDecimal.ZERO;
		}
		return new BigDecimal(value);
	}

	public java.math.BigDecimal getActual() {
		return getDay1().add(getDay2()).add(getDay3()).add(getDay4()).add(getDay5()).add(getDay6()).add(getDay7()).add(getDay8()).add(getDay9()).add(getDay10())
				.add(getDay11()).add(getDay12()).add(getDay13()).add(getDay14()).add(getDay15()).add(getDay16()).add(getDay17()).add(getDay18()).add(getDay19())
				.add(getDay20()).add(getDay21()).add(getDay22()).add(getDay23()).add(getDay24()).add(getDay25()).add(getDay26()).add(getDay27()).add(getDay28())
				.add(getDay29()).add(getDay30()).add(getDay31());
	}

	public void setActual(java.math.BigDecimal actual) {
		this.actual = actual;
	}

	public java.math.BigDecimal getEs() {
		if(es==null) {
			return BigDecimal.ZERO;
		}
		return es;
	}

	public void setEs(java.math.BigDecimal es) {
		this.es = es;
	}

	public BigDecimal getPro() {
		return pro;
	}

	public void setPro(BigDecimal pro) {
		this.pro = pro;
	}

	public BigDecimal getProEs() {
		return proEs;
	}

	public void setProEs(BigDecimal proEs) {
		this.proEs = proEs;
	}

	public java.math.BigDecimal getDiff() {
		return this.getActual().subtract(getEs());
	}

	public void setDiff(java.math.BigDecimal diff) {
		this.diff = diff;
	}

	public java.math.BigDecimal getAch() {
		if(this.es==null || this.es.doubleValue()==0) {
			return BigDecimal.ZERO;
		}
		return this.getActual().divide(this.es, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));//;
	}

	public void setAch(java.math.BigDecimal ach) {
		this.ach = ach;
	}

	public java.math.BigDecimal getEsNext() {
		if(esNext==null) {
			return BigDecimal.ZERO;
		}
		return esNext;
	}

	public void setEsNext(java.math.BigDecimal esNext) {
		this.esNext = esNext;
	}

	public BigDecimal getProEsNext() {
		if(proEsNext==null) {
			return BigDecimal.ZERO;
		}
		return proEsNext;
	}

	public void setProEsNext(BigDecimal proEsNext) {
		this.proEsNext = proEsNext;
	}

	public java.math.BigDecimal getProportion() {
		return proportion;
	}

	public void setProportion(java.math.BigDecimal proportion) {
		this.proportion = proportion;
	}

	public Boolean getAvg() {
		return avg;
	}

	public void setAvg(Boolean avg) {
		this.avg = avg;
	}

	public java.lang.String getRemark() {
		return remark;
	}

	public void setRemark(java.lang.String remark) {
		this.remark = remark;
	}

	public java.lang.String getCronSource() {
		return cronSource;
	}

	public void setCronSource(java.lang.String cronSource) {
		this.cronSource = cronSource;
	}

	public Integer getTemplateSource() {
		return templateSource;
	}

	public void setTemplateSource(Integer templateSource) {
		this.templateSource = templateSource;
	}

	public java.lang.String getTips() {
		return tips;
	}

	public void setTips(java.lang.String tips) {
		this.tips = tips;
	}

	public void setDay(int index, BigDecimal bigDecimal) {
		try {
			BeanUtilsBean2.getInstance().setProperty(this, "day" + index,
					bigDecimal);
		} catch (IllegalAccessException | InvocationTargetException e) {
		}
	}

	public java.lang.String getParentCode() {
		return parentCode;
	}

	public void setParentCode(java.lang.String parentCode) {
		this.parentCode = parentCode;
	}
}
