package com.huayun.modules.erp.oms.query;

import com.huayun.modules.erp.oms.constant.enums.CollectionStateEnum;
import com.huayun.modules.erp.oms.constant.enums.OrderStateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 订单列表表单
 * @date 2022/8/7 22:36
 **/
@Data
@ApiModel(value = "订单列表表单", description = "订单列表表单")
@Accessors(chain = true)
public class ListOrderQuery {

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customerId;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式id")
    private String payType;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private Set<String> orderIds;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private Set<String> orderNos;

    /**
     * 订单编号
     */
    private String orderNo;

    @ApiModelProperty(value = "客户负责人")
    private String supervisor;

    /**
     * 产品ID
     */
    private String productId;

    /**收款状态*/
    private List<CollectionStateEnum> collectionStates;

    /**订单无效状态*/
    private List<OrderStateEnum>  notInOrderStates;

}
