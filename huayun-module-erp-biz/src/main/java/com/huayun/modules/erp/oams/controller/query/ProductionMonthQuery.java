package com.huayun.modules.erp.oams.controller.query;

import com.huayun.modules.common.mybatis.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductionMonthQuery extends BaseQuery {
    @ApiModelProperty(value = "月份")
    private String month;

    @Excel(name = "产品编码", width = 15)
    @ApiModelProperty(value = "产品编码")
    private java.lang.String productCode;
    /**产品名称*/
    @Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
    private java.lang.String productName;

    @ApiModelProperty(value = "产品类别id")
    private String categoryId;
}
