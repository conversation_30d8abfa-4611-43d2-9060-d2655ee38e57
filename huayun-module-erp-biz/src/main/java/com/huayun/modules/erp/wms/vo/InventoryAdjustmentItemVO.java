package com.huayun.modules.erp.wms.vo;

import com.huayun.modules.erp.wms.entity.model.InventoryAdjustmentItemModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 调整批号信息
 *
 *<AUTHOR>
 *@date 2022/11/7 14:40
 **/
@Data
@NoArgsConstructor
public class InventoryAdjustmentItemVO {

    /**批号*/
    @ApiModelProperty(value = "批号")
    private String lotNo;

    /**供应商id*/
    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    /**供应商名称*/
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /**标签入库时间*/
    @ApiModelProperty(value = "标签入库时间")
    private Date receiptTime;

    /**货位编码*/
    @ApiModelProperty(value = "货位编码")
    private String goodsAllocationCode;

    /**调整前物料数量*/
    @ApiModelProperty(value = "调整前物料数量")
    private BigDecimal materialQuantity;

    /**调整数量*/
    @ApiModelProperty(value = "调整数量")
    private BigDecimal adjustmentQuantity;

    @ApiModelProperty(value = "标签单价")
    private BigDecimal salePrice;

    public InventoryAdjustmentItemVO(InventoryAdjustmentItemModel model) {
        init(model);
    }

    private void init(InventoryAdjustmentItemModel model) {
        BeanUtils.copyProperties(model, this);
    }
}
