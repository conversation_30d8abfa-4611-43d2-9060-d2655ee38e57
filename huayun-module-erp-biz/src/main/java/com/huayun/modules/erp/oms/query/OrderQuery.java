package com.huayun.modules.erp.oms.query;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huayun.modules.common.enums.YesOrNOEnum;
import com.huayun.modules.common.mybatis.BaseQuery;
import com.huayun.modules.erp.oms.constant.bus.OrderPropertyConstant;
import com.huayun.modules.erp.oms.constant.enums.CollectionStateEnum;
import com.huayun.modules.erp.oms.constant.enums.OrderCategoryEnum;
import com.huayun.modules.erp.oms.constant.enums.OrderStateEnum;
import com.huayun.modules.erp.oms.entity.Order;
import com.huayun.modules.erp.pdm.constant.enums.product.ProductTypeEnum;
import com.huayun.modules.erp.wms.constant.enums.material.MaterialPropertyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description 订单列表表单
 * @date 2022/8/7 22:36
 **/
@Data
@ApiModel(value = "订单列表表单", description = "订单列表表单")
public class OrderQuery extends BaseQuery {

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customerId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 客户负责人
     */
    @ApiModelProperty(value = "客户负责人")
    private String supervisor;



    public void updateSupervisor(String supervisor) {
       this.supervisor = supervisor;
    }

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号列表")
    private List<String> orderNoList;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;
    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 交货开始日期
     */
    @ApiModelProperty(value = "交货开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDeliveryDate;

    /**
     * 交货结束日期
     */
    @ApiModelProperty(value = "交货结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDeliveryDate;

    /**
     * 是否有合同
     */
    @ApiModelProperty(value = "是否有合同")
    private Boolean hasContract;

    /**
     * 排除取消的
     */
    @ApiModelProperty(value = "排除取消的")
    private Boolean excludeCancel;

    /**
     * 是否还可以请款订单
     */
    @ApiModelProperty(value = "是否还可以请款订单")
    private Boolean isAppliedOrder;

    /**
     * 订单属性(字典：OMS_ORDER_PROPERTY)
     */
    @ApiModelProperty("订单属性(字典：OMS_ORDER_PROPERTY)")
    private String property;

    /**
     * 订单状态(枚举)
     */
    @ApiModelProperty("订单状态(枚举)")
    private OrderStateEnum state;

    /**
     * 订单状态(枚举)
     */
    @ApiModelProperty("订单状态(枚举)")
    private Set<OrderStateEnum> states;

    @ApiModelProperty("排除状态（枚举）")
    private Set<OrderStateEnum> excludeStates;

    /**
     * 订单类型
     */
    private OrderCategoryEnum type;

    /**
     * 多个订单类型
     */
    private Set<OrderCategoryEnum> types;


    /**仅查已完成入库*/
    @ApiModelProperty(value = "仅查已完成入库")
    private Boolean onlyStorage;

    /**
     * 创建开始日期
     */
    @ApiModelProperty(value = "创建开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;

    /**
     * 创建结束日期
     */
    @ApiModelProperty(value = "创建结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;

    @ApiModelProperty(value = "创建开始日期 精确到天")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startCreateDate;

    /**
     * 创建结束日期
     */
    @ApiModelProperty(value = "创建结束日期  精确到天")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endCreateDate;

    /**收款状态*/
    @ApiModelProperty("收款状态(枚举)")
	private Set<CollectionStateEnum> collectionStates;

    @ApiModelProperty("是否寄售")
    private Boolean isConsignment;

    /**
     * 业务部负责人
     */
    @ApiModelProperty(hidden = true,readOnly = true)
    private String deptId;

    /**
     * 业务部负责人
     */
    @ApiModelProperty(hidden = true,readOnly = true)
    private Collection<String> deptIds;


    @ApiModelProperty("排序")
    private String orderBy;

    @ApiModelProperty("BOM号")
    private String bomSn;

    @ApiModelProperty("是否根据显示在计划中标记过滤")
    private Boolean filterShowInPlan;
    
    @ApiModelProperty("过滤已申请寄售调拨")
    private Boolean filterConsignmentTransfer;

    private String productInfo;
    
    private Collection<String> customerNos;
    private Collection<String> productCodes;

    /** 规格 */
    @ApiModelProperty(value = "规格")
    private java.lang.String specification;

    /** 型号 */
    @ApiModelProperty(value = "型号")
    private java.lang.String model;

    /**
     * 列表分页查询条件
     * 方法提取到这里
     * service 类简单一点
     * @return
     */
    public QueryWrapper<Order> toPageQueryWrapper() {
        OrderQuery order = this;
        QueryWrapper<Order> queryWrapper = new QueryWrapper<Order>()
                .eq("o.deleted", YesOrNOEnum.NO.getValue())
                .eq(StringUtils.isNotEmpty(order.getCustomerId()), "o.customer_id", order.getCustomerId())
                .in(ObjectUtils.isNotEmpty(order.getCustomerNos()), "customer.customer_no", order.getCustomerNos())
                .in(ObjectUtils.isNotEmpty(order.getProductCodes()), "o.product_code", order.getProductCode())

                .eq(!OrderPropertyConstant.INTERIOR.equals(order.getProperty()) && StringUtils.isNotEmpty(order.getDeptId()), "customer.dept_id", order.getDeptId())

                .and(!CollectionUtils.isEmpty(order.getDeptIds()),q2 -> q2.in("customer.dept_id", order.getDeptIds()).or().isNull("customer.dept_id"))
                .apply(StringUtils.isNotEmpty(order.getCustomerName()), "(customer.customer_name like CONCAT('%',{0},'%') or customer.customer_no like CONCAT('%',{0},'%'))", order.getCustomerName())

                .like(StringUtils.isNotEmpty(order.getOrderNo()), "o.order_no", order.getOrderNo())
                .in(ObjectUtil.isNotEmpty(order.getOrderNoList()), "o.order_no", order.getOrderNoList())
//                .like(StringUtils.isNotEmpty(order.getContractNo()), "contract.contract_no", order.getContractNo())
                .eq(StringUtils.isNotEmpty(order.getProductId()), "o.product_id", order.getProductId())
                .like(StringUtils.isNotEmpty(order.getProductCode()), "o.product_code", order.getProductCode())
                // 产品名称和产品编码 能在一个搜索框内 模糊搜索；http://192.168.1.22/task-view-1826.html?tid=5x4nogqq
                .apply(StringUtils.isNotEmpty(order.getProductName()), "(o.product_code like CONCAT('%',{0},'%') or o.product_name like CONCAT('%',{0},'%'))", order.getProductName())
                .eq(StringUtils.isNotEmpty(order.getCreateBy()), "o.create_by", order.getCreateBy())
                .eq(StringUtils.isNotEmpty(order.getProperty()), "o.property", order.getProperty())
                .eq(StringUtils.isNotEmpty(order.getContractId()), "contract.id", order.getContractId())
                // 是否根据显示在计划的标记过滤
                .eq(Boolean.TRUE.equals(filterShowInPlan), "o.show_in_plan", Boolean.TRUE)
                .isNotNull(ObjectUtils.isNotEmpty(order.getHasContract()), "contract.id")
                .notIn(Boolean.TRUE.equals(order.getExcludeCancel()), "o.state", OrderStateEnum.CANCEL,OrderStateEnum.DRAFT,OrderStateEnum.SUBMITTED, OrderStateEnum.TERMINATE)
                .in(!CollectionUtils.isEmpty(order.getStates()), "o.state", order.getStates())
                .notIn(!CollectionUtils.isEmpty(order.getExcludeStates()), "o.state", order.getExcludeStates())
                .in(!CollectionUtils.isEmpty(order.getTypes()), "o.type", order.getTypes())
                .in(!CollectionUtils.isEmpty(order.getCollectionStates()), "o.collection_state", order.getCollectionStates())
                .apply(Boolean.TRUE.equals(order.getIsAppliedOrder()), "o.amount > o.applied_amount")
                .like(StringUtils.isNotEmpty(order.getBomSn()), "o.bom_sn", order.getBomSn());

        if (StringUtils.isNotEmpty(order.getContractNo())) {
            // 客户订单号，查询订单号、明细号，不然没有订单的查不到
            queryWrapper.and(orderQueryWrapper -> orderQueryWrapper
                    .like("contract.contract_no", order.getContractNo())
                    .or()
                    .like("o.order_no", order.getContractNo()));
        }
        if (ObjectUtil.isNotEmpty(order.getProperty())){
            if (OrderPropertyConstant.INTERIOR.equals(order.getProperty())){
                queryWrapper.eq(StringUtils.isNotEmpty(order.getSupervisor()),"o.create_by", order.getSupervisor());
            }else {
                queryWrapper.apply(StringUtils.isNotEmpty(order.getSupervisor()), "json_contains(contract.supervisor, CONCAT( '\"' ,'" + order.getSupervisor() + "' , '\"' )) ");
            }
        }
        if(ObjectUtils.isNotEmpty(order.getOnlyStorage()) && order.getOnlyStorage()) {
            queryWrapper.apply("o.storage_quantity > 0");
        }

        if (order.getTypes() != null) {
            queryWrapper.in("o.type", order.getTypes());
        } else {
            queryWrapper
                    .eq(order.getType() != null, "o.type", order.getType());
        }

        //订单状态查询
        if (ObjectUtil.isNotEmpty(order.getState())) {
            queryWrapper.eq("o.state", order.getState());
        }

        //交货时间查询
        if (ObjectUtils.isNotEmpty(order.getStartDeliveryDate())
                && ObjectUtils.isNotEmpty(order.getEndDeliveryDate())){
            //交货开始和结束时间都不为空就添加交货时间条件查询；DateUtil.beginOfDay获取一天的开始、DateUtil.endOfDay获取一天的结束
            queryWrapper.between("o.delivery_date",
                    DateUtil.beginOfDay(order.getStartDeliveryDate()),
                    DateUtil.endOfDay(order.getEndDeliveryDate()));
        }
        if (ObjectUtil.isAllNotEmpty(order.getStartCreateTime(),order.getEndCreateTime())){
            queryWrapper.between("o.create_time",
                    DateUtil.beginOfDay(order.getStartCreateTime()),
                    DateUtil.endOfDay(order.getEndCreateTime()));
        }
        if (ObjectUtil.isAllNotEmpty(order.getStartCreateDate(),order.getEndCreateDate())){
            queryWrapper.between("o.create_time",
                    DateUtil.beginOfDay(order.getStartCreateDate()),
                    DateUtil.endOfDay(order.getEndCreateDate()));
        }

        // 计划生产的时候，只返回成品、半成品、包装成品
        if (OrderStateEnum.PLAN.equals(getState()) && getTypes() != null && getTypes().contains(OrderCategoryEnum.PRODUCTION_ORDER) && getTypes().contains(OrderCategoryEnum.SAMPLE)) {
            queryWrapper.in("o.product_type", MaterialPropertyEnum.FINISHED, MaterialPropertyEnum.SEMI_FINISHED, MaterialPropertyEnum.PACKAGED_PRODUCT, ProductTypeEnum.PRODUCT.getValue(), ProductTypeEnum.SEMI_PRODUCT.getValue(), ProductTypeEnum.PACKAGED_PRODUCT.getValue());
        }
        if (StringUtils.isNotEmpty(getOrderBy())) {
            // 自定义排序
            String[] sorts = getOrderBy().split("_");
            if (sorts.length == 2) {
                Map<String, String> map = new HashMap<>();
                map.put("deliveryDate", "o.delivery_date");
                String key = map.get(sorts[0]);
                if (key != null) {
                	queryWrapper.orderBy(true, sorts[1].equals("asc"), key);
                	queryWrapper.orderByAsc("o.id");
                }
            }
        } else {
        	queryWrapper.orderByDesc("o.create_time", "o.id");
        }
        return queryWrapper;
    }

    /**
     * 发货列表分页查询条件
     * 方法提取到这里
     * service 类简单一点
     * @return
     */
    public QueryWrapper<Order> toQueryDeliveryPageWrapper(String... orderBy) {
        OrderQuery order = this;
        QueryWrapper<Order> wrapper = new QueryWrapper<Order>()
                .eq("o.deleted", YesOrNOEnum.NO.getValue())
                .eq("o.is_consignment", Boolean.TRUE.equals(order.isConsignment))
                .apply(Boolean.TRUE.equals(order.filterConsignmentTransfer), "o.consignment_transfer_quantity < o.total_quantity")
                .apply(Boolean.FALSE.equals(order.filterConsignmentTransfer), "o.consignment_transfer_quantity > 0")
                .eq("o.property", OrderPropertyConstant.EXTERNAL)
                .eq(StringUtils.isNotEmpty(order.customerId), "o.customer_id", order.customerId)
                .in(order.getTypes() != null, "o.type", order.getTypes())
                .like(StringUtils.isNotEmpty(order.getOrderNo()), "o.order_no", order.getOrderNo())
                .like(StringUtils.isNotEmpty(order.getContractNo()), "contract.contract_no", order.getContractNo())
                .like(StringUtils.isNotEmpty(order.getCustomerName()), "customer.customer_name", order.getCustomerName())
                // 产品名称跟产品编码模糊查询
                .apply(StringUtils.isNotEmpty(order.getProductName()), "(o.product_code like CONCAT('%',{0},'%') or o.product_name like CONCAT('%',{0},'%'))", order.getProductName())
                .between(order.getStartDeliveryDate() != null && order.getEndDeliveryDate() != null, "o.delivery_date", order.getStartDeliveryDate(), order.getEndDeliveryDate())
                .apply("o.total_quantity > o.delivery_quantity")
                .notIn("o.state",
                        OrderStateEnum.DRAFT,
                        OrderStateEnum.SUBMITTED,
                        OrderStateEnum.TERMINATE,
                        OrderStateEnum.COMPLETED,
                        OrderStateEnum.CANCEL)
                .like(StringUtils.isNotEmpty(order.getSpecification()), "o.product_specification", order.getSpecification())
                .like(StringUtils.isNotEmpty(order.getModel()), "o.product_model", order.getModel())
                .like(StringUtils.isNotEmpty(order.getSupervisor()), "contract.supervisor", order.getSupervisor());
                // 可以不排序或者自定义排序，默认按时间、ID排序
//                .orderByDesc(orderBy != null,"o.create_time", "o.id");
        boolean customSort = false;
        if (StringUtils.isNotEmpty(getOrderBy()) && orderBy != null) {
            //默认按交付日期升序(目前是这样)+有可用库存的记录显示在最前面，可用库存为0的记录往后按交付日期升序排列 task:4498
            if ("default_delivery".equals(getOrderBy()) ){
                wrapper.orderByAsc(" CASE" +
                        "        WHEN inventory.available_inventory > 0 THEN 0 " +
                        "        WHEN inventory.available_inventory IS NULL OR inventory.available_inventory <= 0 THEN 1" +
                        "        ELSE 2" +
                        "    END");
                wrapper.orderByAsc("o.delivery_date");
                wrapper.orderByAsc("o.id");

            }else {
                // 自定义排序
                String[] sorts = getOrderBy().split("_");
                if (sorts.length == 2) {
                    Map<String, String> map = new HashMap<>();
                    map.put("deliveryDate", "o.delivery_date");
                    map.put("createTime", "o.create_time");
                    String key = map.get(sorts[0]);
                    if (key != null) {
                        wrapper.orderBy(true, sorts[1].equals("asc"), key);
                        wrapper.orderByAsc("o.id");

                    }
                }
            }
            customSort = true;
        }
        if (!customSort) {
            // 默认排序
            wrapper.orderByDesc(orderBy != null,"o.create_time", "o.id");
        }

        return wrapper;
    }

    public QueryWrapper<Order> toQueryReturnPageWrapper() {
        OrderQuery query = this;
        QueryWrapper<Order> wrapper = new QueryWrapper<Order>()
                .eq("o.deleted", YesOrNOEnum.NO.getValue())
                .in("o.state",
                        OrderStateEnum.PLAN,
                        OrderStateEnum.PENDING_DELIVERY,
                        OrderStateEnum.TO_DO_SCHEDULE,
                        OrderStateEnum.PENDING_PRODUCTION,
                        OrderStateEnum.PENDING_REJECTED,
                        OrderStateEnum.INTO_THE_WAREHOUSE,
                        OrderStateEnum.TO_DO_DELIVERY,
                        OrderStateEnum.TERMINATE,
                        OrderStateEnum.COMPLETED)
                .gt("o.delivery_quantity", 0)
                .eq("o.property", OrderPropertyConstant.EXTERNAL)
                .eq(query.getIsConsignment() != null, "o.is_consignment", query.getIsConsignment())
                .eq(StringUtils.isNotEmpty(query.getCustomerId()), "o.customer_id", query.getCustomerId())
                .like(StringUtils.isNotEmpty(query.getOrderNo()), "o.order_no", query.getOrderNo())
                .like(StringUtils.isNotEmpty(query.getContractNo()), "contract.contract_no", query.getContractNo())
//                .like(StringUtils.isNotEmpty(query.getProductName()), "o.product_name", query.getProductName())
                .apply(StringUtils.isNotEmpty(query.getProductName()), "(o.product_name like {0} or o.product_code like {0})", "%" + query.getProductName() + "%")
                .eq(StringUtils.isNotEmpty(query.getProductId()), "o.product_id", query.getProductId())
                .orderByDesc("o.create_time", "o.id");
        return wrapper;
    }
}
