package com.huayun.modules.erp.oms.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huayun.modules.common.enums.YesOrNOEnum;
import com.huayun.modules.erp.oms.dao.IOrderReturnItemDao;
import com.huayun.modules.erp.oms.entity.OrderReturnItem;
import com.huayun.modules.erp.oms.entity.model.OrderReturnItemModel;
import com.huayun.modules.erp.oms.entity.model.ProductDeliveryQuantityModel;
import com.huayun.modules.erp.oms.mapper.OrderReturnItemMapper;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * 订单退货物料清单管理
 *
 * <AUTHOR>
 * @date 2023/3/7 14:53
 **/
@Component
public class OrderReturnItemDaoImpl extends ServiceImpl<OrderReturnItemMapper, OrderReturnItem> implements IOrderReturnItemDao {

    @Override
    public List<OrderReturnItem> findListByReturnId(String id) {
        LambdaQueryWrapper<OrderReturnItem> wrapper = new LambdaQueryWrapper<OrderReturnItem>()
                .eq(OrderReturnItem::getDeleted, YesOrNOEnum.NO.getValue())
                .eq(OrderReturnItem::getReturnId, id);
        return this.list(wrapper);
    }

    @Override
    public List<OrderReturnItemModel> findList(QueryWrapper<OrderReturnItemModel> wrapper) {
        return this.baseMapper.findList(wrapper);
    }

    @Override
    public List<OrderReturnItem> findPendingQcReturnQuantityByOrderNos(Collection<String> orderNos) {
        return this.baseMapper.findPendingQcReturnQuantityByOrderNos(orderNos);
    }

    @Override
    public List<ProductDeliveryQuantityModel> findProductReturnQuantity(QueryWrapper<OrderReturnItem> wrapper) {
        return this.baseMapper.findProductReturnQuantity(wrapper);
    }
}
