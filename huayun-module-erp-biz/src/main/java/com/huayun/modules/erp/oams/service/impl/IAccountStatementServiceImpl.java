package com.huayun.modules.erp.oams.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huayun.modules.common.approval.constant.enums.ApprovalStateEnum;
import com.huayun.modules.common.approval.entity.Approval;
import com.huayun.modules.common.approval.service.ApprovalService;
import com.huayun.modules.common.exception.BadRequestException;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.common.util.CodeGenerator;
import com.huayun.modules.erp.constant.enums.CurrencyEnum;
import com.huayun.modules.erp.constant.enums.StatementTypeEnum;
import com.huayun.modules.erp.oams.constant.OamsConstant;
import com.huayun.modules.erp.oams.constant.enums.PaymentStateEnum;
import com.huayun.modules.erp.oams.constant.enums.StatementDetailTypeEnum;
import com.huayun.modules.erp.oams.constant.enums.StatementPayStateEnum;
import com.huayun.modules.erp.oams.controller.form.AccountStatementChangeForm;
import com.huayun.modules.erp.oams.controller.form.AccountStatementEndPayForm;
import com.huayun.modules.erp.oams.controller.form.AccountStatementForm;
import com.huayun.modules.erp.oams.dao.PaymentDAO;
import com.huayun.modules.erp.oams.entity.Currency;
import com.huayun.modules.erp.oams.entity.*;
import com.huayun.modules.erp.oams.mapper.AccountStatementMapper;
import com.huayun.modules.erp.oams.service.*;
import com.huayun.modules.erp.oams.util.AmountCalculateUtil;
import com.huayun.modules.erp.pms.entity.*;
import com.huayun.modules.erp.pms.entity.model.StatementOrderModel;
import com.huayun.modules.erp.pms.entity.model.StatementOrderProductModel;
import com.huayun.modules.erp.pms.entity.model.StatementOrderReceiptModel;
import com.huayun.modules.erp.pms.form.CreatePurchaseStatementForm;
import com.huayun.modules.erp.pms.query.AccountStatementQuery;
import com.huayun.modules.erp.pms.service.*;
import com.huayun.modules.erp.wms.dao.IReceiptDao;
import com.huayun.modules.erp.wms.dao.IReceiptItemDao;
import com.huayun.modules.erp.wms.entity.InventoryLog;
import com.huayun.modules.erp.wms.entity.Material;
import com.huayun.modules.erp.wms.entity.Receipt;
import com.huayun.modules.erp.wms.entity.ReceiptItem;
import com.huayun.modules.erp.wms.service.material.IInventoryLogService;
import com.huayun.modules.erp.wms.service.material.IMaterialService;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Collection;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* @description 针对表【oams_account_statement(对账单)】的数据库操作Service实现
* @createDate 2023-05-12 14:57:59
*/
@Service
public class IAccountStatementServiceImpl extends ServiceImpl<AccountStatementMapper, AccountStatement> implements IAccountStatementService {
    @Resource
    private CodeGenerator codeGenerator;
    @Resource
    private IStatementOrderService IStatementOrderService;
    @Resource
    private IStatementOrderReceiptService receiptService;
    @Resource
    private IInventoryLogService inventoryLogService;
    @Resource
    private IAccountStatementDetailService statementDetailService;

    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private ISupplierService supplierService;
    @Resource
    private ISupplierPaymentDaysService supplierPaymentDaysService;
    @Resource
    private IOutsourcingOrderService outsourcingOrderService;
    @Resource
    private ICurrencyService currencyService;
    @Resource
    private AccountStatementMapper accountStatementMapper;
    @Resource
    private IReceiptItemDao receiptItemDao;
    @Resource
    private IReceiptDao receiptDao;
    @Resource
    private IOutsourcingOrderTranferService outsourcingOrderTranferService;
    @Resource
    private IMaterialService materialService;
    @Resource
    private IPmsPaymentService pmsPaymentService;
    @Resource
    private PaymentDAO paymentDAO;

    @Resource
    private ApprovalService approvalService;

    @Resource
    private IPaymentService paymentService;


    @Override
    @Deprecated
    public AccountStatement create(CreatePurchaseStatementForm form) {
        List<StatementOrder> statementOrderList = new ArrayList<>();
        List<StatementOrderReceipt> statementOrderReceiptList = new ArrayList<>();

        //插入对账单表
        int orderAmount = 0; //订单数量
        BigDecimal totalAmount = BigDecimal.ZERO; //订单金额
        BigDecimal requestAmount = BigDecimal.ZERO; //应付金额 生成对账单表里的入库金额合计数
        BigDecimal paidAmount = BigDecimal.ZERO; //实付金额 请款单申请付款后财务实付金额，每次请款单付款后累加到实付金额里
        BigDecimal unpaidAmount; //未付金额 应付金额-实付金额
        String  payState = StatementPayStateEnum.SUBMITTED.getValue(); //审批中状态

        //插入对账订单表和对账订单入库信息表
        List<StatementOrderModel> statementOrderModelList = form.getStatementOrderModelList();
        for (StatementOrderModel statementOrderModel : statementOrderModelList) {
            orderAmount++;
            List<StatementOrderProductModel> productModels = statementOrderModel.getStatementOrderProductModels();
            for (StatementOrderProductModel productModel : productModels) {
                totalAmount = totalAmount.add(productModel.getTotalTaxAmount());
                StatementOrder statementOrder = new StatementOrder();
                BeanUtils.copyProperties(productModel,statementOrder);
                BeanUtils.copyProperties(statementOrderModel,statementOrder);
                statementOrderList.add(statementOrder);
                requestAmount = requestAmount.subtract(productModel.getReturnProductAmount());
                List<StatementOrderReceiptModel> orderReceipts = productModel.getStatementOrderReceiptModels();
                for (StatementOrderReceiptModel orderReceipt : orderReceipts) {
                    requestAmount = requestAmount.add(orderReceipt.getReceiptAmount());
                    StatementOrderReceipt statementOrderReceipt = new StatementOrderReceipt();
                    BeanUtils.copyProperties(orderReceipt,statementOrderReceipt);
                    statementOrderReceiptList.add(statementOrderReceipt);
                }
            }
        }
        //未请款 未付金额等于应付金额
        unpaidAmount = requestAmount;
        AccountStatement accountStatement = new AccountStatement();
        accountStatement.setStatementNo(codeGenerator.next("AS"))
                .setPayMethodName(form.getPayMethodName());
        this.save(accountStatement);
        IStatementOrderService.saveBatch(statementOrderList);
        receiptService.saveBatch(statementOrderReceiptList);
        return this.getById(accountStatement.getId());
    }



    @Override
    public AccountStatement createAccountStatement(AccountStatementForm accountStatementForm) {
        List<String> inventoryLogIds = accountStatementForm.getInventoryLogIds();
        BizAssert.isNotEmpty(inventoryLogIds, "未选择入库记录");

        //幂等性校验
        BizAssert.isEmpty(statementDetailService.getByInventoryLogIds(inventoryLogIds),"存在入库记录已生成过对账单");

        //获取库存记录
        List<InventoryLog> inventoryLogList = inventoryLogService
                .findList(new LambdaQueryWrapper<InventoryLog>().in(InventoryLog::getId, inventoryLogIds))
                .stream().peek(item ->{
                    if (item.getNegate()){
                        item.setQuantity(item.getQuantity().negate());
                    }
                }).collect(Collectors.toList());
        //如果在inventoryLogIds 但是没查出流水,则认为直接是入库单明细
        Set<String> logIds = inventoryLogList.stream().map(InventoryLog::getId).collect(Collectors.toSet());
        Set<String> receiptItemIds = new HashSet<>();
        for(String id : inventoryLogIds){
            if(!logIds.contains(id)){
                receiptItemIds.add(id);
            }
        }

        // 采购/委外入库明细
        Set<String> receiptIds = inventoryLogList.stream().map(InventoryLog::getTaskId).collect(Collectors.toSet());
        List<ReceiptItem> receiptItems;
        receiptIds.add("none");
        receiptItems = receiptItemDao.list(new LambdaQueryWrapper<ReceiptItem>().in(ReceiptItem::getReceiptId, receiptIds));
        Set<String> orderNos = inventoryLogList.stream().map(InventoryLog::getBizCode).collect(Collectors.toSet());
        List<Material> materialReceiptList = new ArrayList<>();
        List<String> materialIdReceipts = new ArrayList<>();
        if(!receiptItemIds.isEmpty()){
            List<ReceiptItem> receiptItemImports = receiptItemDao.listByIds(receiptItemIds);
            Set<String> materialIds = receiptItemImports.stream().map(ReceiptItem::getMaterialId).collect(Collectors.toSet());
            materialIdReceipts.addAll(materialIds);
            materialReceiptList = materialService.getMaterialListByIds(materialIds);
            Map<String, Material> materialMap = materialReceiptList.stream().collect(Collectors.toMap(Material::getId, Function.identity()));
            if(!receiptItemImports.isEmpty()){

                receiptItems.addAll(receiptItemImports) ;
                List<Receipt> receiptImports = receiptDao.listByIds(receiptItemImports.stream().map(ReceiptItem::getReceiptId).collect(Collectors.toList()));
                Map<String, Receipt> receiptMap = receiptImports.stream().collect(Collectors.toMap(Receipt::getId, Function.identity()));
                for (Receipt receiptImport : receiptImports) {
                    orderNos.add(receiptImport.getRelationCode());
                }

                receiptItemImports.forEach(receiptItemImport -> {
                    if(receiptItemImport.getStatement()){
                        throw new BadRequestException("选择的委外入库单有已经对账的!");
                    }
                    receiptItemImport.setStatement(true);
                    receiptItemImport.setStatementQuantity(receiptItemImport.getDeliveryQuantity());
                    Receipt receiptData = receiptMap.get(receiptItemImport.getReceiptId());
                    InventoryLog inventoryLog = new InventoryLog();
                    inventoryLog.setBizCode(receiptData.getRelationCode())
                            .setTaskId(receiptData.getId())
                            .setTaskCode(receiptData.getCode())
                            .setType("OUTSOURCE_RECEIPT")
                            .setMaterialId(receiptItemImport.getMaterialId())
                            .setMaterialCode(receiptItemImport.getMaterialCode())
                            .setMaterialName(materialMap.get(receiptItemImport.getMaterialId()).getName())
                            .setMaterialUnit(materialMap.get(receiptItemImport.getMaterialId()).getUnit())
                            .setMaterialProperty(materialMap.get(receiptItemImport.getMaterialId()).getProperty().getValue())
                            .setMaterialSpecification(materialMap.get(receiptItemImport.getMaterialId()).getSpecification())
                            .setQuantity(receiptItemImport.getDeliveryQuantity())
                            .setUnitPrice(receiptItemImport.getPrice())

                            .setTotal(receiptItemImport.getDeliveryQuantity().multiply(receiptItemImport.getPrice()));
                    inventoryLog.setId(receiptItemImport.getId());
                    inventoryLog.setNegate(false);
                    inventoryLog.setCreateTime(receiptData.getReceiptTime());
                    inventoryLogList.add(inventoryLog);
                });
                receiptItemDao.updateBatchById(receiptItemImports);
            }
        }

        AccountStatement accountStatement = null;
        if (accountStatementForm.getType().equals(StatementTypeEnum.PURCHASE)) {
            //采购订单信息 为了获取采购订单的部分信息
            List<PurchaseOrder> orderList = purchaseOrderService.getPurchaseOrderByOrderNos(orderNos);
            //获取供应商及支付方式信息
            Set<String> supplierIds = orderList.stream().map(PurchaseOrder::getSupplier).collect(Collectors.toSet());
            BizAssert.isTrue(supplierIds.size() == 1, "只能选择同一个采购商的订单");
         /*   Set<String> payMethodIds = orderList.stream().map(PurchaseOrder::getPaymentDays).collect(Collectors.toSet());
            BizAssert.isTrue(payMethodIds.size() == 1, "只能选择同一个支付方式的订单");*/
            Set<CurrencyEnum> currencySet = orderList.stream().map(PurchaseOrder::getCurrency).collect(Collectors.toSet());
            BizAssert.isTrue(currencySet.size() == 1, "只能选择同一种货币的订单");
            Supplier supplier = supplierService.getById(orderList.get(0).getSupplier());
            SupplierPaymentDays paymentDays = supplierPaymentDaysService.getById(orderList.get(0).getPaymentDays());
            Currency currency = currencyService.getByCode(orderList.get(0).getCurrency().getValue());
            BizAssert.isNotNull(supplier, "供应商不存在");
            BizAssert.isNotNull(paymentDays, "支付方式不存在");
            BizAssert.isNotNull(currency, "币种不存在");
            BigDecimal exchangeRate = currency.getExchangeRate();
            accountStatement = new AccountStatement(accountStatementForm, inventoryLogList, orderList, paymentDays, supplier,exchangeRate,receiptItems);
            accountStatement.setStatementNo(codeGenerator.next("PODZ"));

        }else {
            //流转
            List<OutsourcingOrderTranfer> outsourcingOrderTranfers = outsourcingOrderTranferService.listByIds(inventoryLogIds);
            orderNos.addAll(outsourcingOrderTranfers.stream().map(OutsourcingOrderTranfer::getOrderNo).collect(Collectors.toList()));
            List<OutsourcingOrder> orderList = outsourcingOrderService.findOutsourcingOrderList(orderNos) ;
            Set<String> supplierIds = orderList.stream().map(OutsourcingOrder::getSupplierId).collect(Collectors.toSet());
            BizAssert.isTrue(supplierIds.size() == 1, "只能合并同一个委外商的订单");
            Set<String> payMethodIds = orderList.stream().map(OutsourcingOrder::getPayMethod).collect(Collectors.toSet());
            //BizAssert.isTrue(payMethodIds.size() == 1, "只能合并同一个支付方式的订单");
            Set<CurrencyEnum> currencySet = orderList.stream().map(OutsourcingOrder::getCurrency).collect(Collectors.toSet());
            BizAssert.isTrue(currencySet.size() == 1, "只能选择同一种货币的订单");
            SupplierPaymentDays paymentDays = supplierPaymentDaysService.getById(orderList.get(0).getPayMethod());
            Supplier supplier = supplierService.getById(supplierIds.iterator().next());
            Currency currency = currencyService.getByCode(orderList.get(0).getCurrency().getValue());
            BigDecimal exchangeRate = currency.getExchangeRate();

            List<String> materialIds = outsourcingOrderTranfers.stream().map(OutsourcingOrderTranfer::getProductId).collect(Collectors.toList());
            //materialIds.addAll(materialIdReceipts);
            List<Material> materialList = materialService.getMaterialListByIds(materialIds);

            accountStatement = new AccountStatement(accountStatementForm, inventoryLogList, paymentDays, orderList, supplier,exchangeRate,receiptItems,outsourcingOrderTranfers,materialList);
            accountStatement.setStatementNo(codeGenerator.next("POPDZ"));

        }
        BizAssert.isTrue(this.save(accountStatement),"保存失败");

        for (StatementDetail statementDetail : accountStatement.getDetailList()) {
            statementDetail.setStatementId(accountStatement.getId());
            statementDetail.setStatementNo(accountStatement.getStatementNo());
        }
        statementDetailService.saveBatch(accountStatement.getDetailList());
        //对账单生成修改库存记录
        inventoryLogService.updateFinanceCode(inventoryLogIds,accountStatement.getId());

        //对账单生成修改入库明细金额
        updateReceiptItemAmount(accountStatement);


        return accountStatement;
    }

    /**
     * 更新发货明细的单价金额
     * @param statement
     */
    private void updateReceiptItemAmount(AccountStatement statement) {
        Map<String, StatementDetail> detailMap = statement.getDetailList().stream().filter(item -> ObjectUtil.isNotEmpty(item.getTaskDetailId()))
                .collect(Collectors.toMap(StatementDetail::getTaskDetailId, Function.identity(), (before, after) -> after));
        List<ReceiptItem> updateItemList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(detailMap)){
            List<ReceiptItem> receiptItems = receiptItemDao.listByIds(detailMap.keySet());
            Set<String> receiptIds = receiptItems.stream().map(ReceiptItem::getReceiptId).collect(Collectors.toSet());
            Map<String, Receipt> receiptMap = receiptDao.listByIds(receiptIds).stream().collect(Collectors.toMap(Receipt::getId, Receipt -> Receipt));


            for (ReceiptItem receiptItem : receiptItems) {
                Receipt receipt = receiptMap.get(receiptItem.getReceiptId());
                //没有业务结账的才变更入库明细
                if (ObjectUtil.isNotEmpty(receipt.getOccDate())){
                    continue;
                }

                StatementDetail statementDetail = detailMap.get(receiptItem.getId());
                if (!ObjectUtil.isAllNotEmpty(receiptItem.getTaxPrice(),receiptItem.getTaxRate()) ||
                        receiptItem.getTaxPrice().compareTo(statementDetail.getTaxPrice()) != 0 ||
                        receiptItem.getTaxRate().compareTo(statementDetail.getTaxTate()) != 0
                ){
                    BigDecimal exchange = statement.getExchange();
                    statementDetailCovertToReceiptItem(statementDetail,receiptItem,exchange);
                    updateItemList.add(receiptItem);
                }
            }
            if (ObjectUtil.isNotEmpty(updateItemList)){
                receiptItemDao.updateBatchById(updateItemList);
            }
        }

    }

    /**
     * 对账单转为入库明细
     * @param statementDetail
     * @param receiptItem
     * @param exchange
     */
    private void statementDetailCovertToReceiptItem(StatementDetail statementDetail, ReceiptItem receiptItem, BigDecimal exchange) {

        receiptItem.setTaxPrice(statementDetail.getTaxPrice().multiply(exchange));
        receiptItem.setTaxRate(statementDetail.getTaxTate());
        receiptItem.setPrice(statementDetail.getPrice().multiply(exchange));
        AmountCalculate amountCalculate = AmountCalculateUtil.taxPriceCalAmount(receiptItem.getTaxPrice(), receiptItem.getTaxRate(), receiptItem.getReceiptQuantity());
        receiptItem.setTaxAmount(amountCalculate.getTaxAmount());
        receiptItem.setOriginalUnitPrice(amountCalculate.getPrice());
        receiptItem.setTotalAmount(amountCalculate.getInTaxAmount());
        receiptItem.setOriginalTotalAmount(amountCalculate.getExTaxAmount());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAccountStatement(String accountStatementId) {
        AccountStatement statement = this.getAccountStatement(accountStatementId);
        BizAssert.isNotNull(statement, "对账单不存在");
        BizAssert.isFalse(statement.getPaidAmount().add(statement.getApplyingAmount()).compareTo(BigDecimal.ZERO) > 0,"对账单已付款，不能删除"); ;
        BizAssert.isTrue(statement.getTicketAmount().doubleValue() == 0.0,"对账单已到票，不能删除") ;

        List<StatementDetail> detailList = statement.getDetailList();
        if(ObjectUtil.isNotEmpty(detailList)){
            statementDetailService.removeByIds(detailList.stream().map(StatementDetail::getId).collect(Collectors.toList()));
        }
        //对账单生成修改库存记录
        Set<String> inventoryLogIds = statement.getDetailList().stream().map(StatementDetail::getInventoryLogId).collect(Collectors.toSet());
        inventoryLogService.updateFinanceCode(inventoryLogIds,"");
        BizAssert.isTrue(this.removeById(accountStatementId),"删除失败");
        //有一些是入库单明细,需要更新
        Collection<ReceiptItem> items = new ArrayList<>();
        inventoryLogIds.stream().forEach(id -> {
            ReceiptItem item = new ReceiptItem();
            item.setId(id);
            item.setStatement(false);
            item.setStatementQuantity(BigDecimal.ZERO);
            items.add(item);
        });
        receiptItemDao.updateBatchById(items);
    }

    @Override
    public AccountStatement getAccountStatement(String accountStatementId) {
        AccountStatement statement = this.getById(accountStatementId);
        BizAssert.isNotNull(statement, "对账单不存在");
        List<StatementDetail> detailList = statementDetailService.getByStatementId(accountStatementId);
        //设置序号、总的未税金额、总的税额
        for (int i = 0; i < detailList.size(); i++) {
            StatementDetail statementDetail = detailList.get(i);
            statementDetail.setNo(i);
        }

        statement.setDetailList(detailList);
        return statement;
    }

    @Override
    public AccountStatement paymentReject(PmsPayment pmsPayment) {
        if(ObjectUtil.isEmpty(pmsPayment.getStatementId())){
            AccountStatement statement = new AccountStatement();
            statement.setId("");
            statement.setStatementNo("")
                    .setPaymentNo("");
            return statement;
        }
        AccountStatement statement = this.getById(pmsPayment.getStatementId());
        BigDecimal applying = statement.getApplyingAmount().subtract(pmsPayment.getAppliedAmount());
        if(applying.compareTo(BigDecimal.ZERO) < 0){
            applying = BigDecimal.ZERO;
        }

        statement.setApplyingAmount(applying)
                .setPaymentNo("")
                .setPaymentId("")
                ;
        //已经付款了得扣减付款金额
        if (pmsPayment.getPaidAmount().compareTo(BigDecimal.ZERO)>0){
            BigDecimal paidAmount = statement.getPaidAmount().subtract(pmsPayment.getPaidAmount());
            BigDecimal discountAmount = statement.getDiscountAmount().subtract(pmsPayment.getDiscountAmount());
            BigDecimal unpaid = statement.getUnpaidAmount().add(pmsPayment.getPaidAmount()).add(pmsPayment.getDiscountAmount());
            statement
                    .setPaidAmount(paidAmount)
                    .setUnpaidAmount(unpaid)
                    .setDiscountAmount(discountAmount)
            ;
        }
        statement.calPayState();
        BizAssert.isTrue(this.updateById(statement), "更新失败");
        return statement;
    }

    @Override
    public AccountStatement updateStateAndPayment(String statementId, Payment payment) {
        AccountStatement statement = this.getById(statementId);
        BizAssert.isTrue(statement.getApplyingAmount().add(statement.getPaidAmount()).compareTo(statement.getRequestAmount()) < 0, "对账单申请请款金额已满，不能再次请款");
        statement.setPaymentId(payment.getId())
                .setPaymentNo(payment.getSerialNo())
                .setApplyingAmount(payment.getAppliedAmount().add(statement.getApplyingAmount()))
                .setDiscountAmount(payment.getDiscountAmount().add(statement.getDiscountAmount()))
                ;
        statement.calPayState();
        BizAssert.isTrue(this.updateById(statement), "更新失败");
        return statement;
    }

    @Override
    public AccountStatement paymentSuccess(PmsPayment pmsPayment) {
        AccountStatement statement = this.getById(pmsPayment.getStatementId());
        BigDecimal unpaid = statement.getUnpaidAmount().subtract(pmsPayment.getAppliedAmount()).subtract(pmsPayment.getDiscountAmount());
        BigDecimal applying = statement.getApplyingAmount().subtract(pmsPayment.getAppliedAmount());
        if(unpaid.compareTo(BigDecimal.ZERO) < 0){
            unpaid = BigDecimal.ZERO;
        }
        if(applying.compareTo(BigDecimal.ZERO) < 0){
            applying = BigDecimal.ZERO;
        }
        statement
                .setPaidAmount(statement.getPaidAmount().add(pmsPayment.getAppliedAmount()))
                .setUnpaidAmount(unpaid)
                .setApplyingAmount(applying)
                .setDiscountAmount(pmsPayment.getDiscountAmount().add(statement.getDiscountAmount()))
                ;
        statement.calPayState();
        BizAssert.isTrue(this.updateById(statement), "更新对账单失败");
        return statement;
    }

    @Override
    public List<AccountStatement> exportList(AccountStatementQuery accountStatementQuery) {
        return accountStatementMapper.exportList(accountStatementQuery.createExportQueryWrapper());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccountStatement(AccountStatementChangeForm accountStatementForm) {
        BizAssert.isNotEmpty(accountStatementForm.getId(), "对账单ID不能为空");
        AccountStatement statement = this.getById(accountStatementForm.getId());
        BizAssert.isNotNull(statement, "对账单不存在");
        List<StatementDetail> detailList = statementDetailService.getByStatementId(statement.getId());
        Map<String, AccountStatementChangeForm.Item> itemMap = accountStatementForm.getItems().stream().collect(Collectors.toMap(AccountStatementChangeForm.Item::getId, m -> m));
        BigDecimal requestAmount = BigDecimal.ZERO;
        BigDecimal receiptAmount = BigDecimal.ZERO;
        BigDecimal receiptQuantity = BigDecimal.ZERO;
        BigDecimal returnAmount = BigDecimal.ZERO;
        BigDecimal requestCount = BigDecimal.ZERO;
        BigDecimal orderAmount = BigDecimal.ZERO;
        int orderQuantity = 0;

        List<StatementDetail> updatedList = new ArrayList<>();
        List<StatementDetail> deleteList;
        //删除的明细
        if (accountStatementForm.getItems().size() < detailList.size()){
            deleteList = detailList.stream()
                    .filter(item -> !itemMap.containsKey(item.getId()))
                    .collect(Collectors.toList());
            detailList.removeIf(deleteList::contains);
            BizAssert.isTrue(statementDetailService.removeByIds(deleteList), "对账单修改失败");
            //释放库存流水
            inventoryLogService.updateFinanceCode(deleteList.stream().map(StatementDetail::getInventoryLogId).collect(Collectors.toSet()),"");
        }

        //Set<String> inventoryLogIds = detailList.stream().map(StatementDetail::getInventoryLogId).collect(Collectors.toSet());
        //List<InventoryLog> inventoryLogs = inventoryLogService.findList(new LambdaQueryWrapper<InventoryLog>().in(InventoryLog::getId, inventoryLogIds));
       // Map<String, InventoryLog> inventoryLogMap = inventoryLogs.stream().collect(Collectors.toMap(InventoryLog::getId, m -> m));
        MultiKeyMap<String,StatementDetail> orderMap = new MultiKeyMap<>();
        Set<String> orderNoSet = new HashSet<>();

        //修改的明细
        for (StatementDetail item : detailList) {
            AccountStatementChangeForm.Item changeItem = itemMap.get(item.getId());
            // 单价或货值更新才需要更新
            item.updateByChangeItem(changeItem, true);
            requestAmount = requestAmount.add(item.getTotalAmount());
            // 入库金额（包含了退货金额）
            receiptAmount = receiptAmount.add(item.getTotalAmount());
            receiptQuantity = receiptQuantity.add(item.getQuantity());
            if (StatementDetailTypeEnum.getReturnType().contains(item.getStatementDetailType())) {
                // 退库（只有退货）
                returnAmount = returnAmount.add(item.getQuantity().multiply(item.getTaxPrice()));
            }
            //订单-产品维度
            if (!orderMap.containsKey(item.getBizId(),item.getMaterialId())){
                //订单维度
                if (!orderNoSet.contains(item.getBizCode())){
                    orderQuantity += 1;
                }
                requestCount = requestCount.add(NumberUtil.null2Zero(item.getBizQuantity()) );
                orderAmount = orderAmount.add(NumberUtil.null2Zero(item.getBizAmount()));
            }
            orderMap.put(item.getBizId(),item.getMaterialId(), item);
            orderNoSet.add(item.getBizCode());

            updatedList.add(item);

        }
        //对账单
        statement.setReceiptAmount(receiptAmount);
        statement.setReceiptQuantity(receiptQuantity);
        statement.setReturnAmount(returnAmount);
        //请款
        statement.setRequestAmount(receiptAmount);
        statement.setUnpaidAmount(receiptAmount);
        //订单
        statement.setOrderQuantity(orderQuantity);
        statement.setRequestCount(requestCount);
        statement.setTotalTaxAmount(orderAmount);
        BizAssert.isTrue(this.updateById(statement), "对账单修改失败");
        if (!updatedList.isEmpty()){
            BizAssert.isTrue(statementDetailService.updateBatchById(updatedList), "对账单修改失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean endPay(AccountStatementEndPayForm form) {
        AccountStatement accountStatement = this.getById(form.getId());
        if (accountStatement.getApplyingAmount().compareTo(BigDecimal.ZERO) > 0) {
            LambdaQueryWrapper<PmsPayment> wr = new LambdaQueryWrapper<>();
            wr.eq(PmsPayment::getStatementId, form.getId());
            wr.in(PmsPayment::getState, PaymentStateEnum.PENDING, PaymentStateEnum.SUBMITTED);
            List<PmsPayment> pmsPaymentList = pmsPaymentService.list(wr);
            if (!pmsPaymentList.isEmpty()){
                List<String> serialNos = pmsPaymentList.stream().map(PmsPayment::getSerialNo).collect(Collectors.toList());
                Map<String, PmsPayment> pmsPaymentMap = pmsPaymentList.stream()
                        .collect(Collectors.toMap(PmsPayment::getSerialNo, Function.identity(), (v1, v2) -> v1));
                List<Payment> paymentList = paymentDAO.list(new LambdaQueryWrapper<Payment>().in(Payment::getSerialNo, serialNos));
                for (Payment payment : paymentList) {
                    if (payment.getState() == PaymentStateEnum.PENDING){
                        PmsPayment pmsPayment = pmsPaymentMap.get(payment.getSerialNo());
                        if (StatementTypeEnum.PURCHASE.equals( accountStatement.getType())){
                            purchaseOrderService.cancelPayment(pmsPayment.getId());
                        }else {
                            outsourcingOrderService.cancelPayment(pmsPayment.getId());
                        }


                    }
                    if (payment.getState() == PaymentStateEnum.SUBMITTED){
                        Approval approval = approvalService.getApproval(payment.getId(), OamsConstant.RESOURCE_PAYMENT, ApprovalStateEnum.PENDING);
                        paymentService.rejected(approval);
                    }
                }
            }

        }
            accountStatement.setPayState(StatementPayStateEnum.HAVA_PAID);
        LambdaUpdateWrapper<AccountStatement> updateWrapper = new LambdaUpdateWrapper<AccountStatement>()
                .set(AccountStatement::getPayState, StatementPayStateEnum.HAVA_PAID)
                .set(AccountStatement::getEndPayReason, form.getEndPayReason())
                .eq(AccountStatement::getId, form.getId());
        return this.update(updateWrapper);
    }
}
