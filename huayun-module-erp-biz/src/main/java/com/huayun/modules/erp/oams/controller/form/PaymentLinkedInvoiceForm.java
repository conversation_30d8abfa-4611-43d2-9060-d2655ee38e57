package com.huayun.modules.erp.oams.controller.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 请款单关联发票form
 */
@Data
@ApiModel("手动创建请款单")
public class PaymentLinkedInvoiceForm {

    @ApiModelProperty(value = "请款单id列表")
    @Size(min = 1, message = "请款单id列表不能为空")
    private List<String> serialIds;

    @ApiModelProperty(value = "发票id")
    @NotEmpty(message = "发票id不能为空")
    private String invoiceId;


}
