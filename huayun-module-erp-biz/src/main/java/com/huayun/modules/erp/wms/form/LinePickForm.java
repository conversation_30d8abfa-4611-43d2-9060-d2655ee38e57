package com.huayun.modules.erp.wms.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

/**
 * 线边仓备料
 *
 * <AUTHOR>
 * @date 2023/4/23 10:57
 **/
@Data
public class LinePickForm {

    /**物料编码*/
    @ApiModelProperty(value = "物料编码")
    @NotBlank(message = "物料编码不可为空")
    private String materialCode;

    /**物料标签*/
/*    @ApiModelProperty(value = "物料标签")
    @NotEmpty(message = "物料标签不可为空")
    private Set<String> lotNos;*/

    @ApiModelProperty(value = "物料标签")
    private List<PickingRecordForm> list;
}
