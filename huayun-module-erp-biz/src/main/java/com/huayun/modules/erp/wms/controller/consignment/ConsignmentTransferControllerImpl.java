package com.huayun.modules.erp.wms.controller.consignment;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.common.entity.BaseEntity;
import com.huayun.modules.common.enums.YesOrNOEnum;
import com.huayun.modules.common.exception.ExceptionFactory;
import com.huayun.modules.common.log.aspect.annotation.Log;
import com.huayun.modules.common.log.constant.OperateTypeConstant;
import com.huayun.modules.common.util.BizAssert;
import com.huayun.modules.common.vo.BaseVO;
import com.huayun.modules.erp.oms.constant.OMSConstant;
import com.huayun.modules.erp.oms.constant.OMSLogConstant;
import com.huayun.modules.erp.oms.constant.enums.ConsignmentTransferType;
import com.huayun.modules.erp.oms.constant.enums.OrderCategoryEnum;
import com.huayun.modules.erp.oms.entity.ConsignmentInventory;
import com.huayun.modules.erp.oms.entity.Customer;
import com.huayun.modules.erp.oms.entity.CustomizedProduct;
import com.huayun.modules.erp.oms.entity.Order;
import com.huayun.modules.erp.oms.entity.model.OrderModel;
import com.huayun.modules.erp.oms.query.OrderQuery;
import com.huayun.modules.erp.oms.service.IConsignmentInventoryService;
import com.huayun.modules.erp.oms.service.ICustomerService;
import com.huayun.modules.erp.oms.service.ICustomizedProductService;
import com.huayun.modules.erp.oms.service.IOrderService;
import com.huayun.modules.erp.oms.vo.ConsignmentOrderVO;
import com.huayun.modules.erp.oms.vo.ListOrderVO;
import com.huayun.modules.erp.pdm.entity.Product;
import com.huayun.modules.erp.pdm.service.IProductService;
import com.huayun.modules.erp.utils.FileUtils;
import com.huayun.modules.erp.utils.ImportError;
import com.huayun.modules.erp.wms.entity.ConsignmentTransfer;
import com.huayun.modules.erp.wms.entity.ConsignmentTransferItem;
import com.huayun.modules.erp.wms.entity.ConsignmentTransferLog;
import com.huayun.modules.erp.wms.form.ConsignmentTransferSellImport;
import com.huayun.modules.erp.wms.form.LineShelfForm;
import com.huayun.modules.erp.wms.form.PickingRecordForm;
import com.huayun.modules.erp.wms.service.consignment.IConsignmentTransferItemService;
import com.huayun.modules.erp.wms.service.consignment.IConsignmentTransferLogService;
import com.huayun.modules.erp.wms.service.consignment.IConsignmentTransferService;
import com.huayun.modules.mes.controller.IMaterialDispatchItemController;
import com.huayun.modules.mes.controller.IMaterialDispatchLogController;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@AllArgsConstructor
public class ConsignmentTransferControllerImpl implements ConsignmentTransferController {
    
    IConsignmentTransferService consignmentTransferService;
    IConsignmentTransferItemService consignmentTransferItemService;
    IConsignmentTransferLogService consignmentTransferLogService;
    IOrderService orderService;
    ICustomizedProductService customizedProductService;
    ICustomerService customerService;
    IConsignmentInventoryService consignmentInventoryService;
    IProductService productService;
    
    @Override
    public Result<IPage<ConsignmentTransferDTO>> requestGet(ConsignmentTransferQuery query) {
        QueryWrapper<ConsignmentTransfer> qw = new QueryWrapper<>();
        qw.eq("a.deleted", YesOrNOEnum.NO.getValue());
        qw.like(StringUtils.isNoneEmpty(query.getCode()), "a.code", query.getCode());
        qw.in(ObjectUtils.isNotEmpty(query.getType()), "a.type", query.getType());
        qw.eq(StringUtils.isNoneEmpty(query.getCustomerId()), "a.customer_id", query.getCustomerId());
        qw.like(StringUtils.isNoneEmpty(query.getCustomerName()), "b.customer_name", query.getCustomerName());
        qw.in(ObjectUtil.isNotEmpty(query.getState()), "a.state", query.getState());
        qw.orderByDesc("a.create_time", "a.id");
        if (query.getCreateTime() != null && query.getCreateTime().size() == 2) {
            qw.between( "a.create_time", query.getCreateTime(), query.getCreateTime());
        }
        IPage<ConsignmentTransferDTO> page = consignmentTransferService
                .page(new Page<>(query.getPage(), query.getPageSize()), qw)
                .convert(ConsignmentTransferDTO::new);
        if (query.getType() != null && query.getType().size() == 1 && ConsignmentTransferType.SELL.equals(query.getType().get(0)) && !page.getRecords().isEmpty()) {
            // 销货查出详情
            Set<String> ids = page.getRecords().stream().map(BaseVO::getId).collect(Collectors.toSet());
            List<ConsignmentTransferItem> items = consignmentTransferItemService.listByTransferIds(ids);
            Map<String, List<ConsignmentTransferItem>> groupItems = items.stream().collect(Collectors.groupingBy(ConsignmentTransferItem::getTransferId));
            page.getRecords().forEach(row -> {
                List<MaterialTransferItemDTO> itemList = groupItems.get(row.getId()).stream().map(MaterialTransferItemDTO::new).collect(Collectors.toList());
                row.setMaterialList(itemList);
            });
        }
        
        return Result.OK(page);
    }

    @Override
    public Result<ConsignmentTransferDTO> requestCreate(ConsignmentTransferForm form) {
        ConsignmentTransferDTO dto = consignmentTransferService.create(form);
        return Result.ok(dto);
    }

    @Override
    public Result<ConsignmentTransferDTO> requestGet(String transferId) {
        ConsignmentTransfer transfer = consignmentTransferService.getById(transferId);
        BizAssert.isNotNull(transfer, "调拨单不存在");
        Customer customer = customerService.getById(transfer.getCustomerId());
        transfer.setCustomerName(customer.getCustomerName());
        List<ConsignmentTransferItem> items = consignmentTransferItemService.getList(transfer.getId());
        ConsignmentTransferDTO dto = new ConsignmentTransferDTO(transfer);
        List<MaterialTransferItemDTO> list = items.stream().map(MaterialTransferItemDTO::new).collect(Collectors.toList());
        dto.setMaterialList(list);
        Set<String> materialIds = list.stream().map(MaterialTransferItemDTO::getMaterialId).collect(Collectors.toSet());
        Map<String, String> productMap = productService.listByIds(materialIds).stream().filter(m -> m.getDrawingNumber() != null).collect(Collectors.toMap(BaseEntity::getId, Product::getDrawingNumber));
        list.forEach(item -> item.setDrawingNumber(productMap.get(item.getMaterialId())));
        return Result.OK(dto);
    }

    @Override
    public Result<ConsignmentTransferDTO> requestUpdate(String transferId, ConsignmentTransferForm form) {
        return null;
    }

    @Override
    @Log(resourceType = OMSConstant.CONSIGNMENT,
            operateType = OperateTypeConstant.DELETE,
            template = "取消寄售发货")
    public Result<ConsignmentTransferDTO> requestCancel(String transferId) {
        ConsignmentTransfer transfer = consignmentTransferService.getById(transferId);
        BizAssert.isNotNull(transfer, "单据不存在");
        consignmentTransferService.cancel(transfer);
        return Result.OK(new ConsignmentTransferDTO(transfer));
    }

    @Override
    public Result<ConsignmentTransferDTO> requestDelete(String transferId) {
        return null;
    }

    @Override
    public Result<Long> requestGetList(ConsignmentTransferQuery query) {
        return null;
    }

    @Override
    public Result<IPage<ConsignmentOrderVO>> queryOrderPageList(OrderQuery query) {
        Page<Order> page = new Page<>(query.getPage(), query.getPageSize());
        query.setIsConsignment(true);
        query.setTypes(new HashSet<>(Arrays.asList(OrderCategoryEnum.PRODUCTION_ORDER, OrderCategoryEnum.RETAIL, OrderCategoryEnum.SAMPLE, OrderCategoryEnum.INTERNAL_ORDER)));
        IPage<ConsignmentOrderVO> pageList = orderService.queryDeliveryPage(page, query)
                .convert(ConsignmentOrderVO::new);

        List<ConsignmentOrderVO> records = pageList.getRecords();
        if (!records.isEmpty()) {
            Set<String> productIds = records.stream().map(ListOrderVO::getProductId).collect(Collectors.toSet());
            Map<String, CustomizedProduct> customizedProductMap = customizedProductService.findByProductIds(productIds).stream().collect(Collectors.toMap(row -> String.format("%s_%s", row.getCustomerId(), row.getProductId()), m -> m));
            records.forEach(item -> item.setAlias(customizedProductMap.get(String.format("%s_%s", item.getCustomerId(), item.getProductId()))));
            
            // 统计未交付数量，按产品维度
//            Map<String, BigDecimal> sumOrderMap = orderService.sumDeliveryOrder(query).stream().collect(Collectors.toMap(Order::getProductId, Order::getQuantity));

            // 寄售库存
            if (StringUtils.isNotEmpty(query.getCustomerId())) {
                Map<String, ConsignmentInventory> consignmentInventoryMap = consignmentInventoryService.listByProductId(query.getCustomerId(), productIds).stream().collect(Collectors.toMap(ConsignmentInventory::getProductId, m -> m));
                records.forEach(item -> {
                    ConsignmentInventory consignmentInventory = consignmentInventoryMap.get(item.getProductId());
                    item.setConsignmentInventory(consignmentInventory != null ? consignmentInventory.getQuantity() : BigDecimal.ZERO);
//                    item.setUnDeliveryQuantity(sumOrderMap.getOrDefault(item.getProductId(), BigDecimal.ZERO));
                    BigDecimal unDeliveryQuantity = item.getTotalQuantity().subtract(item.getDeliveryQuantity());
                    item.setUnDeliveryQuantity(unDeliveryQuantity.compareTo(BigDecimal.ZERO) > 0 ? unDeliveryQuantity : BigDecimal.ZERO);
                });
            }
        }

        return Result.OK("查询成功！", pageList);
    }

    @Override
    public Result<String> pick(String transferId, List<PickingRecordForm> recordList) {
        consignmentTransferService.pick(transferId, recordList);
        return Result.OK("备料成功！");
    }

    @Override
    public Result<IMaterialDispatchItemController.MaterialDispatchItemDTO> itemLogs(String itemId) {
        ConsignmentTransferItem item = consignmentTransferItemService.getById(itemId);
        BizAssert.isNotNull(item, "调拨物料明细不存在");
        IMaterialDispatchItemController.MaterialDispatchItemDTO itemDTO = new IMaterialDispatchItemController.MaterialDispatchItemDTO(item);
        List<ConsignmentTransferLog> logs = consignmentTransferLogService.getListByMaterial(item.getTransferId(), itemId, item.getMaterialCode());
        LinkedList<IMaterialDispatchLogController.MaterialDispatchLogDTO> lotList = new LinkedList<>();
        for (ConsignmentTransferLog materialDispatchLog : logs) {
            lotList.add(new IMaterialDispatchLogController.MaterialDispatchLogDTO(materialDispatchLog));
        }
        itemDTO.setLotList(lotList);
        return Result.OK(itemDTO);
    }

    @Override
    public Result<String> requestPicking(String transferId) {
        consignmentTransferService.picking(transferId);
        return Result.OK("操作成功");
    }

    @Override
    public Result<String> sell(ConsignmentTransferForm form) {
        consignmentTransferService.sell(form);
        return Result.OK("销货成功");
    }

    @Override
    public Result<String> consignmentReturn(ConsignmentTransferForm form) {
        ConsignmentTransferDTO dto = consignmentTransferService.consignmentReturn(form);
        return Result.OK("提交成功", dto.getId());
    }

    @Override
    public Result<String> returnPick(String id, LineShelfForm form) {
        consignmentTransferService.returnPick(id, form);
        return Result.OK("上架成功");
    }

    @Override
    public Result<List<ImportError>> sellImport(MultipartFile excelFile) {
        List<ConsignmentTransferSellImport> imports = FileUtils.importExcel(ConsignmentTransferSellImport.class, excelFile);
        if (imports == null || imports.isEmpty()) {
            return Result.error("导入失败，请使用导入模版，并正确填写数据");
        }
        List<ImportError> errors = consignmentTransferService.sellImport(imports);
        if (!errors.isEmpty()) {
            Result<List<ImportError>> resultError = Result.error("导入失败", errors);
            resultError.setCode(400);
            return resultError;
        }
        return Result.ok("导入成功！");
    }
}
