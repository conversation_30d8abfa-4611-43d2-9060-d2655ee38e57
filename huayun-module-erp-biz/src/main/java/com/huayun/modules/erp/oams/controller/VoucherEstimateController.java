package com.huayun.modules.erp.oams.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huayun.modules.common.poi.IExportService;
import com.huayun.modules.erp.oams.controller.form.VoucherEstimateForm;
import com.huayun.modules.erp.oams.controller.query.VoucherDeliveryEstimateQuery;
import com.huayun.modules.erp.oams.controller.query.VoucherEstimateQuery;
import com.huayun.modules.erp.oams.service.IVoucherEstimateService;
import com.huayun.modules.erp.oams.vo.DeliveryBalanceVO;
import com.huayun.modules.erp.oams.vo.ReceiptBalanceResVO;
import com.huayun.modules.erp.oams.vo.VoucherVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/25
 */
@Slf4j
@Api(tags = "OAMS-凭证管理")
@RestController
@RequestMapping("/oams/voucher/estimate")
public class VoucherEstimateController {

    @Resource
    private IVoucherEstimateService voucherEstimateService;

    @Resource
    private IExportService exportService;
    /**
     * 创建
     */
    @ApiOperation(value = "凭证管理-创建", notes = "凭证管理-创建")
    //@ApprovalRequired(resourceType = OamsConstant.RESOURCE_VOUCHER, approvalType = "凭证创建")
    @PostMapping("/create")
    public Result<VoucherVO> requestCreate(@RequestBody VoucherEstimateForm form) {
        return Result.ok(voucherEstimateService.create(form));
    }

    @ApiOperation(value = "暂估入库余额表-分页查询", notes = "暂估入库余额表-分页查询")
    @GetMapping("/getReceiptBalance")
    public Result<IPage<ReceiptBalanceResVO>> requestGetPage(VoucherEstimateQuery query) {
        IPage<ReceiptBalanceResVO> page = voucherEstimateService.getReceiptBalance(query);
        return Result.ok(page);
    }

    @ApiOperation(value = "暂估入库余额表-汇总查询", notes = "暂估入库余额表-汇总查询")
    @GetMapping("/getReceiptBalanceTotal")
    public Result<ReceiptBalanceResVO> getReceiptBalanceTotal(VoucherEstimateQuery query) {
        ReceiptBalanceResVO res = voucherEstimateService.getReceiptBalanceTotal(query);
        return Result.ok(res);
    }


    @ApiOperation(value = "暂估入库余额表-导出", notes = "暂估入库余额表-导出")
    @GetMapping("/export")
    public void export(VoucherEstimateQuery query)  {
    	Map<String, Object> map =  voucherEstimateService.expReceiptBalance(query);

    	exportService.exportExcel("oss://templates/暂估入库余额表导出模版.xlsx", 0, 0, map);
    	//exportService.exportExcel(ReceiptBalanceResVO.class, list);
        //exportService.exportExcel(LedgerAccountInitPort.class, list);
    }

    @ApiOperation(value = "发出商品-分页查询", notes = "暂估入库余额表-分页查询")
    @GetMapping("/delivery/getBalance")
    public Result<IPage<DeliveryBalanceVO>> getBalance(VoucherDeliveryEstimateQuery query) {
        IPage<DeliveryBalanceVO> page = voucherEstimateService.getDeliveryBalance(query);
        return Result.ok(page);
    }

    @ApiOperation(value = "发出商品-汇总查询", notes = "暂估入库余额表-汇总查询")
    @GetMapping("/delivery/getBalanceTotal")
    public Result<DeliveryBalanceVO> getBalanceTotal(VoucherDeliveryEstimateQuery query) {
        DeliveryBalanceVO deliveryBalanceVO = voucherEstimateService.getTotal(query);
        return Result.ok(deliveryBalanceVO);
    }


    @ApiOperation(value = "发出商品汇总-导出", notes = "发出商品汇总-导出")
    @GetMapping("/delivery/export")
    public void deliveryExport(VoucherDeliveryEstimateQuery query)  {
        Map<String, Object> map =  voucherEstimateService.expDeliveryBalance(query);
        exportService.exportExcel("oss://templates/交付出库发出商品报表模版.xlsx", 0, 0, map);
        //exportService.exportExcel("C:/Users/<USER>/Desktop/lin/1/交付出库发出商品报表模版.xlsx", 0, 0, map);

    }
}
