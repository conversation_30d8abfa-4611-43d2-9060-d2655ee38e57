package com.huayun.modules.erp.oams.util;

import java.util.HashMap;
import java.util.Map;

public class CurrencySymbolUtil {

    private static final Map<String, String> currencySymbols = new HashMap<>();

    static {
        currencySymbols.put("CNY", "¥");   // 人民币
        currencySymbols.put("USD", "$");   // 美元
        currencySymbols.put("EUR", "€");   // 欧元
        currencySymbols.put("GBP", "£");   // 英镑
        currencySymbols.put("JPY", "¥");   // 日元
        currencySymbols.put("AUD", "A$");  // 澳元
        currencySymbols.put("CAD", "C$");  // 加元
        currencySymbols.put("CHF", "CHF"); // 瑞士法郎
        currencySymbols.put("NZD", "NZ$"); // 新西兰元
        currencySymbols.put("SEK", "kr");  // 瑞典克朗
        currencySymbols.put("NOK", "kr");  // 挪威克朗
        currencySymbols.put("DKK", "kr");  // 丹麦克朗
        currencySymbols.put("SGD", "S$");  // 新加坡元
        currencySymbols.put("HKD", "HK$"); // 港元
        currencySymbols.put("MXN", "$");   // 墨西哥比索
        currencySymbols.put("ILS", "₪");   // 以色列新谢克尔
        currencySymbols.put("MYR", "RM");  // 马来西亚林吉特
        currencySymbols.put("THB", "฿");   // 泰国铢
        currencySymbols.put("PHP", "₱");   // 菲律宾比索
        currencySymbols.put("ZAR", "R");   // 南非兰特
        currencySymbols.put("INR", "₹");   // 印度卢比
        currencySymbols.put("BRL", "R$");  // 巴西雷亚尔
        currencySymbols.put("ARS", "$");    // 阿根廷比索
        currencySymbols.put("VND", "₫");   // 越南盾
        currencySymbols.put("CLP", "$");   // 智利比索
        currencySymbols.put("COP", "$");    // 哥伦比亚比索
        currencySymbols.put("PKR", "₨");   // 巴基斯坦卢比
        currencySymbols.put("TRY", "₺");    // 土耳其里拉
        currencySymbols.put("AED", "د.إ");  // 阿联酋迪拉姆
        currencySymbols.put("SAR", "ر.س");  // 沙特里亚尔
        currencySymbols.put("QAR", "ر.ق");  // 卡塔尔里亚尔
        currencySymbols.put("OMR", "ر.ع.");  // 阿曼里亚尔
        currencySymbols.put("BHD", ".د.ب");  // 巴林第纳尔
        currencySymbols.put("KWD", "د.ك");   // 科威特第纳尔
        currencySymbols.put("JOD", "د.ا");   // 约旦第纳尔
        currencySymbols.put("TWD", "NT$");   // 新台币
        currencySymbols.put("CUP", "$");     // 古巴比索
        currencySymbols.put("NIO", "C$");    // 尼加拉瓜科多巴
        currencySymbols.put("GHS", "₵");     // 加纳塞地
        currencySymbols.put("HUF", "Ft");    // 匈牙利福林
        currencySymbols.put("CZK", "Kč");    // 捷克克朗
        currencySymbols.put("ISK", "kr");     // 冰岛克朗
        currencySymbols.put("LTL", "Lt");     // 立陶宛立特
        currencySymbols.put("MDL", "L");      // 摩尔多瓦列伊
        currencySymbols.put("RSD", "дин.");   // 塞尔维亚第纳尔
        currencySymbols.put("RON", "lei");    // 罗马尼亚列伊
        currencySymbols.put("BGN", "лв.");    // 保加利亚列弗
        currencySymbols.put("HRK", "kn");     // 克罗地亚库纳
        currencySymbols.put("UAH", "₴");      // 乌克兰格里夫纳
        currencySymbols.put("XOF", "CFA");     // 西非法郎
        currencySymbols.put("XAF", "CFA");     // 中非法郎
        currencySymbols.put("XPF", "CFP");     // 法属太平洋法郎
        // 继续添加更多货币符号...
    }

    public static String getCurrencySymbol(String currencyCode) {
        return currencySymbols.getOrDefault(currencyCode.toUpperCase(), currencyCode);
    }

}