package com.huayun.modules.erp.mro.form;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class AccessoryImportForm {

    @Excel(name = "配件编码")
    @Length(max = 32, message = "配件编码长度超出限制")
    private String code;
    @Excel(name = "配件名称")
    @NotBlank(message = "配件名称不能为空")
    @Length(max = 64, message = "配件名称长度超出限制")
    private String name;
    @Excel(name = "规格")
    @NotBlank(message = "规格不能为空")
    @Length(max = 128, message = "规格长度超出限制")
    private String specification;
    @Excel(name = "型号")
    @NotBlank(message = "型号不能为空")
    @Length(max = 128, message = "型号长度超出限制")
    private String model;
    @Excel(name = "单位")
    @NotBlank(message = "单位不能为空")
    @Length(max = 32, message = "单位长度超出限制")
    private String unit;
    @Excel(name = "供应商")
    @Length(max = 128, message = "供应商长度超出限制")
    private String supplier;
    @Excel(name = "品牌")
    @Length(max = 64, message = "品牌长度超出限制")
    private String brand;
    @Excel(name = "单价")
    @NotBlank(message = "单价不能为空")
    private String price;
    @Excel(name = "币种")
    @NotBlank(message = "币种不能为空")
    @Length(max = 32, message = "币种长度超出限制")
    private String currency;
    @Excel(name = "库存")
    @NotBlank(message = "库存不能为空")
    private String count;
    
    BigDecimal inventoryCount;
    BigDecimal unitPrice;

}
