package com.huayun.modules.erp.wms.controller.material;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.common.enums.YesOrNOEnum;
import com.huayun.modules.common.log.aspect.annotation.Log;
import com.huayun.modules.common.log.constant.OperateTypeConstant;
import com.huayun.modules.common.rule.validate.ValidationGroup;
import com.huayun.modules.erp.wms.constant.WmsConstant;
import com.huayun.modules.erp.wms.entity.MaterialSubstitution;
import com.huayun.modules.erp.wms.entity.model.MaterialSubstitutionModel;
import com.huayun.modules.erp.wms.form.MaterialSubstitutionBatchForm;
import com.huayun.modules.erp.wms.form.MaterialSubstitutionForm;
import com.huayun.modules.erp.wms.query.MaterialSubstitutionQuery;
import com.huayun.modules.erp.wms.service.material.IMaterialSubstitutionService;
import com.huayun.modules.erp.wms.vo.MaterialSubstitutionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 替代物料管理
 *
 * <AUTHOR>
 * @date   2022/08/23
 */
@Slf4j
@Api(tags="WMS-替代物料管理")
@RestController
@RequestMapping("/wms/material-substitutions")
public class MaterialSubstitutionController {

    @Resource
    private IMaterialSubstitutionService materialSubstitutionService;

    /**
     * 分页列表查询
     *
     * @param query
     */
    @ApiOperation(value="替代物料管理-分页列表查询", notes="替代物料管理-分页列表查询")
    @GetMapping
    public Result<IPage<MaterialSubstitutionVO>> queryPageList(MaterialSubstitutionQuery query) {
        Page<MaterialSubstitution> page = new Page<>(query.getPage(), query.getPageSize());
        IPage<MaterialSubstitutionVO> pageList = materialSubstitutionService.getMaterialSubstitutionPage(page, query).convert(MaterialSubstitutionVO::new);
        return Result.OK("查询成功！", pageList);
    }

    /**
     * 列表查询
     *
     * <AUTHOR>
     * @date 2022/12/6 11:19
     * @param materialId
     * @return org.jeecg.common.api.vo.Result<java.util.List<com.huayun.modules.erp.wms.vo.MaterialSubstitutionVO>>
     */
    @ApiOperation(value="列表查询", notes="列表查询")
    @GetMapping("/list")
    public Result<List<MaterialSubstitutionVO>> findListByMaterialId(@RequestParam(name="materialId") String materialId){
        QueryWrapper<MaterialSubstitution> wrapper = new QueryWrapper<MaterialSubstitution>()
                .eq("substitution.deleted", YesOrNOEnum.NO.getValue())
                .orderByDesc("substitution.create_time")
                .eq("substitution.primary_material_id", materialId);
        List<MaterialSubstitutionVO> list = materialSubstitutionService.findList(wrapper)
                .stream()
                .map(MaterialSubstitutionVO::new)
                .collect(Collectors.toList());
        return Result.OK("查询成功！", list);
    }

    /**
     * 添加
     *
     * @param createMaterialSubstitutionForm
     */
    @ApiOperation(value="替代物料管理-添加", notes="替代物料管理-添加")
    @PostMapping
    @Log(resourceType = WmsConstant.RESOURCE_MATERIAL_SUBSTITUTIONS, operateType = OperateTypeConstant.INSERT
            , template = "新增替代物料，主物料：”${primaryMaterialCode}“，替代物料：”${secondaryMaterialCode}“，比例：${primaryMaterialProportion}:${secondaryMaterialProportion}，启用：${enable}")
    public Result<MaterialSubstitutionVO> createMaterialSubstitution(@Validated(value = ValidationGroup.Create.class)
                                                                     @RequestBody MaterialSubstitutionForm createMaterialSubstitutionForm) {
        MaterialSubstitution materialSubstitution = new MaterialSubstitution();
        BeanUtils.copyProperties(createMaterialSubstitutionForm, materialSubstitution);
        MaterialSubstitutionModel model = materialSubstitutionService.createMaterialSubstitution(materialSubstitution);
        return Result.OK("添加成功！", new MaterialSubstitutionVO(model));
    }

    /**
     * 添加
     *
     * @param form
     */
    @ApiOperation(value="替代物料管理-批量添加", notes="替代物料管理-批量添加")
    @PostMapping("/batch")
    public Result<String> saveBatch(@Validated(value = ValidationGroup.Create.class)
                                                        @RequestBody MaterialSubstitutionBatchForm form) {
        List<MaterialSubstitution> substitutionList = form.getSecondaryMaterialIds().stream().map(secondaryMaterialId -> {
            MaterialSubstitution substitution = new MaterialSubstitution();
            substitution.setPrimaryMaterialId(form.getPrimaryMaterialId());
            substitution.setPrimaryMaterialProportion(BigDecimal.ZERO);
            substitution.setSecondaryMaterialId(secondaryMaterialId);
            substitution.setSecondaryMaterialProportion(BigDecimal.ZERO);
            substitution.setEnable(true);
            return substitution;
        }).collect(Collectors.toList());
        materialSubstitutionService.saveBatch(substitutionList);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param id
     * @param updateMaterialSubstitutionForm
     */
    @ApiOperation(value="替代物料管理-编辑", notes="替代物料管理-编辑")
    @PutMapping("/{id}")
    @Log(resourceType = WmsConstant.RESOURCE_MATERIAL_SUBSTITUTIONS, operateType = OperateTypeConstant.UPDATE
            , template = "编辑替代物料，主物料：”${primaryMaterialCode}“，替代物料：”${secondaryMaterialCode}“，比例：${primaryMaterialProportion}:${secondaryMaterialProportion}，启用：${enable}")
    public Result<MaterialSubstitutionVO> updateMaterialSubstitution(@PathVariable String id,
                                                                     @Validated(value = ValidationGroup.Update.class)
                                                                     @RequestBody MaterialSubstitutionForm updateMaterialSubstitutionForm) {
        MaterialSubstitution materialSubstitution = new MaterialSubstitution();
        BeanUtils.copyProperties(updateMaterialSubstitutionForm, materialSubstitution);
        MaterialSubstitutionModel model = materialSubstitutionService.updateMaterialSubstitution(id, materialSubstitution);
        return Result.OK("编辑成功!", new MaterialSubstitutionVO(model));
    }

    /**
     * 启停用
     *
     * <AUTHOR>
     * @date 2022/9/1 17:55
     * @param id
     * @param enable
     * @return org.jeecg.common.api.vo.Result<java.lang.String>
     */
    @Log(resourceType = WmsConstant.RESOURCE_MATERIAL_SUBSTITUTIONS, operateType = OperateTypeConstant.UPDATE
            , template = "启停用，主物料：”${primaryMaterialCode}“，替代物料：”${secondaryMaterialCode}“，启用：${enable}")
    @ApiOperation(value = "替代物料管理-启停用", notes = "替代物料管理-启停用")
    @PutMapping(value = "/{id}/enable")
    public Result<MaterialSubstitutionVO> enable(@PathVariable(name="id") String id,
                                 @ApiParam(value = "是否启用",required = true)
                                 @RequestParam(name="enable") Boolean enable) {
        MaterialSubstitutionModel model = materialSubstitutionService.enable(id, enable);
        return Result.OK("编辑成功!", new MaterialSubstitutionVO(model));
    }

    /**
     * 通过id删除
     *
     * @param id
     */
    @ApiOperation(value="替代物料管理-通过id删除", notes="替代物料管理-通过id删除")
    @DeleteMapping("/{id}")
    @Log(resourceType = WmsConstant.RESOURCE_MATERIAL_SUBSTITUTIONS, operateType = OperateTypeConstant.DELETE
            , template = "删除替代物料，主物料：”${primaryMaterialCode}“，替代物料：”${secondaryMaterialCode}“")
    public Result<MaterialSubstitutionVO> deleteMaterialSubstitution(@PathVariable String id) {
        MaterialSubstitutionModel model = materialSubstitutionService.deleteMaterialSubstitution(id);
        return Result.OK("删除成功!", new MaterialSubstitutionVO(model));
    }

}