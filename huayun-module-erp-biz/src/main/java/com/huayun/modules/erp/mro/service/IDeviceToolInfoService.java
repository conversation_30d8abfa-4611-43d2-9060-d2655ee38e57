package com.huayun.modules.erp.mro.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huayun.modules.erp.mro.constant.enums.DeviceTorsionStatusEnums;
import com.huayun.modules.erp.mro.entity.DeviceToolInfo;
import com.huayun.modules.erp.mro.form.DeviceToolImportForm;
import com.huayun.modules.erp.mro.form.GeneralInformationForm;
import com.huayun.modules.erp.mro.query.*;
import com.huayun.modules.erp.mro.vo.GeneralInformationVO;
import com.huayun.modules.erp.mro.vo.SupportingInformationVO;
import com.huayun.modules.erp.utils.ImportError;

import java.util.List;

/**
 * 工具信息维护
 *
 * <AUTHOR> am I?
 * @date 2022/08/04
 */
public interface IDeviceToolInfoService  extends IService<DeviceToolInfo> {

    /**
     * TODO
     *
     * @param deviceToolInfoQuery
     * @return
     */
    IPage<DeviceToolInfo> getDeviceToolInfoPage(DeviceToolInfoQuery deviceToolInfoQuery);

    /**
     * 根据查询条件获取设备列表
     *
     * @param deviceToolInfoQuery
     * @return java.util.List<com.huayun.modules.erp.mro.entity.DeviceToolInfo>
     * <AUTHOR>
     * @date 2023/10/10 9:50
     */
    List<DeviceToolInfo> getDeviceToolInfoList(DeviceToolInfoQuery deviceToolInfoQuery);

    /**
     * 获取工具信息 给固定资产使用
     *
     * @param deviceToolInfoQuery
     * @return
     */
    IPage<DeviceToolInfo> getToolInfo4AssetPage(DeviceToolInfoQuery deviceToolInfoQuery);

    /**
     * TODO
     *
     * @param deviceToolInfo
     * @return
     */
    DeviceToolInfo createOrUpdateDeviceToolInfo(DeviceToolInfo deviceToolInfo);

    /**
     * TODO
     *
     * @param deviceToolInfo
     * @param deviceToolInfoId
     * @return
     */
    DeviceToolInfo updateDeviceToolInfo(String deviceToolInfoId, DeviceToolInfo deviceToolInfo);

    /**
     * TODO
     *
     * @param deviceToolInfoId
     * @return
     */
    void deleteDeviceToolInfo(String deviceToolInfoId);


    GeneralInformationVO findGeneralInformation(GeneralInformationForm informationForm);

    /**
     * 查看配套工具
     *
     * @param informationForm
     * @return
     */
    SupportingInformationVO findSupportingInformation(GeneralInformationForm informationForm);

    /**
     * 根据设备功能类型查询设备信息
     *
     * @param deviceToolInfoTypeQuery
     */
    IPage<DeviceToolInfo> findTooInfoBytoolModel(DeviceToolInfoTypeQuery deviceToolInfoTypeQuery);

    /**
     * 根据编码查询设备信息
     *
     * @param deviceToolInfoCodeQuery
     * @return
     */
    IPage<DeviceToolInfo> findTooInfoByToolCode(DeviceToolInfoCodeQuery deviceToolInfoCodeQuery);

    /**
     * 获取工序工具
     *
     * @param toolModels
     * @param toolCodes
     * @return java.util.List<com.huayun.modules.erp.mro.entity.DeviceToolInfo>
     * <AUTHOR>
     * @date 2022/9/15 11:12
     */
    List<DeviceToolInfo> getProcessToolList(String[] toolModels, String[] toolCodes);

    /***
     * 根据设备类型和设备型号查询设备
     * @param deviceTypeAndModelFrom
     * @return
     */
    IPage<DeviceToolInfo> findTypeAndModeTools(ModelTypeDeviceQuery deviceTypeAndModelFrom);

    /***
     * 查询通用类设备
     * @param modelTypeDeviceQuery
     * @return
     */
    IPage<DeviceToolInfo> findGeneralDeviceTools(DeviceToolModeQuery modelTypeDeviceQuery);

    /***
     * 通用类设备按编码查询工具信息
     * @param deviceToolModeQuery
     * @return
     */
    IPage<DeviceToolInfo> findToolCodeDeviceTools(DeviceToolCodeQuery deviceToolModeQuery);

    /***
     * 保养计划添加工具列表查询
     * @param deviceToolQuery
     * @return
     */
    IPage<DeviceToolInfo> findTools(DeviceToolQuery deviceToolQuery);

    /**
     * 查询型号信息
     *
     * @return
     */
    List<DeviceToolInfo> findModels();

    /**
     * 查询审核之后的数据
     *
     * @param id
     * @return
     */
    DeviceToolInfo selectDeviceToolInfos(String id);

    /**
     * 根据工具编码工具信息
     */
    DeviceToolInfo findDeviceToolInfoByCode(String code);

    /**
     * 根据产品ID查询配套工具
     *
     * @param modelTypeDeviceQuery
     * @return
     */
    IPage<DeviceToolInfo> findToolInfoByProductId(ModelTypeDeviceQuery modelTypeDeviceQuery);

    /**
     * 详情查询
     *
     * @param toolInfoId
     * @return
     */
    DeviceToolInfo findDeviceToolDetails(String toolInfoId);


    /**
     * 工具审核修改状态
     * @param deviceToolInfo 工具实体
     */
    void  updateToolStatus(DeviceToolInfo deviceToolInfo);

    /**
     * app工具查询
     * @return List<DeviceToolInfo>
     */
    List<DeviceToolInfo> findToolByApp();
    /**
     * 工具导入
     * @return List<ImportError>
     */
    List<ImportError> importDeviceTools(List<DeviceToolImportForm> deviceTools, Boolean coverFlag);

    IPage<DeviceToolInfo> selectToolGroupByModel(Page<DeviceToolInfo> page);

    /**
     * 修改状态
     *
     * @param id
     * @param code
     * @param status
     * <AUTHOR>
     * @date 2023/12/21 18:09
     */
    void updateDeviceStatus(String id, String code, DeviceTorsionStatusEnums status);
}
