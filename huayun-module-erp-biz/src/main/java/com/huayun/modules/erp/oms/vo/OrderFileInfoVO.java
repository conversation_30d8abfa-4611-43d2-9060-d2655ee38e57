package com.huayun.modules.erp.oms.vo;

import com.huayun.modules.common.entity.UploadFile;
import com.huayun.modules.erp.oms.constant.enums.OrderFileTypeEnum;
import com.huayun.modules.erp.oms.entity.OrderFileInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @description 订单附件
 * @date 2022/8/9 10:23
 **/
@Data
@NoArgsConstructor
@ApiModel(value = "订单附件", description = "订单附件")
public class OrderFileInfoVO {

    /**
     * id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 文件类型(枚举)
     */
    @ApiModelProperty(value = "文件类型(枚举)")
    private OrderFileTypeEnum fileType;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private UploadFile attachment;
    
    public OrderFileInfoVO(ContractFileInfoVO contractFileInfoVO) {
        BeanUtils.copyProperties(contractFileInfoVO, this);
        if (attachment != null) {
            attachment.setDesc(contractFileInfoVO.getTitle());
        }
    }

    public OrderFileInfoVO(OrderFileInfo orderFileInfo) {
        init(orderFileInfo);
    }

    private void init(OrderFileInfo orderFileInfo) {
        BeanUtils.copyProperties(orderFileInfo, this);
    }
}
