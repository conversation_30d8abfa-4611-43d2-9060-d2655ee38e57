package com.huayun.modules.erp.pdm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.erp.pdm.enums.ProductPriceRecordTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2024/12/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "pdm_product_price_record", autoResultMap = true)
@ApiModel(value = "pdm_product_price_record对象", description = "产品价格记录")
public class ProductPriceRecord extends BaseTenantEntity {

    @ApiModelProperty(value = "产品id")
    private String productId;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "调整前价格")
    private BigDecimal oldPrice;

    @ApiModelProperty(value = "价格类型")
    private ProductPriceRecordTypeEnum type;
}
