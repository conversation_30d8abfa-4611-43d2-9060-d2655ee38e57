package com.huayun.modules.erp.wms.controller.receipt.returns;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.common.log.aspect.annotation.Log;
import com.huayun.modules.common.rule.validate.ValidationGroup;
import com.huayun.modules.erp.oms.constant.OMSConstant;
import com.huayun.modules.erp.oms.entity.OrderReturn;
import com.huayun.modules.erp.oms.entity.model.OrderReturnModel;
import com.huayun.modules.erp.oms.query.OrderReturnQuery;
import com.huayun.modules.erp.oms.service.IOrderReturnService;
import com.huayun.modules.erp.oms.vo.ListOrderReturnVO;
import com.huayun.modules.erp.oms.vo.OrderReturnVO;
import com.huayun.modules.erp.wms.entity.OrderReturnQualityControl;
import com.huayun.modules.erp.wms.form.OrderReturnQualityControlForm;
import com.huayun.modules.erp.wms.service.qc.IOrderReturnQualityControlService;
import com.huayun.modules.erp.wms.service.receipt.IReturnReceiptService;
import com.huayun.modules.erp.wms.vo.OrderReturnQualityControlVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户退货管理
 *
 * <AUTHOR>
 * @date   2022/09/08
 */
@Slf4j
@Api(tags="WMS-客户退货管理")
@RestController
@RequestMapping("/wms/return-receipts")
public class ReturnReceiptController {

    @Resource
    private IReturnReceiptService returnReceiptService;

    @Resource
    private IOrderReturnService orderReturnService;

    @Resource
    private IOrderReturnQualityControlService orderReturnQualityControlService;


    /**
     * 分页列表查询
     *
     * <AUTHOR>
     * @date 2023/3/7 16:54
     * @param query
     * @return org.jeecg.common.api.vo.Result<com.baomidou.mybatisplus.core.metadata.IPage<com.huayun.modules.erp.oms.vo.ListOrderReturnVO>>
     */
    @ApiOperation(value = "分页列表查询", notes = "分页列表查询")
    @GetMapping
    public Result<IPage<ListOrderReturnVO>> queryPageList(OrderReturnQuery query) {
        Page<OrderReturn> page = new Page<>(query.getPage(), query.getPageSize());
        IPage<ListOrderReturnVO> pageList = orderReturnService.page(page, query).convert(ListOrderReturnVO::new);
        return Result.OK("查询成功！", pageList);
    }

    /**
     * 根据id查询
     *
     * <AUTHOR>
     * @date 2023/3/7 17:17
     * @param id
     * @return org.jeecg.common.api.vo.Result<com.huayun.modules.erp.oms.vo.OrderReturnVO>
     */
    @ApiOperation(value = "根据id查询", notes = "根据id查询")
    @GetMapping("/{id}")
    public Result<OrderReturnVO> getById(@PathVariable String id){
        OrderReturnModel model = orderReturnService.getDetailById(id);
        return Result.OK("查询成功！", new OrderReturnVO(model));
    }

    /**
     * 根据退货清单id查询质检记录列表
     *
     * <AUTHOR>
     * @date 2023/3/8 18:33
     * @param itemId
     * @return org.jeecg.common.api.vo.Result<java.util.List<com.huayun.modules.erp.wms.vo.OrderReturnQualityControlVO>>
     */
    @ApiOperation(value = "根据退货清单id查询质检记录列表", notes = "根据退货清单id查询质检记录列表")
    @GetMapping("/item-qc")
    public Result<List<OrderReturnQualityControlVO>> findQcList(@RequestParam(name = "itemId") String itemId){
        List<OrderReturnQualityControlVO> list = orderReturnQualityControlService.findListByItemId(itemId)
                .stream()
                .map(OrderReturnQualityControlVO::new)
                .collect(Collectors.toList());
        return Result.OK("查询成功！", list);
    }

    /**
     * 质检报告提交
     *
     * <AUTHOR>
     * @date 2023/3/8 20:36
     * @param from
     * @return org.jeecg.common.api.vo.Result<?>
     */
    @ApiOperation(value = "质检报告提交", notes = "质检报告提交")
    @PostMapping("/item-qc")
    public Result<?> qcSubmit(@Validated(ValidationGroup.Create.class)
                              @RequestBody OrderReturnQualityControlForm from){
        OrderReturnQualityControl control = new OrderReturnQualityControl();
        BeanUtils.copyProperties(from, control);
        orderReturnQualityControlService.save(control);

        return Result.OK("提交成功！");
    }

    /**
     * 质检报告修改
     *
     * <AUTHOR>
     * @date 2023/3/8 20:36
     * @param from
     * @return org.jeecg.common.api.vo.Result<?>
     */
    @ApiOperation(value = "质检报告修改", notes = "质检报告修改")
    @PutMapping("/item-qc")
    public Result<?> qcEdit(@Validated(ValidationGroup.Update.class)
                            @RequestBody OrderReturnQualityControlForm from){
        OrderReturnQualityControl control = new OrderReturnQualityControl();
        BeanUtils.copyProperties(from, control);
        orderReturnQualityControlService.save(control);

        return Result.OK("修改成功！");
    }

    /**
     * 质检报告删除
     *
     * <AUTHOR>
     * @date 2023/3/8 20:36
     * @param qcId
     * @return org.jeecg.common.api.vo.Result<?>
     */
    @ApiOperation(value = "质检报告删除", notes = "质检报告删除")
    @DeleteMapping("/item-qc")
    public Result<?> qcDelete(@RequestParam(name = "qcId") String qcId){
        orderReturnQualityControlService.delete(qcId);

        return Result.OK("删除成功！");
    }

    /**
     * 完成质检
     *
     * <AUTHOR>
     * @date 2023/3/9 15:09
     * @param id
     * @return org.jeecg.common.api.vo.Result<com.huayun.modules.erp.oms.vo.OrderReturnVO>
     */
    @Log(resourceType = OMSConstant.RESOURCE_CUSTOMER_RETURN, operateType = "完成质检", template = "退货单据完成质检，退货单号：“${code}”")
    @ApiOperation(value = "完成质检", notes = "完成质检")
    @PostMapping("/{id}/qc-complete")
    public Result<OrderReturnVO> qcComplete(@PathVariable String id){
        OrderReturn orderReturn = returnReceiptService.qcComplete(id);
        return Result.OK("完成质检！", new OrderReturnVO(orderReturn));
    }

    /**
     * 确认入库
     *
     * <AUTHOR>
     * @date 2023/3/9 15:09
     * @param id
     * @return org.jeecg.common.api.vo.Result<com.huayun.modules.erp.oms.vo.OrderReturnVO>
     */
    @Log(resourceType = OMSConstant.RESOURCE_CUSTOMER_RETURN, operateType = "确认入库", template = "退货单据确认入库，退货单号：“${code}”")
    @ApiOperation(value="确认入库", notes="确认入库")
    @PostMapping("/{id}/storage-complete")
    public Result<OrderReturnVO> storageComplete(@PathVariable String id) {
        OrderReturn orderReturn = returnReceiptService.storageComplete(id);
        return Result.OK("确认入库成功！", new OrderReturnVO(orderReturn));
    }

}