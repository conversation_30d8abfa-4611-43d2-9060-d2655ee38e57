package com.huayun.modules.erp.mro.query;

import com.huayun.modules.common.mybatis.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO description
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceMaintenanceTemplateQuery extends BaseQuery {
    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;
    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    /**
     * 设备或者工具类型
     */
    @ApiModelProperty(value = "设备或者工具类型")
    private String templateDeviceType;
    /**
     * 模板名称
     */
    @ApiModelProperty(value = "查询类型（0设备，1工具）")
    private String templateFindType;
}
