package com.huayun.modules.erp.oams.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huayun.modules.common.auth.Session;
import com.huayun.modules.common.entity.DictData;
import com.huayun.modules.common.service.IDictDataService;
import com.huayun.modules.common.util.CodeGenerator;
import com.huayun.modules.erp.oams.entity.AccountingTitle;
import com.huayun.modules.erp.oams.mapper.AccountingTitleMapper;
import com.huayun.modules.erp.oams.service.IAccountingTitleService;
import com.huayun.modules.erp.oams.vo.AccountingTitleQueryVO;
import com.huayun.modules.erp.oams.vo.AccountingTitleVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 会计科目
 * @Author: jeecg-boot
 * @Date:   2023-07-20
 * @Version: V1.0
 */
@Service
public class AccountingTitleServiceImpl extends ServiceImpl<AccountingTitleMapper, AccountingTitle> implements IAccountingTitleService {
	@Resource
	private IDictDataService dictDataService;

	@Resource
	private CodeGenerator codeGenerator;

	private long INIT_STEP = 200;
	/**
	 * getAccountingTitle:查询会计科目.
	 * 1. 一级科目从数据字典查出来.
	 * 2. 二级科目从科目表oams_accounting_title查询
	 *
	 * <AUTHOR>
	 * @return
	 * @since version 1.0
	 */
	@Override
	public List<AccountingTitleVO> getAccountingTitle(String type){
		QueryWrapper<AccountingTitle> query = new QueryWrapper<>();
		query.eq("type", type);
		query.orderByAsc("code");
		//List<DictData> dicts = dictDataService.findByDictType("OAMS_ACCOUNTING_TITLE_"+type);
		//List<AccountingTitle> dicts = new ArrayList<>();
		List<AccountingTitle> at2s = this.list(query);
		List<AccountingTitleVO> res = new ArrayList<>();
		for(AccountingTitle dict : at2s) {
			if(!new Integer(1).equals(dict.getLevel()) ) {
				continue;
			}
			AccountingTitleVO level1 = new AccountingTitleVO();
			BeanUtils.copyProperties(dict, level1);
			level1.setChildren(new ArrayList<>());
			setSun(at2s, level1, 2);

			res.add(level1);
		}

		return res;
	}

	private void setSun(List<AccountingTitle> at2s, AccountingTitleVO level1, Integer level) {
		for(AccountingTitle at : at2s) {
			if(at.getLevel()!=level) {
				continue;
			}
			AccountingTitleVO son = new AccountingTitleVO();

			BeanUtils.copyProperties(at, son);
			if(at.getParentCode().equals(level1.getCode())) {
				if(level1.getChildren()==null) {
					level1.setChildren(new ArrayList<>());
				}
				level1.getChildren().add(son);
			}
			setSun(at2s, son, level+1);
		}
	}

	/**
	 * createAccountingTitle:添加会计科目.
	 *
	 * <AUTHOR>
	 * @param accountingTitle
	 * @return
	 * @since version 1.0
	 */
	@Override
	public AccountingTitleVO createAccountingTitle(AccountingTitleVO accountingTitleVO) {
		AccountingTitle accountingTitle = new AccountingTitle();
		BeanUtils.copyProperties(accountingTitleVO, accountingTitle);
		accountingTitle.setCanedit(true);
		accountingTitle.setStatus(1);

		this.save(accountingTitle);
		return accountingTitleVO;
	}

	/**
	 * createAccountingTitle:修改会计科目.
	 *
	 * <AUTHOR>
	 * @param accountingTitle
	 * @return
	 * @since version 1.0
	 */
	@Override
	public AccountingTitleVO updAccountingTitle(AccountingTitleVO accountingTitleVO) {
		AccountingTitle accountingTitleOld = this.getById(accountingTitleVO.getId());
		//判断是否修改了父级
		if(!accountingTitleOld.getParentCode().equals(accountingTitleVO.getParentCode())) {
			DictData parent = dictDataService.getById(accountingTitleVO.getParentCode());
			accountingTitleOld.setCode(codeGenerator.sequence(parent.getDictCode(), Long.parseLong(parent.getDictCode())+INIT_STEP).toString());
			accountingTitleOld.setParentCode(accountingTitleVO.getParentCode());
		}
		accountingTitleOld.setName(accountingTitleVO.getName());
		accountingTitleOld.setRemark(accountingTitleVO.getRemark());
		this.updateById(accountingTitleOld);
		return accountingTitleVO;
	}

	/**
	 * initTenantData:初始化租户数据.
	 *
	 * <AUTHOR>
	 * @param userId
	 * @param tenantId
	 * @since version 1.0
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void initTenantData(String userId, String tenantId, Session session) {
		session.setTenant("1");
		List<AccountingTitle> templates = this.list(); //查出模版
		session.setTenant(tenantId);
		for(AccountingTitle title : templates) {
			title.setId(null);
			title.setTenantId(tenantId);
		}
		this.saveBatch(templates);
	}

	@Override
	public void saveAccountingTitle(AccountingTitleVO form) {
		List<AccountingTitle> at2s = this.list();
		for(AccountingTitle at : at2s) {
			if(form.getIds().contains(at.getId())) {
				at.setStatus(1);
			}else {
				at.setStatus(0);
			}
		}
		this.saveOrUpdateBatch(at2s);
	}

	/**
	 * getAccountingTitle:查询会计科目.
	 * 1. 一级科目从数据字典查出来.
	 * 2. 二级科目从科目表oams_accounting_title查询
	 *
	 * <AUTHOR>
	 * @return
	 * @since version 1.0
	 */
	@Override
	public List<AccountingTitleVO> getAccountingTitle(AccountingTitleQueryVO queryVo){

		QueryWrapper<AccountingTitle> query = new QueryWrapper<>();
		query.eq("type", queryVo.getType());
		//List<DictData> dicts = dictDataService.findByDictType("OAMS_ACCOUNTING_TITLE_"+type);
		//List<AccountingTitle> dicts = new ArrayList<>();
		List<AccountingTitle> at2s = this.list(query);
		List<AccountingTitleVO> res = new ArrayList<>();
		for(AccountingTitle dict : at2s) {
			if(!new Integer(1).equals(dict.getLevel()) ) {
				continue;
			}
			AccountingTitleVO level1 = new AccountingTitleVO();
			BeanUtils.copyProperties(dict, level1);
			level1.setChildren(new ArrayList<>());
			setSun(at2s, level1, 2);

			res.add(level1);
		}

		return res;

	}
}
