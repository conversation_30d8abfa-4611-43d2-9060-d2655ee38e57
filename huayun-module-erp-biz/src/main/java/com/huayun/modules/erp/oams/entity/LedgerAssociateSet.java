package com.huayun.modules.erp.oams.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import com.huayun.modules.erp.oams.constant.enums.LedgerAssoSetTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 科目关联设置表
*
* <AUTHOR>
* @date   2024/07/18
*/
@Data
@TableName("oams_ledger_associate_set")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oams_ledger_associate_set对象", description="科目关联设置表")
public class LedgerAssociateSet extends BaseTenantEntity {
    
	/**类型：存货、客户、供应商 */
    @ApiModelProperty(value = "类型：存货、客户、供应商 ")
	private LedgerAssoSetTypeEnum type;
	/**名称*/
    @ApiModelProperty(value = "名称")
	private String name;
	/**编码*/
    @ApiModelProperty(value = "编码")
	private String code;
	/**
	 * 科目编码1
	 */
	@ApiModelProperty(value = "科目编码1 存货：存货科目 客户：应收账款科目 供应商：应付账款科目")
	private String ledgerCode1;
	/**
	 * 科目编码2
	 */
	@ApiModelProperty(value = "科目编码2 存货：收入科目 客户：预收账款科目 供应商：预付账款科目")
	private String ledgerCode2;
	/**
	 * 科目编码3
	 */
	@ApiModelProperty(value = "科目编码3 存货：成本科目 客户：其他应收科目 供应商：其他应付款科目")
	private String ledgerCode3;


	/**是否为默认值 0-否 1-是*/
    @ApiModelProperty(value = "是否为默认值 0-否 1-是")
	private Boolean defaultFlag;
	/**状态 0-正常 1-停用*/
    @ApiModelProperty(value = "状态 0-正常 1-停用")
	private Integer status;
	/**账套ID*/
    @ApiModelProperty(value = "账套ID")
	private String acId;

	/**
	 * 科目编码1
	 */
	@ApiModelProperty(value = "科目名称1")
	private transient String ledgerName1;
	/**
	 * 科目编码2
	 */
	@ApiModelProperty(value = "科目名称2")
	private transient String ledgerName2;
	/**
	 * 科目编码3
	 */
	@ApiModelProperty(value = "科目名称3")
	private transient String ledgerName3;
}
