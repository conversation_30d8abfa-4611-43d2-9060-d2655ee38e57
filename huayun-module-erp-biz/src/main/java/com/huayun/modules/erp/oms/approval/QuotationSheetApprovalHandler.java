package com.huayun.modules.erp.oms.approval;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.huayun.modules.common.approval.ApprovalHandler;
import com.huayun.modules.common.approval.dao.ApprovalLogDAO;
import com.huayun.modules.common.approval.entity.Approval;
import com.huayun.modules.erp.oms.constant.OMSConstant;
import com.huayun.modules.erp.oms.service.IQuotationSheetService;
import com.huayun.modules.erp.oms.vo.QuotationSheetVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class QuotationSheetApprovalHandler implements ApprovalHandler {
    @Resource
    IQuotationSheetService quotationSheetService;
    @Resource
    ObjectMapper objectMapper;

    @Resource
    private ApprovalLogDAO approvalLogDAO;

    @Override
    public String resourceType() {
        return OMSConstant.RESOURCE_QUOTATION;
    }

    @Override
    public List<Approval.Property> getProperties(Object resource) {
        if (resource instanceof QuotationSheetVo) {
            QuotationSheetVo vo = (QuotationSheetVo) resource;
            QuotationSheetVo old = vo.getOldVo();
            List<Approval.Property> list = new ArrayList<>();
            if (old != null) {
                // 变更的时候才有
                if (!old.getClause().equals(vo.getClause())) {
                    list.add(new Approval.Property("clause", "重要条款", old.getClause(), vo.getClause()));
                }
                if (!old.getCustomerName().equals(vo.getCustomerName())) {
                    list.add(new Approval.Property("customerName", "客户名称", old.getCustomerName(), vo.getCustomerName()));
                }
                if (!old.getCurrency().equals(vo.getCurrency())) {
                    list.add(new Approval.Property("currency", "币别", old.getCurrency(), vo.getCurrency()));
                }
                if (!old.getVatRates().equals(vo.getVatRates())) {
                    list.add(new Approval.Property("vatRates", "增值税率", old.getVatRates(), vo.getVatRates()));
                }
                if (!Objects.equals(old.getSupervisorName(), vo.getSupervisorName())) {
                    list.add(new Approval.Property("supervisorName", "业务员", old.getSupervisorName(), vo.getSupervisorName()));
                }
                if (!old.getState().equals(vo.getState())) {
                    list.add(new Approval.Property("state", "状态", old.getState().getLabel(), vo.getState().getLabel()));
                }
                if (old.getItems() != vo.getItems()) {
                    list.add(new Approval.Property("_items", "产品", toJsonArrayString(old.getItems()), toJsonArrayString(vo.getItems())));
                }
            }
            // 这里加一个空的属性，不然审批的时候修改了上面没有加的属性会修改失败
            list.add(new Approval.Property("_", "", "", ""));
            return list;
        }
        return Collections.emptyList();
    }

    public String toJsonArrayString(Object object) {
        if (object == null) {
            return "[]";
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("序列化转json失败", e);
        }
        return "[]";
    }

    @Override
    public boolean isAutoCommit(String methodName, Object[] args) {
        return !"change".equals(methodName);
    }

    @Override
    public Map<String, String> extraColumns(Object resource) {
        return quotationSheetService.extraColumns(resource);
    }

    @Override
    public void pending(Approval approval) {
        quotationSheetService.pending(approval);
    }

    @Override
    public void approved(Approval approval) {
        quotationSheetService.approved(approval);
    }

    @Override
    public void approved(Object[] args, Object object) {
        quotationSheetService.approved(args, object);
    }

    @Override
    public void rejected(Approval approval) {
        quotationSheetService.rejected(approval);
    }

}
