/**
 * Project Name:huayun-module-erp-biz
 * File Name:DataPermissionInterceptor.java
 * Package Name:com.huayun.modules.erp.oams.interceptor
 * Date:2023年12月22日下午5:10:14
 * Copyright (c) 2023, <EMAIL> All Rights Reserved.
 *
*/

package com.huayun.modules.erp.oams.interceptor;

import com.baomidou.mybatisplus.core.toolkit.*;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.huayun.modules.common.auth.context.SessionContext;
import com.huayun.modules.common.exception.BadRequestException;
import com.huayun.modules.erp.oams.service.IAcSetService;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.expression.operators.relational.MultiExpressionList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.util.*;
/**
 * ClassName:DataPermissionInterceptor
 * Function: TODO ADD FUNCTION.
 * Reason:   TODO ADD REASON.
 * Date:     2023年12月22日 下午5:10:14
 * <AUTHOR>
 * @version
 * @since    version 1.0
 * @see
 */
@Slf4j
@Component
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class DataPermissionInterceptor extends TenantLineInnerInterceptor implements Interceptor{

    private final static String AC_ID = "ac_id";

    @Lazy
    @Resource
    private IAcSetService acSetService;

    private static Set<String> tables = new HashSet<String>();

    static {
    	tables.add("oams_ledger_account");
    	tables.add("oams_voucher");
    	tables.add("oams_ledger_account_init");
    	tables.add("oams_voucher_detail");
    	tables.add("oams_ledger_account_init_month");
        tables.add("oams_ledger_account_detail");

    	tables.add("oams_voucher_auxiliary");
    	tables.add("oams_voucher_auxiliary_month");
    	tables.add("oams_amout_share_month");
    	tables.add("oams_amout_share_month_detail");
    	tables.add("oams_account_colse");
    	tables.add("oams_voucher_estimate");
        tables.add("oams_ledger_associate_set");
        tables.add("oams_voucher_template");
        tables.add("oams_voucher_associate_biz");
        tables.add("oams_voucher_wash_transaction");
        tables.add("oams_daily_book");
        tables.add("oams_internal_transfer");
        tables.add("oams_voucher_associate_lot");

    	//tables.add("oams_voucher_estimate_log");
    }

    @Override
    public Object intercept(Invocation invocation) throws BadRequestException, JSQLParserException, InvocationTargetException, IllegalAccessException  {
    	String userId = SessionContext.getPrincipal();
        if (userId == null){
        	return invocation.proceed();
        }

        // 拿到mybatis的一些对象
        StatementHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

        // mappedStatement.getId()为执行的mapper方法的全路径名,newId为执行的mapper方法的类全名
        String newId = mappedStatement.getId().substring(0, mappedStatement.getId().lastIndexOf("."));
        // 如果不是指定的方法，直接结束拦截
        //if (!classNames.contains(newId)) {
           // return invocation.proceed();
        //}
        String newName = mappedStatement.getId().substring(mappedStatement.getId().lastIndexOf(".") + 1, mappedStatement.getId().length());
        //if(newId.contains("LedgerAccountInitMonthMapper")) {
        	//System.out.println(newName);
        //}
        //是否开启数据权限
        boolean isPermi = false;
        Class<?> clazz = null;
		try {
			clazz = Class.forName(newId);
		} catch (ClassNotFoundException e) {

		}
        //遍历方法
        if(clazz.isAnnotationPresent(DataPermission.class)) {
        	/*for (Method method : clazz.getDeclaredMethods()) {
                //方法是否含有DataPermission注解，如果含有注解则将数据结果过滤
                if (method.isAnnotationPresent(DataPermission.class) && newName.equals(method.getName())) {
                    DataPermission dataPermission =  method.getAnnotation(DataPermission.class);
                    if (dataPermission != null) {
                        //不验证
                        if (!dataPermission.isPermi()) {
                            isPermi = false;
                        } else { //开启验证
                            isPermi = true;
                        }
                    }
                }
                if(newName.contains("select")) {
                	isPermi = true;
                }
                else if(newName.contains("delete")) {
                	isPermi = true;
                }
                else if(newName.contains("insert")) {
                	isPermi = true;
                }
                else if(newName.contains("update")) {
                	isPermi = true;
                }
            }*/

        	DataPermission dataPermission =  clazz.getAnnotation(DataPermission.class);
            if (dataPermission != null) {
                //不验证
                if (!dataPermission.isPermi()) {
                    isPermi = false;
                } else { //开启验证
                    isPermi = true;
                }
            }
        }


        if (isPermi){
        	 String sql = statementHandler.getBoundSql().getSql();

        	//根据当前用户获取一次他的账套
            String acId = acSetService.getAcSetId(userId);
			/**try {
				acId = acSetService.getAcSetId(userId);
			} catch (BadRequestException e) {
				throw new BadRequestException("当前账号没有设置账套!");
			}**/
            // 获取到原始sql语句
            Statement statement = CCJSqlParserUtil.parse(sql);

            // 解析并返回新的SQL语句
            if (statement instanceof Select) {
            	if(acId == null) {
                	throw new BadRequestException("当前账号没有设置账套!");
                }
                sql = getSql(sql,acId);
            } else {
            	if(sql.contains(AC_ID)) {
            		return invocation.proceed();
            	}
            	if(acId == null) {
                	throw new BadRequestException("当前账号没有设置账套!");
                }
            	sql = processParser(statement, 0 , acId);
            }

            // 修改sql
            metaObject.setValue("delegate.boundSql.sql", sql);
        } else {
        	return invocation.proceed();
        }
        return invocation.proceed();


    }


    /**
     * 执行 SQL 解析
     *
     * @param statement JsqlParser Statement
     * @return sql
     */
    protected String processParser(Statement statement, int index, String acId) {

        if (statement instanceof Insert) {
            this.processInsert((Insert) statement, index, acId);
        } else if (statement instanceof Update) {
            this.processUpdate((Update) statement, index, acId);
        } else if (statement instanceof Delete) {
            this.processDelete((Delete) statement, index, acId);
        }
        String sql = statement.toString();
        if (logger.isDebugEnabled()) {
            logger.debug("parse the finished SQL: " + sql);
        }
        return sql;
    }


	/**
     * 解析SQL语句，并返回新的SQL语句
     *
     * @param sql 原SQL
     * @return 新SQL
     */
    private String getSql(String sql,String acId) {

        try {
            String condition = AC_ID +" = '" + acId+"'";

            if (StringUtils.isBlank(condition)){
                return sql;
            }
            Select select = (Select)CCJSqlParserUtil.parse(sql);
            PlainSelect plainSelect = (PlainSelect)select.getSelectBody();
            //plainSelect.getJoins()

            Expression where = plainSelect.getWhere();
            processWhereSubSelect(where);
         // 处理 fromItem
            FromItem fromItem = plainSelect.getFromItem();
            List<Table> list = processFromItem(fromItem, acId);
            List<Table> mainTables = new ArrayList<>(list);

            // 处理 join
            List<Join> joins = plainSelect.getJoins();
            if (CollectionUtils.isNotEmpty(joins)) {
                mainTables = processJoins(mainTables, joins, acId);
            }

            // 当有 mainTable 时，进行 where 条件追加
            if (CollectionUtils.isNotEmpty(joins)) { //多表得查询
                plainSelect.setWhere(builderExpression(where, mainTables, acId));
            } else {              //单表得查询
            	//取得原SQL的where条件
                final Expression expression = plainSelect.getWhere();
                //增加新的where条件
                final Expression envCondition = CCJSqlParserUtil.parseCondExpression(condition);
                if (expression == null) {
                    plainSelect.setWhere(envCondition);
                } else {
                    AndExpression andExpression = new AndExpression(expression, envCondition);
                    plainSelect.setWhere(andExpression);
                }
            }


            return plainSelect.toString();
        } catch (JSQLParserException e) {
            log.error("解析原SQL并构建新SQL错误：" + e);
            return sql;
        }
    }

    /**
     * 处理 joins
     *
     * @param mainTables 可以为 null
     * @param joins      join 集合
     * @return List<Table> 右连接查询的 Table 列表
     */
    private List<Table> processJoins(List<Table> mainTables, List<Join> joins, String acId) {
        // join 表达式中最终的主表
        Table mainTable = null;
        // 当前 join 的左表
        Table leftTable = null;

        if (mainTables == null) {
            mainTables = new ArrayList<>();
        } else if (mainTables.size() == 1) {
            mainTable = mainTables.get(0);
            leftTable = mainTable;
        }

        //对于 on 表达式写在最后的 join，需要记录下前面多个 on 的表名
        Deque<List<Table>> onTableDeque = new LinkedList<>();
        for (Join join : joins) {
            // 处理 on 表达式
            FromItem joinItem = join.getRightItem();

            // 获取当前 join 的表，subJoint 可以看作是一张表
            List<Table> joinTables = null;
            if (joinItem instanceof Table) {
                joinTables = new ArrayList<>();
                joinTables.add((Table) joinItem);
            } else if (joinItem instanceof SubJoin) {
                joinTables = processSubJoin((SubJoin) joinItem, acId);
            }

            if (joinTables != null) {

                // 如果是隐式内连接
                if (join.isSimple()) {
                    mainTables.addAll(joinTables);
                    continue;
                }

                // 当前表是否忽略
                Table joinTable = joinTables.get(0);

                List<Table> onTables = null;
                // 如果不要忽略，且是右连接，则记录下当前表
                if (join.isRight()) {
                    mainTable = joinTable;
                    if (leftTable != null) {
                        onTables = Collections.singletonList(leftTable);
                    }
                } else if (join.isLeft()) {
                     onTables = Collections.singletonList(joinTable);
                } else if (join.isInner()) {
                    if (mainTable == null) {
                        onTables = Collections.singletonList(joinTable);
                    } else {
                        onTables = Arrays.asList(mainTable, joinTable);
                    }
                    mainTable = null;
                }

                mainTables = new ArrayList<>();
                if (mainTable != null) {
                    mainTables.add(mainTable);
                }

                // 获取 join 尾缀的 on 表达式列表
                Collection<Expression> originOnExpressions = join.getOnExpressions();
                // 正常 join on 表达式只有一个，立刻处理
                if (originOnExpressions.size() == 1 && onTables != null) {
                    List<Expression> onExpressions = new LinkedList<>();
                    onExpressions.add(builderExpression(originOnExpressions.iterator().next(), onTables, acId));
                    join.setOnExpressions(onExpressions);
                    leftTable = joinTable;
                    continue;
                }
                // 表名压栈，忽略的表压入 null，以便后续不处理
                onTableDeque.push(onTables);
                // 尾缀多个 on 表达式的时候统一处理
                if (originOnExpressions.size() > 1) {
                    Collection<Expression> onExpressions = new LinkedList<>();
                    for (Expression originOnExpression : originOnExpressions) {
                        List<Table> currentTableList = onTableDeque.poll();
                        if (CollectionUtils.isEmpty(currentTableList)) {
                            onExpressions.add(originOnExpression);
                        } else {
                            onExpressions.add(builderExpression(originOnExpression, currentTableList, acId));
                        }
                    }
                    join.setOnExpressions(onExpressions);
                }
                leftTable = joinTable;
            } else {
                processOtherFromItem(joinItem);
                leftTable = null;
            }
        }

        return mainTables;
    }

    private List<Table> processFromItem(FromItem fromItem, String acId) {
        // 处理括号括起来的表达式
        while (fromItem instanceof ParenthesisFromItem) {
            fromItem = ((ParenthesisFromItem) fromItem).getFromItem();
        }

        List<Table> mainTables = new ArrayList<>();
        // 无 join 时的处理逻辑
        if (fromItem instanceof Table) {
            Table fromTable = (Table) fromItem;
            mainTables.add(fromTable);
        } else if (fromItem instanceof SubJoin) {
            // SubJoin 类型则还需要添加上 where 条件
            List<Table> tables = processSubJoin((SubJoin) fromItem, acId);
            mainTables.addAll(tables);
        } else {
            // 处理下 fromItem
            processOtherFromItem(fromItem);
        }
        return mainTables;
    }

    /**
     * 处理 sub join
     *
     * @param subJoin subJoin
     * @return Table subJoin 中的主表
     */
    private List<Table> processSubJoin(SubJoin subJoin, String acId) {
        List<Table> mainTables = new ArrayList<>();
        if (subJoin.getJoinList() != null) {
            List<Table> list = processFromItem(subJoin.getLeft(), acId);
            mainTables.addAll(list);
            mainTables = processJoins(mainTables, subJoin.getJoinList(), acId);
        }
        return mainTables;
    }


    protected Expression builderExpression(Expression currentExpression, List<Table> tables, String acId) {
        // 没有表需要处理直接返回
        if (CollectionUtils.isEmpty(tables)) {
            return currentExpression;
        }
        // 构造每张表的条件
        List<Expression> equalsTos = new ArrayList<>();
        for(Table table : tables) {
        	if(includeTable(table)) {
        		equalsTos.add(builderExpression(table, acId));
        	}
        }
        if (ObjectUtils.isEmpty(equalsTos)) {
            return currentExpression;
        }

        // 注入的表达式
        Expression injectExpression = equalsTos.get(0);
        // 如果有多表，则用 and 连接
        if (equalsTos.size() > 1) {
            for (int i = 1; i < equalsTos.size(); i++) {
                injectExpression = new AndExpression(injectExpression, equalsTos.get(i));
            }
        }

        if (currentExpression == null) {
            return injectExpression;
        }
        if (currentExpression instanceof OrExpression) {
            return new AndExpression(new Parenthesis(currentExpression), injectExpression);
        } else {
            return new AndExpression(currentExpression, injectExpression);
        }
    }

    private Expression builderExpression(Table table, String acId) {

        return new EqualsTo(this.getAliasColumn(table), new StringValue(acId));
    }

    /**
     * 租户字段别名设置
     * <p>tenantId 或 tableAlias.tenantId</p>
     *
     * @param table 表对象
     * @return 字段
     */
    protected Column getAliasColumn(Table table) {
        StringBuilder column = new StringBuilder();
        // 为了兼容隐式内连接，没有别名时条件就需要加上表名
        if (table.getAlias() != null) {
            column.append(table.getAlias().getName());
        } else {
            column.append(table.getName());
        }
        column.append(StringPool.DOT).append(AC_ID);
        return new Column(column.toString());
    }

    private boolean includeTable(Table table) {
        //TenantLineHandler tenantLineHandler = getTenantLineHandler();
        return tables.contains(table.getName());
    }



    protected void processInsert(Insert insert, int index, String acId) {
    	if (!tables.contains(insert.getTable().getName())) {
            // 过滤退出执行
            return;
        }
        List<Column> columns = insert.getColumns();
        if (CollectionUtils.isEmpty(columns)) {
            // 针对不给列名的insert 不处理
            return;
        }
        String tenantIdColumn = AC_ID;

        columns.add(new Column(tenantIdColumn));

        List<Expression> duplicateUpdateColumns = insert.getDuplicateUpdateExpressionList();
        if (CollectionUtils.isNotEmpty(duplicateUpdateColumns)) {
            EqualsTo equalsTo = new EqualsTo();
            equalsTo.setLeftExpression(new StringValue(tenantIdColumn));
            equalsTo.setRightExpression(new StringValue(acId));
            duplicateUpdateColumns.add(equalsTo);
        }

        Select select = insert.getSelect();
        if (select != null) {
            this.processInsertSelect(select.getSelectBody());
        } else if (insert.getItemsList() != null) {
            // fixed github pull/295
            ItemsList itemsList = insert.getItemsList();
            if (itemsList instanceof MultiExpressionList) {
                ((MultiExpressionList) itemsList).getExpressionLists().forEach(el -> el.getExpressions().add(new StringValue(acId)));
            } else {
                ((ExpressionList) itemsList).getExpressions().add(new StringValue(acId));
            }
        } else {
            throw ExceptionUtils.mpe("Failed to process multiple-table update, please exclude the tableName or statementId");
        }
    }

    /**
     * update 语句处理
     */
    protected void processUpdate(Update update, int index, String acId) {
    	final Table table = update.getTable();
        if (!tables.contains(table.getName())) {
            // 过滤退出执行
            return;
        }
        update.setWhere(this.andExpression(table, update.getWhere(), acId));

        //System.out.println(update.toString());
    }

    /**
     * delete 语句处理
     */
    protected void processDelete(Delete delete, int index, String acId) {
    	if (!tables.contains(delete.getTable().getName())) {
            // 过滤退出执行
            return;
        }
        delete.setWhere(this.andExpression(delete.getTable(), delete.getWhere(), acId));
    }

    /**
     * delete update 语句 where 处理
     */
    protected BinaryExpression andExpression(Table table, Expression where, String acId) {
        //获得where条件表达式
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(this.getAliasColumn(table));
        equalsTo.setRightExpression(new StringValue(acId) );
        if (null != where) {
            if (where instanceof OrExpression) {
                return new AndExpression(equalsTo, new Parenthesis(where));
            } else {
                return new AndExpression(equalsTo, where);
            }
        }
        return equalsTo;
    }
}

