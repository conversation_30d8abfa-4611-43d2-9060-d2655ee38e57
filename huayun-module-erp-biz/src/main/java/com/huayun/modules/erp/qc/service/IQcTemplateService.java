package com.huayun.modules.erp.qc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huayun.modules.erp.qc.dto.QcProcessTemplateDTO;
import com.huayun.modules.erp.qc.dto.QcProcessTemplateQuery;
import com.huayun.modules.erp.qc.entity.QcTemplate;
import com.huayun.modules.erp.qc.entity.model.QcMaterialModel;
import com.huayun.modules.erp.qc.form.*;
import com.huayun.modules.erp.qc.query.QcMaterialQuery;
import com.huayun.modules.erp.qc.query.QcTemplateQuery;
import com.huayun.modules.erp.qc.vo.QcMaterialProcessTemplateVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IQcTemplateService {

    /**
     * 质检模板初始化
     *
     * @param userId
     * @param tenantId
     * @return void
     * <AUTHOR>
     * @date 2023/11/30 11:25
     */
    @Transactional(rollbackFor = Exception.class)
    void init(String userId, String tenantId);

    /**
     * 分页查询
     *
     * @param query
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.huayun.modules.erp.qc.entity.QcTemplate>
     * <AUTHOR>
     * @date 2023/10/20 16:23
     */
    IPage<QcTemplate> page(QcTemplateQuery query);

    /**
     * 分页查询物料
     *
     * @param query
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.huayun.modules.erp.qc.entity.model.QcMaterialModel>
     * <AUTHOR>
     * @date 2023/10/24 10:38
     */
    IPage<QcMaterialModel> materialPage(QcMaterialQuery query);

    /**
     * 详情
     *
     * @param id
     * @return com.huayun.modules.erp.qc.entity.QcTemplate
     * <AUTHOR>
     * @date 2023/10/20 17:11
     */
    QcTemplate queryById(String id);

    /**
     * 根据id查询
     *
     * @param id
     * @return com.huayun.modules.erp.qc.entity.QcTemplate
     * <AUTHOR>
     * @date 2023/11/30 18:31
     */
    QcTemplate getById(String id);

    /**
     * 列表查询
     *
     * @param ids
     * @return java.util.List<com.huayun.modules.erp.qc.entity.QcTemplate>
     * <AUTHOR>
     * @date 2023/11/30 14:49
     */
    List<QcTemplate> listByIds(Collection<String> ids);

    /**
     * 根据编码查询
     *
     * @param templateCodes
     * @return java.util.List<com.huayun.modules.erp.qc.entity.QcTemplate>
     * <AUTHOR>
     * @date 2023/11/30 15:13
     */
    List<QcTemplate> listByCodes(Collection<String> templateCodes);

    /**
     * 新增
     *
     * @param form
     * @return com.huayun.modules.erp.qc.entity.QcTemplate
     * <AUTHOR>
     * @date 2023/10/20 17:31
     */
    @Transactional(rollbackFor = Exception.class)
    QcTemplate create(QcTemplateForm form);

    /**
     * 修改
     *
     * @param id
     * @param form
     * @return com.huayun.modules.erp.qc.entity.QcTemplate
     * <AUTHOR>
     * @date 2023/10/23 10:04
     */
    @Transactional(rollbackFor = Exception.class)
    QcTemplate change(String id, QcTemplateForm form);

    /**
     * 物料配置模板
     *
     * @param form
     * @return void
     * <AUTHOR>
     * @date 2023/10/24 11:37
     */
    @Transactional(rollbackFor = Exception.class)
    void config(QcTemplateConfigForm form);

    /**
     * @param query
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.huayun.modules.erp.qc.vo.QcMaterialProcessTemplateVo>
     * <AUTHOR>
     * @description 分页查询工序模板配置
     * @date 2024/12/5 16:29
     */
    IPage<QcMaterialProcessTemplateVO> processTemplatePage(QcMaterialQuery query);

    /**
     * @param form
     * @return void
     * <AUTHOR>
     * @description 工序模板配置
     * @date 2024/12/5 19:52
     */
    void processTemplateConfig(QcProcessTemplateConfigForm form);


    /**
     * @param processId
     * @param materialId
     * @return com.huayun.modules.erp.qc.entity.QcTemplate
     * <AUTHOR>
     * @description 根据 工序id 和 物料id 查询 工序质检模板
     * @date 2024/12/6 16:24
     */
    QcTemplate getProcessTemplateById(String processId, String materialId);


    /**
     * @param qcUpdateProcessTemplateFormList
     * @return void
     * <AUTHOR>
     * @description 批量修改工序质检模板的 工序编码和名称
     * @date 2024/12/9 10:30
     */
    void updateProcessTemplateByForm(List<QcUpdateProcessTemplateForm> qcUpdateProcessTemplateFormList);


    /**
     *
     * <AUTHOR>
     * @description 根据产品id和工序id查询工序质检模板
     * @date 2025/2/13 14:17
     * @param query
     * @return java.util.List<com.huayun.modules.erp.qc.dto.QcProcessTemplateDTO>
     */
    List<QcProcessTemplateDTO> selectByProcessIdAndProductId(QcProcessTemplateQuery query);

    /**
     *
     * <AUTHOR>
     * @description 新增工序质检配置模板
     * @date 2025/2/17 14:11
     * @param forms,initialFlag
     * @return void
     */
    void addProcessTemplate(List<QcProcessTemplateForm> forms,Boolean initialFlag);

    /**
     *
     * <AUTHOR>
     * @description 初始化同步所有原质检模板
     * @date 2025/3/20 18:07
     * @param
     * @return void
     */
    void refreshQcTemplate();
}
