package com.huayun.modules.erp.oams.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huayun.modules.common.approval.ApprovalHandler;
import com.huayun.modules.common.approval.controller.vo.ApprovalVO;
import com.huayun.modules.erp.oams.constant.enums.CollectionStateEnum;
import com.huayun.modules.erp.oams.controller.form.CollectionForm;
import com.huayun.modules.erp.oams.controller.form.ExpenseForm;
import com.huayun.modules.erp.oams.controller.form.InvoiceForm;
import com.huayun.modules.erp.oams.controller.query.CollectionQuery;
import com.huayun.modules.erp.oams.controller.query.SettlementOverviewQuery;
import com.huayun.modules.erp.oams.controller.vo.ApprovalVoucherVO;
import com.huayun.modules.erp.oams.controller.vo.CollectionVO;
import com.huayun.modules.erp.oams.entity.AccountsReceivables;
import com.huayun.modules.erp.oams.entity.Collection;
import com.huayun.modules.erp.oams.entity.Expense;
import com.huayun.modules.erp.oams.entity.Transaction;
import com.huayun.modules.erp.oams.vo.VoucherVO;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TODO description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/12
 */
public interface ICollectionService extends ApprovalHandler {

    /**
     * 根据筛选条件分页获取
     *
     * @param page    分页信息
     * @param wrapper 筛选条件
     * @return 分页数据
     */
    IPage<Collection> getPage(IPage<Collection> page, Wrapper<Collection> wrapper);

    /**
     * 根据筛选条件获取数据
     *
     * @param wrapper 筛选条件
     * @return 满足筛选条件的数据
     */
    List<Collection> getList(Wrapper<Collection> wrapper);

    /**
     * 创建
     *
     * @param collection 收款单
     * @return 收款单
     */
    @Transactional(rollbackFor = Exception.class)
    Collection create(Collection collection);

    /**
     * 收款
     *
     * @param collectionId 收款单id
     * @return 收款单
     */
    @Transactional(rollbackFor = Exception.class)
    Collection collectById(String collectionId, CollectionForm form);

    /**
     * 根据id获取
     *
     * @param collectionId 收款单id
     * @return 收款单
     */
    Collection getById(String collectionId);

    /**
     * 通过ID获取订单列表
     *
     * @param collectionIds
     * @return java.util.List<com.huayun.modules.erp.oams.entity.Collection>
     * <AUTHOR>
     * @date 2023/2/28 20:04
     */
    List<Collection> getByIds(java.util.Collection<String> collectionIds);

	/**
	 * 通过ID获取订单列表
	 *
	 * @param expenseIds
	 * @return java.util.List<com.huayun.modules.erp.oams.entity.Collection>
	 * <AUTHOR>
	 * @date 2023/2/28 20:04
	 */
	List<CollectionVO> getByExpenseIds(java.util.Collection<String> expenseIds);


    /**
     * 根绝id删除
     *
     * @param collectionId 收款单id
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteById(String collectionId);

    /**
     * 统计供应商-结算方式维度下的未收总金额，本月应收金额，本月已收金额
     * @param date
     * @return
     */
    List<AccountsReceivables> countOverview(Date date);

    /**
     * 统计供应商-结算方式维度下的未收总金额，本月应收金额，本月已收金额
     * @param
     * @return
     */
    List<AccountsReceivables> countOverview(SettlementOverviewQuery query);



    /**
     * 统计供应商-结算方式维度下各个月份的未收金额
     * @param date
     * @return
     */
    List<AccountsReceivables> countMonthlyUnpaid(Date date);


	/**
	 * 交易流水生成凭证
	 * @param transactions
	 * @return
	 */
	VoucherVO transactionCreateVoucher(List<Transaction> transactions);

	/**
	 * 根据对账单获取订单金额
	 * @param statementId
	 * @return
	 */
	Map<String, BigDecimal> getStatementDetailSubmitAmount(String statementId);

	/**
     * 从收款单生成凭证
     * @param resourceId
     * @return
     */
    VoucherVO createVoucherFromCollection(String resourceId);

    /**
	 * cancelled:取消收款.
	 * 执行取消收款操作后，单据流转到已取消状态，申请金额回退。回退到订单的未付金额里；.
	 *
	 * <AUTHOR>
	 * @param collectionId
	 * @return
	 * @since version 1.0
	 */
	Collection cancelled(String collectionId);

	/**
	 * collectionsSum:收款单汇总.
	 *
	 * <AUTHOR>
	 * @param query
	 * @return
	 * @since version 1.0
	 */
	CollectionVO collectionsSum(CollectionQuery query);

	IPage<ApprovalVoucherVO> setVouchers(IPage<ApprovalVO> result);

	/**
	 * getPageTransactions:获取带明细的分页数据.   
	 *  
	 * <AUTHOR>  
	 * @param page
	 * @param wrapper
	 * @return  
	 * @since version 1.0
	 */
	IPage<CollectionVO> getPageTransactions(IPage<Collection> page, Wrapper<Collection> wrapper);

	/**
	 * 汇总明细的应收金额和实收金额
	 * @param wrapper
	 * @return
	 */
	CollectionVO getCountTransactions(LambdaQueryWrapper<Collection> wrapper);

	/**
	 * 费用转为收款单
	 * @param expenseForm
	 * @param expense
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
    Collection expenseCreateCollection(ExpenseForm expenseForm, Expense expense);

	@Transactional(rollbackFor = Exception.class)
	Collection expenseUpdateCollection(ExpenseForm expenseForm, Expense expense,Collection collection);

	/**
	 * 其他费用审批生成凭证
	 * @param resourceId
	 * @return
	 */
	VoucherVO createVoucherFromExpense(String resourceId);

	void collectionRejected(String collectionId);

	/**
	 * 更新收款单状态
	 * @param collectionId
	 * @param state
	 * @return
	 */
	Collection updateState(String collectionId, CollectionStateEnum state);

	/**
	 * 收款单转开票单
	 * @param collectionVO
	 * @return
	 */
	InvoiceForm collectionCovertInvoiceForm(CollectionVO collectionVO);

	/**
	 * 收款单作废
	 * @param collectionId
	 * @return
	 */
	Collection invalidById(String collectionId);

	/**
	 * 通过费用id获取收款单
	 * @param id
	 * @return
	 */
    Collection getByExpenseId(String id);

	/**
	 * 更新收款单信息
	 * @param collection
	 * @return
	 */
	Collection updateById(Collection collection);

    CollectionVO createProject(Collection collection);
}
