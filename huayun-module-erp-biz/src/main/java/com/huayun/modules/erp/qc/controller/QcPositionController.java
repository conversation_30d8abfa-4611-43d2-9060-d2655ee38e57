package com.huayun.modules.erp.qc.controller;

import com.huayun.modules.erp.qc.service.IQcPositionService;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class QcPositionController implements IQcPositionController {

    @Resource
    private IQcPositionService qcPositionService;

    /**
     * 根据筛选条件分页获取质检岗位
     *
     * @param query 筛选条件
     */
    @Override
    public Result<List<QcPositionDTO>> requestGet(PositionQuery query) {
        return Result.ok(qcPositionService.get(query).stream().map(QcPositionDTO::new).collect(Collectors.toList()));
    }

    /**
     * 根据id获取质检岗位
     *
     * @param id 质检岗位id
     */
    @Override
    public Result<QcPositionDTO> requestGet(String id) {
        QcPositionDTO dto = new QcPositionDTO(qcPositionService.get(id));
        return Result.ok(dto);
    }

    /**
     * 创建质检岗位
     *
     * @param form 质检岗位信息
     */
    @Override
    public Result<QcPositionDTO> requestCreate(PositionForm form) {
        QcPositionDTO dto = new QcPositionDTO(qcPositionService.create(form));
        return Result.ok(dto);
    }

    /**
     * 修改质检岗位
     *
     * @param id   质检岗位id
     * @param form 质检岗位信息
     */
    @Override
    public Result<QcPositionDTO> requestUpdate(String id, PositionForm form) {
        QcPositionDTO dto = new QcPositionDTO(qcPositionService.update(id, form));
        return Result.ok(dto);
    }

    /**
     * 删除质检岗位
     *
     * @param id 质检岗位id
     */
    @Override
    public Result<QcPositionDTO> requestDelete(String id) {
        qcPositionService.delete(id);
        return Result.ok();
    }
}
