package com.huayun.modules.erp.wms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
* 来料退货物料标签
*
* <AUTHOR> am I?
* @date   2022/10/08
*/
@Data
@TableName("wms_return_item_lot")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ReturnItemLot extends BaseTenantEntity {
    
	/**退货单据id*/
	private String returnId;

	/**退货物料清单id*/
	private String itemId;

	/**物料id*/
	private String materialId;

	/**标签号*/
	private String lotNo;

	/**供应商id*/
	private String supplierId;

	/**供应商名称*/
	private String supplierName;

	/**单价*/
	private BigDecimal unitPrice;

	/**数量*/
	private BigDecimal quantity;
	
	// ========== 查询值 ==========
	/**物料属性*/
	private transient String property;


	/**
	 * 成本金额
	 * @return
	 */
	public BigDecimal costAmount() {
		if (unitPrice == null || quantity == null) {
			return BigDecimal.ZERO;
		}
		return unitPrice.multiply(quantity);
	}
}
