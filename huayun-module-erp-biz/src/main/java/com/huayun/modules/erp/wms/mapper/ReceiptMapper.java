package com.huayun.modules.erp.wms.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huayun.modules.erp.wms.constant.enums.receipt.ReceiptTypeEnum;
import com.huayun.modules.erp.wms.dto.WarehouseVisualItemDTO;
import com.huayun.modules.erp.wms.entity.Receipt;
import com.huayun.modules.erp.wms.entity.ReceiptItem;
import com.huayun.modules.erp.wms.entity.model.DashBoardModel;
import com.huayun.modules.erp.wms.entity.model.PurchaseOrderReceipt;
import com.huayun.modules.erp.wms.entity.model.ReceiptModel;
import com.huayun.modules.erp.wms.entity.model.ReceiptSum;
import com.huayun.modules.erp.wms.query.ReceiptQuery;
import com.huayun.modules.erp.wms.query.ReceiptSumQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 入库单据管理
 *
 * <AUTHOR> am I?
 * @date 2022/09/08
 */
public interface ReceiptMapper extends BaseMapper<Receipt> {

    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:34
     * @param page
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    Page<ReceiptModel> pageList(Page<Receipt> page,
                                @Param(Constants.WRAPPER) QueryWrapper<Receipt> queryWrapper,
                                @Param("isSelectMaterial") Boolean isSelectMaterial,
                                @Param("categoryId") String categoryId,
                                @Param("categoryName") String categoryName);


    /**
     * 查询list导出
     * @param queryWrapper
     * @return
     */
    List<ReceiptModel> getList(@Param(Constants.WRAPPER) QueryWrapper<Receipt> queryWrapper);



    /**
     * 入库单据分页查询  委外
     *
     * <AUTHOR>
     * @date 2022/9/9 14:34
     * @param page
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    Page<ReceiptModel> pageListOutsourcing(Page<Receipt> page,
                                @Param(Constants.WRAPPER) QueryWrapper<Receipt> queryWrapper,
                                @Param("isSelectMaterial") Boolean isSelectMaterial);


    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:34
     * @param page
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    Page<ReceiptModel> pageListQuery(Page<Receipt> page,
                                @Param(Constants.WRAPPER) QueryWrapper<Receipt> queryWrapper,
                                @Param("isSelectMaterial") Boolean isSelectMaterial);


    /**
     * 入库单据分页查询
     *
     * <AUTHOR>
     * @date 2022/9/9 14:34
     * @param queryWrapper
     * @param isSelectMaterial
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.huayun.modules.erp.wms.entity.model.ReceiptModel>
     */
    List<ReceiptModel> exportList(@Param(Constants.WRAPPER) QueryWrapper<Receipt> queryWrapper,
                                     @Param("isSelectMaterial") Boolean isSelectMaterial);


    /**
     * 根据id修改单据
     *
     * <AUTHOR>
     * @date 2023/9/12 17:22
     * @param receipt
     * @return void
     */
    void updateReceiptById(Receipt receipt);

    /**
     * 获取仓储可视化信息
     *
     * @param wrapper
     * @param sumFiled
     * @return java.util.List<com.huayun.modules.erp.wms.dto.WarehouseVisualDTO>
     * <AUTHOR>
     * @date 2023/2/20 17:06
     */
    WarehouseVisualItemDTO findWarehouseVisualReceipts(@Param(Constants.WRAPPER) QueryWrapper<Receipt> wrapper,
                                                       @Param("sumFiled") String sumFiled);

    /**
     * 查询库存面板入库情况
     *
     * @param receiptType 入库类型
     * @return com.huayun.modules.erp.wms.entity.model.DashBoardModel.ShortcutMenu
     * <AUTHOR>
     * @date 2023/6/14 10:21
     */
    DashBoardModel.ShortcutMenu getShotcutMenuInfo(@Param("receiptType") String receiptType);


    /**
     * 查询库存面板入库情况
     *
     * @param receiptType 入库类型
     * @return com.huayun.modules.erp.wms.entity.model.DashBoardModel.ShortcutMenu
     * <AUTHOR>
     * @date 2023/6/14 10:21
     */
    DashBoardModel.ShortcutMenu selectShotcutMenuInfo(@Param("receiptType") String receiptType);
    //    <select id="sumReceipt" resultType="com.huayun.modules.erp.wms.entity.model.ReceiptSum">
    /**
     * sumReceipt:按订单统计入库单.
     *
     * <AUTHOR>
     * @param receiptSumQuery
     * @return
     * @since version 1.0
     */
    List<ReceiptSum> sumOrderReceipt(@Param("query") ReceiptSumQuery receiptSumQuery);

    /**
     * purchaseOrderReceipt:采购入库单查询.
     * Date:2024年5月8日下午2:36:20
     * <AUTHOR>
     * @param page
     * @param receiptQuery
     * @return
     * @since version 1.0
     */
    Page<PurchaseOrderReceipt> purchaseOrderReceipt(Page<PurchaseOrderReceipt> page, @Param("query") ReceiptQuery receiptQuery);

    /**
     * outsourcingOrderReceipt:委外入库单查询.
     * Date:2024年5月8日下午2:36:20
     * <AUTHOR>
     * @param page
     * @param receiptQuery
     * @return
     * @since version 1.0
     */
    Page<PurchaseOrderReceipt> outsourcingOrderReceipt(Page<PurchaseOrderReceipt> page, @Param("query") ReceiptQuery receiptQuery);


    void deleteByIds(@Param("ids") List<String> ids);


    ReceiptItem sumStorageCount(@Param("type") ReceiptTypeEnum type, @Param("beginDate") Date beginDate, @Param("endDate") Date endDate);


    default List<Receipt> selectReceiptList(List<String> relationCodes, ReceiptTypeEnum type) {
        if (ObjectUtil.isAllNotEmpty(relationCodes, type)) {
            LambdaQueryWrapper<Receipt> receiptLambdaQueryWrapper = new LambdaQueryWrapper<>();
            receiptLambdaQueryWrapper.in(Receipt::getRelationCode, relationCodes);
            receiptLambdaQueryWrapper.eq(Receipt::getType, type);
            return this.selectList(receiptLambdaQueryWrapper);
        }
        return Collections.emptyList();
    }
}
