package com.huayun.modules.erp.oams.entity;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;

import org.apache.commons.beanutils.BeanUtilsBean2;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.annotation.Excel;

import com.baomidou.mybatisplus.annotation.TableName;
import com.huayun.modules.common.entity.BaseTenantEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 资金实绩表
 * @Author: jeecg-boot
 * @Date: 2024-07-15
 * @Version: V1.0
 */
//@Data
@TableName("oams_es_amount_actual")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "oams_es_amount_actual对象", description = "资金实绩表")
public class EsAmountActual extends BaseTenantEntity implements Serializable {
	/** 项目 */
	@Excel(name = "项目", width = 15)
	@ApiModelProperty(value = "项目")
	private java.lang.String project;
	/** 类别 */
	@Excel(name = "类别", width = 15)
	@ApiModelProperty(value = "类别")
	private java.lang.String category;
	/** 科目 */
	@Excel(name = "科目", width = 15)
	@ApiModelProperty(value = "科目")
	private java.lang.String account;
	/** 部门 */
	@Excel(name = "部门", width = 15)
	@ApiModelProperty(value = "部门")
	private java.lang.String dept;
    /**
     * 组织类型id
     */
    @ApiModelProperty(value = "组织类型id")
    private String typeId;
	/** 部门 巴 */
	@Excel(name = "部门 巴", width = 15)
	@ApiModelProperty(value = "部门 巴")
	private java.lang.String deptSun;
	/** 年份 */
	@Excel(name = "年份", width = 15)
	@ApiModelProperty(value = "年份")
	private java.lang.Integer year;
	/** 经营科目编码 */
	@Excel(name = "经营科目编码", width = 15)
	@ApiModelProperty(value = "经营科目编码")
	private java.lang.String accountCode;
	/** 会计科目编码 */
	@Excel(name = "会计科目编码", width = 15)
	@ApiModelProperty(value = "会计科目编码")
	private java.lang.String ledgerCode;
	/** 模板类型 */
	@Excel(name = "模板类型", width = 15)
	@ApiModelProperty(value = "模板类型")
	private java.lang.Integer templateType;
	/** 月份 */
	@Excel(name = "月份", width = 15)
	@ApiModelProperty(value = "月份")
	private java.lang.Integer month;
	/** 1号 */
	@Excel(name = "1号", width = 15)
	@ApiModelProperty(value = "1号")
	private java.math.BigDecimal day1;
	/** 2号 */
	@Excel(name = "2号", width = 15)
	@ApiModelProperty(value = "2号")
	private java.math.BigDecimal day2;
	/** 3号 */
	@Excel(name = "3号", width = 15)
	@ApiModelProperty(value = "3号")
	private java.math.BigDecimal day3;
	/** 4号 */
	@Excel(name = "4号", width = 15)
	@ApiModelProperty(value = "4号")
	private java.math.BigDecimal day4;
	/** 5号 */
	@Excel(name = "5号", width = 15)
	@ApiModelProperty(value = "5号")
	private java.math.BigDecimal day5;
	/** 6号 */
	@Excel(name = "6号", width = 15)
	@ApiModelProperty(value = "6号")
	private java.math.BigDecimal day6;
	/** 7号 */
	@Excel(name = "7号", width = 15)
	@ApiModelProperty(value = "7号")
	private java.math.BigDecimal day7;
	/** 8号 */
	@Excel(name = "8号", width = 15)
	@ApiModelProperty(value = "8号")
	private java.math.BigDecimal day8;
	/** 9号 */
	@Excel(name = "9号", width = 15)
	@ApiModelProperty(value = "9号")
	private java.math.BigDecimal day9;
	/** 10号 */
	@Excel(name = "10号", width = 15)
	@ApiModelProperty(value = "10号")
	private java.math.BigDecimal day10;
	/** 11号 */
	@Excel(name = "11号", width = 15)
	@ApiModelProperty(value = "11号")
	private java.math.BigDecimal day11;
	/** 12号 */
	@Excel(name = "12号", width = 15)
	@ApiModelProperty(value = "12号")
	private java.math.BigDecimal day12;
	/** 13号 */
	@Excel(name = "13号", width = 15)
	@ApiModelProperty(value = "13号")
	private java.math.BigDecimal day13;
	/** 14号 */
	@Excel(name = "14号", width = 15)
	@ApiModelProperty(value = "14号")
	private java.math.BigDecimal day14;
	/** 15号 */
	@Excel(name = "15号", width = 15)
	@ApiModelProperty(value = "15号")
	private java.math.BigDecimal day15;
	/** 16号 */
	@Excel(name = "16号", width = 15)
	@ApiModelProperty(value = "16号")
	private java.math.BigDecimal day16;
	/** 17号 */
	@Excel(name = "17号", width = 15)
	@ApiModelProperty(value = "17号")
	private java.math.BigDecimal day17;
	/** 18号 */
	@Excel(name = "18号", width = 15)
	@ApiModelProperty(value = "18号")
	private java.math.BigDecimal day18;
	/** 19号 */
	@Excel(name = "19号", width = 15)
	@ApiModelProperty(value = "19号")
	private java.math.BigDecimal day19;
	/** 20号 */
	@Excel(name = "20号", width = 15)
	@ApiModelProperty(value = "20号")
	private java.math.BigDecimal day20;
	/** 21号 */
	@Excel(name = "21号", width = 15)
	@ApiModelProperty(value = "21号")
	private java.math.BigDecimal day21;
	/** 22号 */
	@Excel(name = "22号", width = 15)
	@ApiModelProperty(value = "22号")
	private java.math.BigDecimal day22;
	/** 23号 */
	@Excel(name = "23号", width = 15)
	@ApiModelProperty(value = "23号")
	private java.math.BigDecimal day23;
	/** 24号 */
	@Excel(name = "24号", width = 15)
	@ApiModelProperty(value = "24号")
	private java.math.BigDecimal day24;
	/** 25号 */
	@Excel(name = "25号", width = 15)
	@ApiModelProperty(value = "25号")
	private java.math.BigDecimal day25;
	/** 26号 */
	@Excel(name = "26号", width = 15)
	@ApiModelProperty(value = "26号")
	private java.math.BigDecimal day26;
	/** 27号 */
	@Excel(name = "27号", width = 15)
	@ApiModelProperty(value = "27号")
	private java.math.BigDecimal day27;
	/** 28号 */
	@Excel(name = "28号", width = 15)
	@ApiModelProperty(value = "28号")
	private java.math.BigDecimal day28;
	/** 29号 */
	@Excel(name = "29号", width = 15)
	@ApiModelProperty(value = "29号")
	private java.math.BigDecimal day29;
	/** 30号 */
	@Excel(name = "30号", width = 15)
	@ApiModelProperty(value = "30号")
	private java.math.BigDecimal day30;
	/** 31号 */
	@Excel(name = "31号", width = 15)
	@ApiModelProperty(value = "31号")
	private java.math.BigDecimal day31;

	@ApiModelProperty(value = "是否按预定值均摊")
	private Boolean avg;

	@ApiModelProperty(value = "备注")
	private java.lang.String remark;

	@ApiModelProperty(value = "模板类型 来源")
	private java.lang.Integer templateSource;

    @ApiModelProperty(value = "计算公式 来源 和templateSource配合使用")
	private java.lang.String cronSource;

    @ApiModelProperty(value = "父级科目")
	private java.lang.String parentCode;

	public void init() {
		day1 = BigDecimal.ZERO;
		day2 = BigDecimal.ZERO;
		day3 = BigDecimal.ZERO;
		day4 = BigDecimal.ZERO;
		day5 = BigDecimal.ZERO;
		day6 = BigDecimal.ZERO;
		day7 = BigDecimal.ZERO;
		day8 = BigDecimal.ZERO;
		day9 = BigDecimal.ZERO;
		day10 = BigDecimal.ZERO;
		day11 = BigDecimal.ZERO;
		day12 = BigDecimal.ZERO;
		day13 = BigDecimal.ZERO;
		day14 = BigDecimal.ZERO;
		day15 = BigDecimal.ZERO;
		day16 = BigDecimal.ZERO;
		day18 = BigDecimal.ZERO;
		day19 = BigDecimal.ZERO;
		day20 = BigDecimal.ZERO;
		day21 = BigDecimal.ZERO;
		day22 = BigDecimal.ZERO;

		day23 = BigDecimal.ZERO;
		day24 = BigDecimal.ZERO;
		day25 = BigDecimal.ZERO;
		day26 = BigDecimal.ZERO;
		day27 = BigDecimal.ZERO;
		day28 = BigDecimal.ZERO;
		day29 = BigDecimal.ZERO;
		day30 = BigDecimal.ZERO;
		day31 = BigDecimal.ZERO;
	}

	public java.lang.String getProject() {
		return project;
	}

	public void setProject(java.lang.String project) {
		this.project = project;
	}

	public java.lang.String getCategory() {
		return category;
	}

	public void setCategory(java.lang.String category) {
		this.category = category;
	}

	public java.lang.String getAccount() {
		return account;
	}

	public void setAccount(java.lang.String account) {
		this.account = account;
	}

	public java.lang.String getDept() {
		return dept;
	}

	public void setDept(java.lang.String dept) {
		this.dept = dept;
	}

	public java.lang.String getDeptSun() {
		return deptSun;
	}

	public void setDeptSun(java.lang.String deptSun) {
		this.deptSun = deptSun;
	}

	public java.lang.Integer getYear() {
		return year;
	}

	public void setYear(java.lang.Integer year) {
		this.year = year;
	}

	public java.lang.String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(java.lang.String accountCode) {
		this.accountCode = accountCode;
	}

	public java.lang.String getLedgerCode() {
		return ledgerCode;
	}

	public void setLedgerCode(java.lang.String ledgerCode) {
		this.ledgerCode = ledgerCode;
	}

	public java.lang.Integer getTemplateType() {
		return templateType;
	}

	public void setTemplateType(java.lang.Integer templateType) {
		this.templateType = templateType;
	}

	public java.lang.Integer getMonth() {
		return month;
	}

	public void setMonth(java.lang.Integer month) {
		this.month = month;
	}

	public java.math.BigDecimal getDay1() {
		return day1;
	}

	public void setDay1(java.math.BigDecimal day1) {
		this.day1 = day1;
	}

	public java.math.BigDecimal getDay2() {
		return day2;
	}

	public void setDay2(java.math.BigDecimal day2) {
		this.day2 = day2;
	}

	public java.math.BigDecimal getDay3() {
		return day3;
	}

	public void setDay3(java.math.BigDecimal day3) {
		this.day3 = day3;
	}

	public java.math.BigDecimal getDay4() {
		return day4;
	}

	public void setDay4(java.math.BigDecimal day4) {
		this.day4 = day4;
	}

	public java.math.BigDecimal getDay5() {
		return day5;
	}

	public void setDay5(java.math.BigDecimal day5) {
		this.day5 = day5;
	}

	public java.math.BigDecimal getDay6() {
		return day6;
	}

	public void setDay6(java.math.BigDecimal day6) {
		this.day6 = day6;
	}

	public java.math.BigDecimal getDay7() {
		return day7;
	}

	public void setDay7(java.math.BigDecimal day7) {
		this.day7 = day7;
	}

	public java.math.BigDecimal getDay8() {
		return day8;
	}

	public void setDay8(java.math.BigDecimal day8) {
		this.day8 = day8;
	}

	public java.math.BigDecimal getDay9() {
		return day9;
	}

	public void setDay9(java.math.BigDecimal day9) {
		this.day9 = day9;
	}

	public java.math.BigDecimal getDay10() {
		return day10;
	}

	public void setDay10(java.math.BigDecimal day10) {
		this.day10 = day10;
	}

	public java.math.BigDecimal getDay11() {
		return day11;
	}

	public void setDay11(java.math.BigDecimal day11) {
		this.day11 = day11;
	}

	public java.math.BigDecimal getDay12() {
		return day12;
	}

	public void setDay12(java.math.BigDecimal day12) {
		this.day12 = day12;
	}

	public java.math.BigDecimal getDay13() {
		return day13;
	}

	public void setDay13(java.math.BigDecimal day13) {
		this.day13 = day13;
	}

	public java.math.BigDecimal getDay14() {
		return day14;
	}

	public void setDay14(java.math.BigDecimal day14) {
		this.day14 = day14;
	}

	public java.math.BigDecimal getDay15() {
		return day15;
	}

	public void setDay15(java.math.BigDecimal day15) {
		this.day15 = day15;
	}

	public java.math.BigDecimal getDay16() {
		return day16;
	}

	public void setDay16(java.math.BigDecimal day16) {
		this.day16 = day16;
	}

	public java.math.BigDecimal getDay17() {
		return day17;
	}

	public void setDay17(java.math.BigDecimal day17) {
		this.day17 = day17;
	}

	public java.math.BigDecimal getDay18() {
		return day18;
	}

	public void setDay18(java.math.BigDecimal day18) {
		this.day18 = day18;
	}

	public java.math.BigDecimal getDay19() {
		return day19;
	}

	public void setDay19(java.math.BigDecimal day19) {
		this.day19 = day19;
	}

	public java.math.BigDecimal getDay20() {
		return day20;
	}

	public void setDay20(java.math.BigDecimal day20) {
		this.day20 = day20;
	}

	public java.math.BigDecimal getDay21() {
		return day21;
	}

	public void setDay21(java.math.BigDecimal day21) {
		this.day21 = day21;
	}

	public java.math.BigDecimal getDay22() {
		return day22;
	}

	public void setDay22(java.math.BigDecimal day22) {
		this.day22 = day22;
	}

	public java.math.BigDecimal getDay23() {
		return day23;
	}

	public void setDay23(java.math.BigDecimal day23) {
		this.day23 = day23;
	}

	public java.math.BigDecimal getDay24() {
		return day24;
	}

	public void setDay24(java.math.BigDecimal day24) {
		this.day24 = day24;
	}

	public java.math.BigDecimal getDay25() {
		return day25;
	}

	public void setDay25(java.math.BigDecimal day25) {
		this.day25 = day25;
	}

	public java.math.BigDecimal getDay26() {
		return day26;
	}

	public void setDay26(java.math.BigDecimal day26) {
		this.day26 = day26;
	}

	public java.math.BigDecimal getDay27() {
		return day27;
	}

	public void setDay27(java.math.BigDecimal day27) {
		this.day27 = day27;
	}

	public java.math.BigDecimal getDay28() {
		return day28;
	}

	public void setDay28(java.math.BigDecimal day28) {
		this.day28 = day28;
	}

	public java.math.BigDecimal getDay29() {
		return day29;
	}

	public void setDay29(java.math.BigDecimal day29) {
		this.day29 = day29;
	}

	public java.math.BigDecimal getDay30() {
		return day30;
	}

	public void setDay30(java.math.BigDecimal day30) {
		this.day30 = day30;
	}

	public java.math.BigDecimal getDay31() {
		return day31;
	}

	public void setDay31(java.math.BigDecimal day31) {
		this.day31 = day31;
	}

	public Boolean getAvg() {
		return avg;
	}

	public void setAvg(Boolean avg) {
		this.avg = avg;
	}

	public void setAmount(int day, BigDecimal amount) {
		try {
			BigDecimal oldValue = BigDecimal.ZERO;
			String value = BeanUtilsBean2.getInstance().getProperty(this, "day"+day);
			if(StringUtils.isNotEmpty(value)) {
				oldValue = new BigDecimal(value);
			}
			oldValue = oldValue.add(amount);
			BeanUtilsBean2.getInstance().setProperty(this, "day"+day, oldValue);
		} catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {

		}
	}

	public java.lang.String getRemark() {
		return remark;
	}

	public void setRemark(java.lang.String remark) {
		this.remark = remark;
	}

	public java.lang.String getCronSource() {
		return cronSource;
	}

	public void setCronSource(java.lang.String cronSource) {
		this.cronSource = cronSource;
	}

	public java.lang.Integer getTemplateSource() {
		return templateSource;
	}

	public void setTemplateSource(java.lang.Integer templateSource) {
		this.templateSource = templateSource;
	}

	public EsAmountActual() {
		super();
	}

	public BigDecimal getDayAmount(int index) {

		// TODO Auto-generated method stub
		try {
			if(BeanUtilsBean2.getInstance().getProperty(this, "day"+index)!=null) {
				return new BigDecimal(BeanUtilsBean2.getInstance().getProperty(this, "day"+index));
			}
		} catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {

		}
		return BigDecimal.ZERO;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

}
