package com.huayun.modules.erp.pdm.vo.bom;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.huayun.modules.common.entity.UploadFile;
import com.huayun.modules.common.mybatis.UploadFileHandler;
import com.huayun.modules.common.vo.BaseVO;
import com.huayun.modules.erp.pdm.constant.enums.BomAndProcessFlowStateEnum;
import com.huayun.modules.erp.pdm.constant.enums.ProcessCategoryEnum;
import com.huayun.modules.erp.pdm.constant.enums.product.ProductStateEnum;
import com.huayun.modules.erp.pdm.constant.enums.product.ProductTypeEnum;
import com.huayun.modules.erp.pdm.service.model.BomProductModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/17 17:52
 **/
@Data
@ApiModel(value = "产品")
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
public class ProductBomVO extends BaseVO {

    /**
     * bomId
     */
    @ApiModelProperty(value = "bomId")
    private String bomId;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    private String productId;
    /**
     * bom号
     */
    @ApiModelProperty(value = "bom号")
    private String bomSn;

    /**
     * bom号
     */
    @ApiModelProperty(value = "产品编码")
    private String productCode;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 类别id
     */
    @ApiModelProperty(value = "类别id")
    private String categoryId;

    /**
     * 类别id
     */
    @ApiModelProperty(value = "类别id")
    private String categoryName;

    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品规格")
    private String specification;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "关联产品ID")
    private String relationProductId;

    @ApiModelProperty(value = "关联产品ID")
    private String relationProductName;

    /**
     * 缩略图路径
     */
    @ApiModelProperty(value = "缩略图路径")
    @TableField(typeHandler = UploadFileHandler.class)
    private UploadFile iconFile;

    /**
     * 缩略图路径
     */
    @ApiModelProperty(value = "缩略图路径")
    @TableField(typeHandler = UploadFileHandler.class)
    private List<UploadFile> iconFiles;

    /**
     * 产品类型，1--成品，2--半成品
     */
    @ApiModelProperty(value = "产品类型")
    @TableField(value = "product_type")
    private ProductTypeEnum productType;

    /**
     * 产品加工方式，自制 OR 委 外
     */
    @ApiModelProperty(value = "产品加工方式，自制 OR 委 外")
    private ProcessCategoryEnum processCategory;

    /**
     * 产品状态
     */
    @ApiModelProperty(value = "产品状态")
    private ProductStateEnum state;

    @ApiModelProperty(value = "bom的状态")
    @TableField(value = "bom_state")
    private BomAndProcessFlowStateEnum bomState;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
	 * 是否为可配置产品
	 */
	@ApiModelProperty(value = "是否为可配置产品")	
	private Boolean configurable;

    /**
     * 图纸号
     */
    @ApiModelProperty(value = "图纸号")
    private String drawingNumber;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;


    public ProductBomVO(BomProductModel bomProductModel) {
        BeanUtil.copyProperties(bomProductModel, this);
    }
}
