package com.huayun.modules.erp.mms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseService;
import com.huayun.modules.erp.mms.entity.EmployeeSalary;
import com.huayun.modules.erp.mms.form.BatchEmployeeSalaryForm;
import com.huayun.modules.erp.mms.form.EmployeeSalaryForm;
import com.huayun.modules.erp.mms.form.EmployeeSalaryTemplateForm;
import com.huayun.modules.erp.mms.query.EmployeeSalaryQuery;
import com.huayun.modules.erp.mms.service.model.salary.DepartmentSalaryStatistics;
import com.huayun.modules.erp.mms.service.model.salary.DynamicRow;
import com.huayun.modules.erp.mms.service.model.salary.EmployeeSalaryModel;
import com.huayun.modules.erp.mms.service.model.salary.EmployeeSalaryPageModel;

import java.util.Collection;
import java.util.List;

/**
 * 员工工资
 *
 * <AUTHOR> am I?
 * @date 2023/08/02
 */
public interface IEmployeeSalaryService extends MPJBaseService<EmployeeSalary> {

    /**
     * 分页查询
     *
     * @param page
     * @param queryWrapper
     */
    /**
     * @param page
     * @param employeeSalaryQuery
     * @return com.huayun.modules.erp.mms.service.model.salary.EmployeeSalaryPageModel
     * <AUTHOR>
     * @date 2023/8/8 18:23
     */
    EmployeeSalaryPageModel getEmployeeSalaryPage(IPage<EmployeeSalaryModel> page, EmployeeSalaryQuery employeeSalaryQuery);


    /**
     * 补发补扣工资分页查询
     *
     * @param wrapper
     * @return java.util.List<com.huayun.modules.erp.mms.entity.EmployeeSalary>
     * <AUTHOR>
     * @date 2023/8/7 15:22
     */
    IPage<EmployeeSalaryModel> getReissueOfDeductedWages(IPage<EmployeeSalaryModel> page, QueryWrapper<EmployeeSalaryQuery> wrapper);


    /**
     * 列表查询
     *
     * @param queryWrapper
     */
    List<EmployeeSalary> getEmployeeSalaryList(QueryWrapper<EmployeeSalary> queryWrapper);

    /**
     * 重新计算个人上个月工资
  *
  * @param employeeId
  * @param yearMonth
  * @return com.huayun.modules.erp.mms.entity.EmployeeSalary
  * <AUTHOR>
  * @date 2023/8/3 11:37
  */
 void recalculatePersonalSalary(String employeeId, String yearMonth);

 /**
  * 批量生产上个月的工资记录
  *
  * <AUTHOR>
  * @date 2023/8/3 11:40
  */
 void generateSalaryItems();


    List<DynamicRow> salaryStatistics(QueryWrapper<EmployeeSalary> wrapper);



    /**
     * @param wrapper
     * @return java.util.List<com.huayun.modules.erp.mms.service.model.salary.DepartmentSalaryStatistics>
     * <AUTHOR>
     * @date 2023/8/8 10:44
     */
    List<DepartmentSalaryStatistics> getDepartmentSalaryStatistics(QueryWrapper<EmployeeSalaryQuery> wrapper);

    /**
     * 根据员工id获取员工工资项配置  非补发补扣获取上月，补发补扣获取当月，用于更新
     * @param employeeId
     * @return
     */
    List<EmployeeSalary> getEmployeeSalaryByEmployeeForUpdate(String employeeId);


    /**
     * 导出工资
     *
     * @param employeeSalaryQuery
     * <AUTHOR>
     * @date 2023/8/8 16:50
     */
    void exportEmployeeSalary(EmployeeSalaryQuery employeeSalaryQuery);

    /**
     * 根据模板查询数量
     *
     * @param templateIds
     * @return long
     * <AUTHOR>
     * @date 2023/8/10 11:18
     */
    long getSalaryCount(List<String> templateIds);

    /**
     * 查询工资和模板信息
     *
     * @param employeeSalaryQuery
     * @return java.util.List<com.huayun.modules.erp.mms.service.model.salary.EmployeeSalaryModel>
     * <AUTHOR>
     * @date 2023/8/11 12:01
     */
    List<EmployeeSalaryModel> getEmployeeSalaryAndTemplate(EmployeeSalaryQuery employeeSalaryQuery);


    /**
     * 修改
     *
     * @param employeeSalaryForm
     * @param employeeSalaryId
     * @return
     */
    EmployeeSalary updateEmployeeSalary(String employeeSalaryId, EmployeeSalaryForm employeeSalaryForm);

    /**
     * 删除工资项
     *
     * @param employeeIds
     * @param yearMonth
     * <AUTHOR>
     * @date 2023/8/7 15:25
     */
    void removeEmployeeSalary(Collection<String> employeeIds, String yearMonth);

    /**
     * 工资项中 工资系数修改时更新员工工资系数
     * @param templateFormList
     * @param employeeId
     */
    void updateByCoefficient(List<EmployeeSalaryTemplateForm> templateFormList, String employeeId);

    /**
     * 批量修改员工工资项
     * @param batchEmployeeSalaryForm
     * @return
     */
    List<EmployeeSalary> updateEmployeeSalary(BatchEmployeeSalaryForm batchEmployeeSalaryForm);
}
