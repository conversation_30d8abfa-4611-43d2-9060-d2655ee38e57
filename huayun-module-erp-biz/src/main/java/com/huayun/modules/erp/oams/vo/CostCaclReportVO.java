package com.huayun.modules.erp.oams.vo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

import com.huayun.modules.common.vo.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ClassName: CostCaclReportVO   
 * Function: 成本分析表.   
 * date: 2023年12月11日 上午10:36:23   
 *  
 * <AUTHOR>  
 * @version   
 * @since version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="产品成本分析表对象", description="成本核算")
public class CostCaclReportVO  extends BaseVO {	
	/**
	 * 成本月统计
	 */
	@ApiModelProperty(value = "成本月统计")
	private List<CostCaclReportDetailVO> months;
	
	@ApiModelProperty(value = "年度占比")
	private CostCaclReportDetailVO year;
	
	@ApiModelProperty(value = "成本月统计 对比")
	private Collection<CostCaclContrastVO> monthsContrast;
	
	@ApiModelProperty(value = "今年总成本")
	private BigDecimal yearAmout;
	
	@ApiModelProperty(value = "当月总成本")
	private BigDecimal monthAmout;
}
