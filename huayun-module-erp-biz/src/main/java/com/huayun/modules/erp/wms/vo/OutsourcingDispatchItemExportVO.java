package com.huayun.modules.erp.wms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 委外发料物料清单
 *
 * <AUTHOR>
 * @date 2023/6/1 21:25
 **/
@Data
@NoArgsConstructor
public class OutsourcingDispatchItemExportVO {

    /**物料编码*/
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**物料名称*/
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**物料规格*/
    @ApiModelProperty(value = "物料规格")
    private String specification;

    /**物料单位*/
    @ApiModelProperty(value = "物料单位")
    private String unit;

    /**实际数量*/
    @ApiModelProperty(value = "实际数量")
    private BigDecimal quantity;
}
