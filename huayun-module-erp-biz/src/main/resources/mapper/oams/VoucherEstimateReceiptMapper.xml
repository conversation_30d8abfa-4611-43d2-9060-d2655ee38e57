<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huayun.modules.erp.oams.mapper.VoucherEstimateReceiptMapper">


	<select id="getReceiptsPage" resultType="com.huayun.modules.erp.oams.vo.ReceiptBalanceVO">
	   SELECT
			sum( IFNULL(t.tax_price, t.sale_price)  * t.receipt_quantity ) amount,
			t.material_id,
			t.material_code,
			t3.`name` as materialName,
			t2.supplier_id,
	        sum(t.receipt_quantity) quantity
		FROM
		    wms_material t3,
			wms_receipt_item t,
			wms_receipt t2,
			( SELECT es.order_id, es.voucher_id, es.ac_id,vou.vdate  FROM oams_voucher_estimate es, oams_voucher vou WHERE es.voucher_id = vou.id
			   and vou.ac_id=#{query.acId}
			    <if test="query.receiptStrat!=null">
				    AND vou.vdate  >= #{query.receiptStrat}
				</if>
				<if test="query.receiptEnd!=null">
				   <![CDATA[ AND vou.vdate  <= #{query.receiptEnd} ]]>
				</if>

			 ) v1
		WHERE
			t.receipt_id = t2.id
			AND t.material_id = t3.id
			AND t2.id = v1.order_id

		<if test="query.supplier!=null and query.supplier!=''">
		    AND  t.supplier_id = #{query.supplier}
		</if>
		<if test="query.materialName!=null and query.materialName!=''">
		    AND t3.`name` like CONCAT(#{query.materialName}, '%')
		</if>
		<if test="query.materialIds != null and query.materialIds.size() > 0">
            AND t.`material_id` IN
            <foreach collection="query.materialIds" item="code" index="index" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="query.suppliers != null and query.suppliers.size() > 0">
            AND t2.supplier_id IN
            <foreach collection="query.suppliers" item="code" index="index" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>

         group by
			t.material_id,
			t.material_code,
			t3.`name`,
			t2.supplier_id

	</select>

    <select id="getInvoicePage" resultType="com.huayun.modules.erp.oams.vo.ReceiptBalanceVO">
       SELECT
			t2.product_id material_id,
			t2.in_tax amount,
			t.vid,
			t2.invoice_quantity AS quantity,
			( CASE WHEN t2.es = TRUE THEN IFNULL(t3.tax_price,t3.sale_price)  * t2.invoice_quantity ELSE 0 END ) amountReceipt,
			t.cid supplier_id,
			t2.es
		FROM
			oams_invoice t,
			oams_invoice_detail t2 ,
			 wms_receipt_item t3
		WHERE
			t.id = t2.invoice_id
			and t2.delivery_item_id = t3.id
			AND (t2.order_no LIKE 'RK%' OR t2.delivery_no LIKE 'RK%')
       	<if test="query.receiptStrat!=null">
		    AND t.idate >= #{query.receiptStrat}
		</if>
		<if test="query.receiptEnd!=null">
		   <![CDATA[ AND t.idate <= #{query.receiptEnd} ]]>
		</if>
		<if test="query.supplier!=null and query.supplier!=''">
		    AND  t.cid = #{query.supplier}
		</if>
        <if test="query.materialIds != null and query.materialIds.size() > 0">
            AND t2.`product_id` IN
            <foreach collection="query.materialIds" item="code" index="index" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="query.suppliers != null and query.suppliers.size() > 0">
            AND t.cid IN
            <foreach collection="query.suppliers" item="code" index="index" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="deliveryBalancePage"
            resultType="com.huayun.modules.erp.oams.vo.DeliveryBalanceVO">
        SELECT
            customer_id,
            customer_name,
            material_id,
            material_code,
            material_name,
            model,
            material_unit,
            customer_material,
            tenant_id,

            IFNULL( SUM( IF( negate, 0, quantity ) ), 0 ) as deliveryAmount,
            IFNULL( SUM( IF( negate, quantity, 0) ), 0 ) as deliveryQuantity,
            IFNULL( SUM( IF( negate, 0, amount) ), 0 ) as carryQuantity,
            IFNULL( SUM( IF( negate, amount, 0) ), 0 ) as carryAmount
        FROM oams_voucher_estimate_log where 1=1 and create_time>='2024-07-01'

        GROUP BY
            customer_id,
            customer_name,
            material_id,
            material_code,
            material_name,
            model,
            material_unit,
            customer_material,
            tenant_id

    </select>

</mapper>