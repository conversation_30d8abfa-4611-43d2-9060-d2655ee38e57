<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huayun.modules.erp.oams.mapper.CostExpMapper">
    <delete id="delMonth" parameterType="com.huayun.modules.erp.oams.entity.BusinessColse">
        delete from oams_cost_exp
             where year = #{query.year} and month = #{query.month}
    </delete>
</mapper>