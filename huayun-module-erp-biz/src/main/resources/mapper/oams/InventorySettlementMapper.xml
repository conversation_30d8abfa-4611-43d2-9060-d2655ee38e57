<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huayun.modules.erp.oams.mapper.InventorySettlementMapper">

    <resultMap id="BaseResultMap" type="com.huayun.modules.erp.oams.entity.InventorySettlement">
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="initialMonetaryBalance" column="initial_monetary_balance" jdbcType="DECIMAL"/>
            <result property="initialQuantityBalance" column="initial_quantity_balance" jdbcType="DECIMAL"/>
            <result property="endingMonetaryBalance" column="ending_monetary_balance" jdbcType="DECIMAL"/>
            <result property="endingQuantityBalance" column="ending_quantity_balance" jdbcType="DECIMAL"/>
            <result property="settlementTime" column="settlement_time" jdbcType="DATE"/>
            <result property="materialProperty" column="material_property" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="materialName" column="material_name" jdbcType="VARCHAR"/>
            <result property="materialSpecification" column="material_specification" jdbcType="VARCHAR"/>
            <result property="materialUnit" column="material_unit" jdbcType="VARCHAR"/>
            <result property="currency" column="currency" jdbcType="VARCHAR"/>
            <result property="exchangeRate" column="exchange_rate" jdbcType="DECIMAL"/>
            <result property="currentInQuantity" column="current_in_quantity" jdbcType="DECIMAL"/>
            <result property="currentInMoney" column="current_in_money" jdbcType="DECIMAL"/>
            <result property="currentOutQuantity" column="current_out_quantity" jdbcType="DECIMAL"/>
            <result property="currentOutMoney" column="current_out_money" jdbcType="DECIMAL"/>
            <result property="imported" column="imported" jdbcType="BIT"/>
            <result property="deleted" column="deleted" jdbcType="BIT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,initial_monetary_balance,initial_quantity_balance,
        ending_monetary_balance,ending_quantity_balance,settlement_time,
        material_property,material_code,material_name,
        material_specification,material_unit,currency,
        exchange_rate,current_in_quantity,current_in_money,
        current_out_quantity,current_out_money,imported,
        deleted,create_by,create_time,
        update_by,update_time,tenant_id
    </sql>
    <select id="findPage" resultType="com.huayun.modules.erp.oams.entity.InventorySettlement">
        SELECT t1.*
        FROM oams_inventory_settlement t1
        LEFT JOIN oams_inventory_settlement t2 ON
            t1.material_code = t2.material_code
                AND t2.settlement_time &lt; t1.settlement_time
                AND t2.settlement_time BETWEEN #{query.settlementTime[0]}  AND #{query.settlementTime[1]}
            ${ew.customSqlSegment}
;
    </select>

    <select id="findList" resultType="com.huayun.modules.erp.oams.entity.InventorySettlement">
        SELECT t.material_code AS material_code,t.material_id AS material_id,
        t.min_settlement_time as settlement_time,
        s.initial_monetary_balance AS initial_monetary_balance,
        s.initial_quantity_balance AS initial_quantity_balance
        FROM (SELECT material_id, material_code, tenant_id, MIN(settlement_time) AS min_settlement_time
        FROM oams_inventory_settlement
        WHERE settlement_time BETWEEN #{query.settlementTime[0]} AND #{query.settlementTime[1]}
        <if test="query.materialCode != null and query.materialCode!=''">
            and material_code = #{query.materialCode}
        </if>
        <if test="query.materialName != null and query.materialName!=''">
            and material_name  like concat('%' #{query.materialName} '%')
        </if>
        <if test="query.materialProperty != null and query.materialProperty.value!=''">
            and material_property = #{query.materialProperty.value}
        </if>
        <if test="query.materialIds != null">
            and material_id IN
            <foreach item="item" collection="query.materialIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY material_code,material_id, tenant_id
        ORDER BY material_code
            ) t
        JOIN oams_inventory_settlement s ON t.material_code = s.material_code
        AND t.min_settlement_time = s.settlement_time
        ORDER BY s.material_code
    </select>


    <select id="findStatisticsList" resultType="com.huayun.modules.erp.oams.entity.InventorySettlement">
        SELECT
        COALESCE(SUM(initial_monetary_balance), 0) AS initial_monetary_balance,
        COALESCE(SUM(initial_quantity_balance), 0) AS initial_quantity_balance
        FROM
        (SELECT t.material_code AS material_code,
        t.min_settlement_time as settlement_time,
        s.initial_monetary_balance AS initial_monetary_balance,
        s.initial_quantity_balance AS initial_quantity_balance
        FROM (SELECT material_code, tenant_id, MIN(settlement_time) AS min_settlement_time
        FROM oams_inventory_settlement
        WHERE settlement_time BETWEEN #{query.settlementTime[0]} AND #{query.settlementTime[1]}
        <if test="query.materialCode != null and query.materialCode!=''">
            and material_code = #{query.materialCode}
        </if>
        <if test="query.materialName != null and query.materialName!=''">
            and material_name = #{query.materialName}
        </if>
        <if test="query.materialProperty != null and query.materialProperty.value!=''">
            and material_property = #{query.materialProperty.value}
        </if>
        <if test="query.materialIds != null">
            and material_id IN
            <foreach item="item" collection="query.materialIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY material_code, tenant_id
        ORDER BY material_code
        ) t
        JOIN oams_inventory_settlement s ON t.material_code = s.material_code
        AND t.min_settlement_time = s.settlement_time
        ORDER BY s.material_code) g
    </select>
</mapper>
