<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huayun.modules.erp.oams.mapper.EsAmountMapper">

    <delete id="delete">
        delete from oams_es_amount where template_type=#{query.type} and year=#{query.year} and dept=#{query.dept}
    </delete>

    <update id="updateLock">
          update oams_es_amount set
          <if test="condition.month==1">
                jan_lock = true
          </if>
          <if test="condition.month==2">
                feb_lock = true
          </if>
          <if test="condition.month==3">
                mar_lock = true
          </if>
          <if test="condition.month==4">
                apr_lock = true
          </if>
          <if test="condition.month==5">
                may_lock = true
          </if>
          <if test="condition.month==6">
                jun_lock = true
          </if>
          <if test="condition.month==7">
                jul_lock = true
          </if>
          <if test="condition.month==8">
                aug_lock = true
          </if>
          <if test="condition.month==9">
                setp_lock = true
          </if>
          <if test="condition.month==10">
                oct_lock = true
          </if>
          <if test="condition.month==11">
                nov_lock = true
          </if>
          <if test="condition.month==12">
                dec_lock = true
          </if>
          where template_type=#{condition.type} and year=#{condition.year}
    </update>

</mapper>