<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huayun.modules.erp.pm.mapper.ProjectMapper">
    <select id="selectPage" resultType="com.huayun.modules.erp.pm.entity.Project">
		SELECT
			t1.id,
			t1.create_by,
			t1.create_time,
			t1.update_time,
			t1.update_by,
			t1.tenant_id,
			t1.deleted,
			t1.`name`,
			t1.`code`,
			t1.remark,
			t1.cid,
			t1.ccode,
			t1.cname,
			t1.head,
			t1.head_name,
			t1.type,
			t1.`status`,
			t1.es_start_time,
			t1.es_end_time,
			t1.rel_start_time,
			t1.rel_end_time,
            t1.category,
			t1.is_public,
			t1.starter,
			t1.vouchers,
			t1.current_stage,
			t1.percent,
			t1.team,
			t1.enable_review
		FROM
			pm_project_team t,
			pm_project t1
		WHERE
			t.p_id = t1.id
			AND t1.is_public = FALSE
		    AND t1.parent_id = '0'
		    AND t1.deleted = false
			AND t.participant = #{query.participant}
        <if test="query.start!=null">
		    AND t1.create_time >= #{query.start}
		</if>
		<if test="query.end!=null">
		    AND t1.create_time &lt;= #{query.end}
		</if>
		<if test="query.code!=null and query.code!=''">
		    AND t1.`code` like CONCAT(#{query.code}, '%')
		</if>
		<if test="query.name!=null and query.name!=''">
		    AND t1.`name` like CONCAT(#{query.name}, '%')
		</if>
		<if test="query.head!=null and query.head!=''">
		    AND t1.`head_name` like CONCAT(#{query.head}, '%')
		</if>
		<if test="query.state!=null and query.state!=''">
		    AND t1.`status` = #{query.state}
		</if>
		 UNION ALL
		SELECT
			t1.id,
			t1.create_by,
			t1.create_time,
			t1.update_time,
			t1.update_by,
			t1.tenant_id,
			t1.deleted,
			t1.`name`,
			t1.`code`,
			t1.remark,
			t1.cid,
			t1.ccode,
			t1.cname,
			t1.head,
			t1.head_name,
			t1.type,
			t1.`status`,
			t1.es_start_time,
			t1.es_end_time,
			t1.rel_start_time,
			t1.rel_end_time,
            t1.category,
			t1.is_public,
			t1.starter,
			t1.vouchers,
			t1.current_stage,
			t1.percent,
			t1.team,
			t1.enable_review
		FROM
			pm_project t1
		WHERE
			1 = 1
			AND t1.is_public = TRUE
			AND t1.parent_id = '0'
			AND t1.deleted = false
	    <if test="query.start!=null">
		    AND t1.create_time >= #{query.start}
		</if>
		<if test="query.end!=null">
		    AND t1.create_time &lt;= #{query.end}
		</if>
		<if test="query.code!=null and query.code!=''">
		    AND t1.`code` like CONCAT(#{query.code}, '%')
		</if>
		<if test="query.name!=null and query.name!=''">
		    AND t1.`name` like CONCAT(#{query.name}, '%')
		</if>
		<if test="query.head!=null and query.head!=''">
		    AND t1.`head_name` like CONCAT(#{query.head}, '%')
		</if>
		<if test="query.state!=null and query.state!=''">
		    AND t1.`status` = #{query.state}
		</if>

		order by create_time desc
    </select>
</mapper>