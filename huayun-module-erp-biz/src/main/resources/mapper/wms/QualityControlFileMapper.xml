<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huayun.modules.erp.wms.mapper.QualityControlFileMapper">

    <delete id="deleteByIds">
        DELETE FROM wms_quality_control_file
        WHERE id IN
        <foreach collection="fileIds" item="id" index="index" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByQcIds">
        DELETE FROM wms_quality_control_file
        WHERE quality_control_id IN
        <foreach collection="qcIds" item="id" index="index" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>
</mapper>